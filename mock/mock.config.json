{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "260c7ef0-5ae5-4053-848b-d2517fbab104", "ruleName": "xqx-关注公众号", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/RegimentUser/isForWkd", "filterId": "4e88b703-7dd8-4a62-a977-122c95d293be", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    'data|1': [0,1],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "268f34d7-c663-459e-a131-24147efe5682", "ruleName": "xqx-加盟商管理-团长详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/regimentDetail", "filterId": "21feee2a-8fae-4932-aa3b-2838dd724362", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ab462c3f-4033-44e0-a08d-481a463ecde4", "ruleName": "xqx-绑定邀请关系", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/bindInviteRelation", "filterId": "32bf5a8b-face-4d63-bbed-d80b996d233f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: 1,\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1bfb249e-aecb-4c69-a342-37668f9a3cb5", "ruleName": "xqx-代理商-获取层级名称", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/RegimentUser/customLevelName", "filterId": "2d4ef038-1be5-45d4-962a-c2c45e9da102", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n      {\n        key: 0,\n        title: '高级寄件会员',\n      },\n      {\n        key: 1,\n        title: '铂金推荐官',\n      },\n      {\n        key: 2,\n        title: '钻石推荐官',\n      },\n    ],\n    msg: 'success',\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f162dfc0-0d55-4134-ad4b-3b83e34ae964", "ruleName": "xqx-分佣管理-有效订单用户列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/latestValidOrderUserList", "filterId": "999b729f-d3e3-42fb-983d-3232b54c2e2b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n        {\n            \"nickname\":\"cs\",\n            \"avatar\":\"\",\n            \"time\":\"2023-08-20 10:00:00\"\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "35832f8d-3954-4b26-9450-86499a54b71e", "ruleName": "xqx-分佣管理-我的团员升级团长列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/CustomInviteStatistic/myMemberUpgradeRegimentList", "filterId": "6da503f0-0ed8-4b42-97d1-1ce7c9e7892b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n      {\n        \"nickname\":\"@cname\",\n        \"avatar\":\"\",\n        \"time\":\"2023-08-20 10:00:00\"\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ec56b51e-f4ac-4a86-bbca-275a56aa4499", "ruleName": "xqx-分佣管理-获取有效用户", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/CustomInviteStatistic/latestValidUserList", "filterId": "a57a793f-8536-4eae-8786-f505ac90c635", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n      {\n        \"nickname\":\"@cname\",\n        \"avatar\":\"\",\n        \"time\":\"2023-08-20 10:00:00\",\n        \"mobile\": '18877772222'\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "fd08bc33-67d5-4539-a59d-9d1a11904332", "ruleName": "xqx-分佣管理-用户详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/CustomInviteStatistic/myselfLevelDetail", "filterId": "83292b4d-ceea-4dea-8fa5-da50bdfd7760", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":  {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n      \"level\":\"0\",\n      \"order_count\":\"121\",\n      \"user_count\":\"10\",\n      \"profit\":\"0.00\",\n      \"invite_count\":\"0\",\n      \"valid_user_count\":\"0\",\n      \"inviter\":\"这是邀请人\",\n      \"today_order_count\":\"0\",\n      \"today_profit\":\"19.00\",\n      \"invite_order_count\":\"5\",\n      \"today_invite_order_count\":\"8\",\n    },\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d0fb0a5e-3748-492c-a782-c166139f58d3", "ruleName": "xqx-分佣管理-升级", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/userUpgrade", "filterId": "32de91ff-5487-4803-8984-604add06e792", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":true\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5fe60207-f7c7-4886-846b-a316a571abf3", "ruleName": "xqx-发票-按月开票列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/Invoice/getInvoiceOrdersMonth", "filterId": "29128ec2-8246-4a00-bbc2-3d4fe0b46e29", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n      {\n        times: '2023-07',\n        \"cnt\":\"14\",\n        pay_price: '@float(1,10000,2,2)',\n      },\n      {\n       times: '2023-06',\n        \"cnt\":\"24\",\n        pay_price: '@float(1,10000,2,2)',\n      },\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "37379a37-8e48-4bed-9323-97f8dd637d9d", "ruleName": "xqx-加盟商推广-推广团长", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/LeaguePromotionSpecialist/regimentLists", "filterId": "cdd273c8-ca93-486c-896b-d8151dd32ec6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"total\":5,\n        \"page\":1,\n        \"pageSize\":20,\n        \"list\":[\n            {\n                \"id\":\"292\",\n                \"regiment_id\":\"153\",\n                \"mobile\":\"13524777545\",\n                \"avatar_url\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n                \"create_time\":\"2023-02-27 16:02:41\",\n                \"nickname\":\"全寄\"\n            },\n            {\n                \"id\":\"291\",\n                \"regiment_id\":\"152\",\n                \"mobile\":\"18512127789\",\n                \"avatar_url\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n                \"create_time\":\"2023-02-27 14:23:15\",\n                \"nickname\":\"全寄\"\n            },\n            {\n                \"id\":\"289\",\n                \"regiment_id\":\"151\",\n                \"mobile\":\"18217571353\",\n                \"avatar_url\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n                \"create_time\":\"2023-02-27 11:36:04\",\n                \"nickname\":\"全寄\"\n            },\n            {\n                \"id\":\"277\",\n                \"regiment_id\":\"56\",\n                \"mobile\":\"18674184711\",\n                \"avatar_url\":\"https://upload.kuaidihelp.com//package_pics/2023/03/15/626347896433243064115fb574afb9283141262.jpg\",\n                \"create_time\":\"2023-02-08 17:57:41\",\n                \"nickname\":\"测试温某某\"\n            },\n            {\n                \"id\":\"271\",\n                \"regiment_id\":\"149\",\n                \"mobile\":\"13918071380\",\n                \"avatar_url\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n                \"create_time\":\"2023-02-07 09:59:57\",\n                \"nickname\":\"全寄\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f5d3ace4-3506-4669-af2b-3eebab460f96", "ruleName": "xqx-加盟商推广-有效订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/LeaguePromotionSpecialist/orderLists", "filterId": "dcc2c17b-8729-486e-a67c-b4e11e7249ab", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"total\":2,\n        \"page\":1,\n        \"pageSize\":20,\n        \"list\":[\n            {\n                \"id\":\"808025024303849\",\n                \"brand\":\"dp\",\n                \"waybill_no\":\"DPK6566315313607\",\n                \"shipper_name\":\"白色\",\n                \"shipper_province\":\"安徽省\",\n                \"shipper_city\":\"蚌埠市\",\n                \"shipping_name\":\"季老师\",\n                \"shipping_province\":\"安徽省\",\n                \"shipping_city\":\"铜陵市\",\n                \"create_at\":\"2023-08-02 13:57:23\"\n            },\n            {\n                \"id\":\"807205709009755\",\n                \"brand\":\"dp\",\n                \"waybill_no\":\"DPK6400054807967\",\n                \"shipper_name\":\"考虑咯\",\n                \"shipper_province\":\"安徽省\",\n                \"shipper_city\":\"铜陵市\",\n                \"shipping_name\":\"季老师\",\n                \"shipping_province\":\"安徽省\",\n                \"shipping_city\":\"铜陵市\",\n                \"create_at\":\"2023-07-20 15:51:30\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "97e4bc1b-7613-4683-aa38-1cb2a6302149", "ruleName": "xqx-加盟商推广-列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/LeaguePromotionSpecialist/lists", "filterId": "2ab24281-b732-409d-8f27-38a0efeaca78", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n\"data\": {\n\"code\": 0,\n\"msg\": \"成功\",\n\"data\": {\n\"total\": 1,\n\"page\": 1,\n\"pageSize\": 20,\n\"list\": [\n{\n\"id\": \"1\",\n\"league_id\": \"37\",\n\"name\": \"测试\",\n\"mobile\": \"13453444153\",\n\"notes\": \"c测试\",\n\"regiment_num\": \"2\",\n\"regiment_order_num\": \"20\",\n\"users_num\": \"3\",\n\"users_order_num\": \"30\",\n\"create_at\": \"2023-08-04 14:05:28\",\n\"update_at\": \"2023-08-04 14:05:28\"\n},\n{\n\"id\": \"2\",\n\"league_id\": \"40\",\n\"name\": \"测试11111\",\n\"mobile\": \"134500004153\",\n\"notes\": \"c测试aaaaaaaaa\",\n\"regiment_num\": \"2\",\n\"regiment_order_num\": \"20\",\n\"users_num\": \"3\",\n\"users_order_num\": \"30\",\n\"create_at\": \"2023-08-05 14:05:28\",\n\"update_at\": \"2023-08-05 14:05:28\"\n}\n]\n}\n},\n\"statusCode\": \"\",\n\"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9bab46c9-5ccc-414e-92b0-ff8215e84391", "ruleName": "xqx-分佣管理-修改昵称", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/commission/edit", "filterId": "482ff684-ae17-4596-ad7e-bb60e0bd773d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {},\n    msg: '',\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "cbdb3b47-d59b-4fb7-862d-a5d08420d154", "ruleName": "xqx-分佣管理-好友详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/CustomInviteStatistic/inviteUserDetail", "filterId": "9ab447b7-657e-4973-a46c-31d98e39dc53", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      nickName: '@cname',\n      remark: '这是备注',\n      avatar: 'https://img.kuaidihelp.com/qj/miniapp/logo.png',\n      mobile: '18877773333',\n      'level|1': [2],\n      register_at: '@datetime',\n\n      order_count : '@integer(1,10)',\n      profit: '@float(1,10,2,2)',\n      regiment_order_count: '@integer(1,10)',\n      regiment_profit: '@float(1,10,2,2)',\n      register_regiment_at: '@datetime',\n      register_league_at: '@datetime',\n      league_order_count :'@integer(1,10)',\n      league_profit :'@float(1,10,2,2)',\n      award_order_count :'@integer(1,10)',\n      award_profit :'@float(1,10,2,2)',\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "38a8c087-c3de-478f-b497-0b34434a5109", "ruleName": "xqx-分佣管理-好友列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/CustomInviteStatistic/userInviteLevelList", "filterId": "1be4d431-f36e-483d-a40c-a597634ce449", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    \"data\": {\n      total: 5,\n      \"list|1-10\": [\n        {\n          \"user_id\":\"@id\",\n          \"nickname\":\"@cname\",\n          \"avatar\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n          \"register_at\":\"@datetime\",\n          \"mobile\": '18877772222',\n          \"order_count\":\"@integer(1,10000)\",\n          \"order_profit\":\"@integer(1,10000)\",\n        }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e48e5b10-4d53-47b8-b04f-d381015b1bab", "ruleName": "xqx-物流轨迹", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/Logistics/expressTrack", "filterId": "d642e950-bfea-40c0-a79c-01484a1e70ef", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      \"waybill\": \"JDX017712044151\",\n      \"brand\": \"jd\",\n      \"day_consumed\": \"\",\n      \"status\": \"代收\",\n      \"list|10\":[{\n        \"status\": 0,\n        \"date\": \"14:11<br\\/>2023-07-27\",\n        \"time\": \"@datetime\",\n        \"info\": \"揽收任务已分配给刘俊。\"\n      }]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "0e128f40-1a9c-4864-abed-056beb15077a", "ruleName": "xqx-资金-资金往来", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/orderTransaction", "filterId": "52c3aed1-fed1-47fe-a8ec-b8e414e51d26", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"order_id\":\"808095600000619\",\"total_money\":\"-5.50\",\"list\":[{\"id\":\"6638\",\"kb_id\":\"252037573\",\"kb_type\":\"regiment\",\"channel\":\"allPost\",\"trans_type\":\"out\",\"business_type\":\"freight_out\",\"business_desc\":\"下单预支付\",\"order_number\":\"fo_64d3414089393\",\"trade_number\":\"fo_64d3414089393\",\"money\":\"5.50\",\"fee_money\":\"0.00\",\"available_money\":\"5.50\",\"is_successed\":\"1\",\"is_show\":\"1\",\"balance_cash\":\"9886.77\",\"balance_frozen\":\"200.00\",\"desc\":\"付款给平台\",\"order_id\":\"808095600000619\",\"platform_kb_id\":\"279040379\",\"create_time\":\"2023-08-09 15:33:20\",\"success_time\":\"2023-08-09 15:33:20\"}]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ab1f06f7-5b5c-4353-af51-6a434924aafa", "ruleName": "xqx-个人信息-获取团长信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/Regiment/getInfo", "filterId": "af2e96f1-2286-4632-9a80-591a6367e6dc", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"id\":\"2\",\n        \"nickname\":\"<PERSON>\",\n        \"mobile\":\"15270840581\",\n        \"avatar_url\":\"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n        wx_card: \"https:\\/\\/upload.kuaidihelp.com\\/package_pics\\/2023\\/08\\/04\\/43774722778720264cc959c611268292723545.png\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "eb54d971-d6bd-43ab-ae99-6c99cfb1810c", "ruleName": "xqx-下单-获取公司品牌列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/getBrands", "filterId": "b1c27f11-4ed5-4753-9708-11accf20ebb6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"data\":[{\n          \"brand\":\"ss\",\n            \"hot\":1,\n            \"message\":\"优质时效\",\n            \"type\":\"fxj\",\n            \"pay\":\"2\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1\n    }, {\n            \"brand\":\"fczy\",\n            \"hot\":1,\n            \"message\":\"优质时效\",\n            \"type\":\"fxj\",\n            \"pay\":\"2\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1}],\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n\"data\": {\n\"code\": 0,\n\"msg\": \"成功\",\n\"data\": [\n{\n\"brand\": \"sf\",\n\"hot\": 1,\n\"message\": \"预约寄件 快速上门\",\n\"type\": \"fxj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 1,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 8.5,\n\"rate_type\": \"rate\",\n\"sort_info\": {\n\"key\": 1,\n\"title\": \"品质时效\",\n\"sub_title\": \"58%的人选择\"\n},\n\"product_types\": {\n\"offline\": [\n{\n\"label\": \"标快\",\n\"value\": \"offer\"\n},\n{\n\"label\": \"特快\",\n\"value\": \"express\"\n}\n],\n\"online\": [\n{\n\"label\": \"标快\",\n\"value\": \"offer\"\n},\n{\n\"label\": \"特快\",\n\"value\": \"express\"\n}\n]\n},\n\"disable\": 0\n},\n{\n\"brand\": \"sfky\",\n\"hot\": 1,\n\"message\": \"重货包入户，大件更优惠\",\n\"type\": \"fxj\",\n\"pay\": \"2\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"100\",\n\"weightLimitMin\": \"20\",\n\"isYjkd\": 1,\n\"arrivePay\": 1,\n\"payTypes\": [\n1,\n2\n],\n\"rate\": 10,\n\"rate_type\": \"rate\",\n\"sort_info\": {\n\"key\": 1,\n\"title\": \"品质时效\",\n\"sub_title\": \"58%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"jd\",\n\"hot\": 1,\n\"message\": \"优质时效\",\n\"type\": \"fxj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 1,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 6.8,\n\"rate_type\": \"rate\",\n\"sort_info\": {\n\"key\": 1,\n\"title\": \"品质时效\",\n\"sub_title\": \"58%的人选择\"\n},\n\"product_types\": {\n\"offline\": [\n{\n\"label\": \"特惠送\",\n\"value\": \"offer\"\n},\n{\n\"label\": \"特快送\",\n\"value\": \"express\"\n}\n],\n\"online\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"dp\",\n\"hot\": 1,\n\"message\": \"大件特惠超值寄\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 7.3,\n\"rate_type\": \"rate\",\n\"sort_info\": {\n\"key\": 1,\n\"title\": \"品质时效\",\n\"sub_title\": \"58%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"ems\",\n\"hot\": 1,\n\"message\": \"预约寄件 优质时效\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": null,\n\"rate_type\": null,\n\"sort_info\": {\n\"key\": 1,\n\"title\": \"品质时效\",\n\"sub_title\": \"58%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"yd\",\n\"hot\": 1,\n\"message\": \"极速取件 贴心服务\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 0.5,\n\"rate_type\": \"append\",\n\"sort_info\": {\n\"key\": 2,\n\"title\": \"经济实惠\",\n\"sub_title\": \"42%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"yt\",\n\"hot\": 1,\n\"message\": \"一键下单 快速取件\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 0.5,\n\"rate_type\": \"append\",\n\"sort_info\": {\n\"key\": 2,\n\"title\": \"经济实惠\",\n\"sub_title\": \"42%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"sto\",\n\"hot\": 1,\n\"message\": \"安心下单 闪电送达\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 0.5,\n\"rate_type\": \"append\",\n\"sort_info\": {\n\"key\": 2,\n\"title\": \"经济实惠\",\n\"sub_title\": \"42%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 0\n},\n{\n\"brand\": \"zt\",\n\"hot\": 1,\n\"message\": \"放心寄件 急速服务\",\n\"type\": \"djj\",\n\"pay\": \"3\",\n\"cutPayDesc\": \"在快递员上门揽收后\",\n\"weightLimitMax\": \"\",\n\"weightLimitMin\": \"\",\n\"isYjkd\": 1,\n\"arrivePay\": 0,\n\"payTypes\": [\n0,\n2\n],\n\"rate\": 0.5,\n\"rate_type\": \"append\",\n\"sort_info\": {\n\"key\": 2,\n\"title\": \"经济实惠\",\n\"sub_title\": \"42%的人选择\"\n},\n\"product_types\": {\n\"online\": [],\n\"offline\": []\n},\n\"disable\": 1\n}\n]\n},\n\"statusCode\": \"\",\n\"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5b0f1def-2fc1-49e7-be28-eb9da8f6275a", "ruleName": "xqx-下单-德邦特殊折扣", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/checkDpOfflineOrder", "filterId": "5369d1f7-c075-40c9-b79d-4632905d6097", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": ,\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      status: true,\n      rate: 8\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "76e28610-045f-4671-8ce7-acae26aac69e", "ruleName": "xqx-下单-批量报价单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/quotation/getBatchQuotationList", "filterId": "bab8effc-5869-4db0-a713-d66bf9292f00", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n\"code\": 0,\n\"msg\": \"成功\",\n\"data\": [\n{\n\"price\": 0,\n\"discount_price\": \"0.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 20,\n\"s_kg\": \"0\",\n\"f_fee\": \"0\",\n\"s_fee\": \"0\",\n\"s_total_fee\": \"0.00\",\n\"original_price\": \"0.00\",\n\"original_f_fee\": 0,\n\"original_s_fee\": 0,\n\"original_s_total_fee\": \"0.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"sf\",\n\"available\": 0,\n\"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\"\n},\n{\n\"price\": 0,\n\"discount_price\": \"0.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 20,\n\"s_kg\": \"0\",\n\"f_fee\": \"0\",\n\"s_fee\": \"0\",\n\"s_total_fee\": \"0.00\",\n\"original_price\": \"0.00\",\n\"original_f_fee\": 0,\n\"original_s_fee\": 0,\n\"original_s_total_fee\": \"0.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"sfky\",\n\"available\": 0,\n\"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\"\n},\n{\n\"price\": \"46.00\",\n\"discount_price\": \"46.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"8\",\n\"s_fee\": \"2\",\n\"s_total_fee\": \"38.00\",\n\"original_price\": \"46.00\",\n\"original_f_fee\": 8,\n\"original_s_fee\": 2,\n\"original_s_total_fee\": \"38.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"sto\"\n},\n{\n\"price\": \"76.50\",\n\"discount_price\": \"76.50\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"10\",\n\"s_fee\": \"3.5\",\n\"s_total_fee\": \"66.50\",\n\"original_price\": \"76.50\",\n\"original_f_fee\": 10,\n\"original_s_fee\": 3.5,\n\"original_s_total_fee\": \"66.50\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"yd\"\n},\n{\n\"price\": \"89.00\",\n\"discount_price\": \"89.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 3,\n\"s_kg\": \"17\",\n\"f_fee\": \"21.00\",\n\"s_fee\": \"4.00\",\n\"s_total_fee\": \"68.00\",\n\"original_price\": \"89.00\",\n\"original_f_fee\": 21,\n\"original_s_fee\": 4,\n\"original_s_total_fee\": \"68.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"dp\"\n},\n{\n\"price\": \"107.00\",\n\"discount_price\": \"107.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"12\",\n\"s_fee\": \"5\",\n\"s_total_fee\": \"95.00\",\n\"original_price\": \"107.00\",\n\"original_f_fee\": 12,\n\"original_s_fee\": 5,\n\"original_s_total_fee\": \"95.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"yt\"\n},\n{\n\"price\": \"130.00\",\n\"discount_price\": \"130.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"16.00\",\n\"s_fee\": \"6.00\",\n\"s_total_fee\": \"114.00\",\n\"original_price\": \"130.00\",\n\"original_f_fee\": 16,\n\"original_s_fee\": 6,\n\"original_s_total_fee\": \"114.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"jd\"\n},\n{\n\"price\": \"164.00\",\n\"discount_price\": \"164.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"12\",\n\"s_fee\": \"8\",\n\"s_total_fee\": \"152.00\",\n\"original_price\": \"164.00\",\n\"original_f_fee\": 12,\n\"original_s_fee\": 8,\n\"original_s_total_fee\": \"152.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"zt\"\n},\n{\n\"price\": \"211.00\",\n\"discount_price\": \"211.00\",\n\"discount_total_amount\": 0,\n\"f_kg\": 1,\n\"s_kg\": \"19\",\n\"f_fee\": \"21.00\",\n\"s_fee\": \"10.00\",\n\"s_total_fee\": \"190.00\",\n\"original_price\": \"211.00\",\n\"original_f_fee\": 21,\n\"original_s_fee\": 10,\n\"original_s_total_fee\": \"190.00\",\n\"discount_list\": [],\n\"commission\": \"\",\n\"brand\": \"ems\"\n}\n]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d066dba9-9555-4a04-8420-6834e426ffd1", "ruleName": "xqx-短链-获取短链", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/invitationLink", "filterId": "44f089d2-22b0-4ae3-9186-02db8b7ebdcc", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":\"http://kd1.cn/eKZG\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8d5456ba-9416-4282-b4a5-e7d4b9844f20", "ruleName": "xqx-获取下单加个展示方式", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/getPriceSetting", "filterId": "f3276484-3682-4bea-920a-b9b83115c2d6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|1': [1,2,3],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "3834956a-1086-4167-b37d-2ca83363345b", "ruleName": "xqx-订单-标记已付", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/mark", "filterId": "9c7c5bef-f49b-4f45-bfff-cdba82b09282", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "0ab171bf-1dba-4969-a78b-f93acfe17b42", "ruleName": "xqx-订单-批量删除订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/order/deleteOrder", "filterId": "193bfe2b-ddec-4709-ae98-dbb62f694819", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    msg: 'success',\n    data: {}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8f01c53f-9c86-457a-a538-d473cf76ce24", "ruleName": "xqx-下单-预约取件时间", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/PickupTime/getPickupTime", "filterId": "a083a75f-5a3f-4d4c-952a-1c97510da8f2", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"pickupSliceTimeDTOList\":[{\"dateKey\":\"2023-05-08\",\"pickupSliceTimeList\":[{\"startTime\":\"\",\"endTime\":\"\",\"name\":\"立即取件\"}]},{\"dateKey\":\"2023-05-09\",\"pickupSliceTimeList\":[{\"startTime\":\"08:00:00\",\"endTime\":\"09:00:00\",\"name\":\"08:00-09:00\"},{\"startTime\":\"9:00:00\",\"endTime\":\"10:00:00\",\"name\":\"9:00-10:00\"},{\"startTime\":\"10:00:00\",\"endTime\":\"11:00:00\",\"name\":\"10:00-11:00\"},{\"startTime\":\"11:00:00\",\"endTime\":\"12:00:00\",\"name\":\"11:00-12:00\"},{\"startTime\":\"12:00:00\",\"endTime\":\"13:00:00\",\"name\":\"12:00-13:00\"},{\"startTime\":\"13:00:00\",\"endTime\":\"14:00:00\",\"name\":\"13:00-14:00\"},{\"startTime\":\"14:00:00\",\"endTime\":\"15:00:00\",\"name\":\"14:00-15:00\"},{\"startTime\":\"15:00:00\",\"endTime\":\"16:00:00\",\"name\":\"15:00-16:00\"},{\"startTime\":\"16:00:00\",\"endTime\":\"17:00:00\",\"name\":\"16:00-17:00\"},{\"startTime\":\"17:00:00\",\"endTime\":\"18:00:00\",\"name\":\"17:00-18:00\"}]},{\"dateKey\":\"2023-05-10\",\"pickupSliceTimeList\":[{\"startTime\":\"08:00:00\",\"endTime\":\"09:00:00\",\"name\":\"08:00-09:00\"},{\"startTime\":\"9:00:00\",\"endTime\":\"10:00:00\",\"name\":\"9:00-10:00\"},{\"startTime\":\"10:00:00\",\"endTime\":\"11:00:00\",\"name\":\"10:00-11:00\"},{\"startTime\":\"11:00:00\",\"endTime\":\"12:00:00\",\"name\":\"11:00-12:00\"},{\"startTime\":\"12:00:00\",\"endTime\":\"13:00:00\",\"name\":\"12:00-13:00\"},{\"startTime\":\"13:00:00\",\"endTime\":\"14:00:00\",\"name\":\"13:00-14:00\"},{\"startTime\":\"14:00:00\",\"endTime\":\"15:00:00\",\"name\":\"14:00-15:00\"},{\"startTime\":\"15:00:00\",\"endTime\":\"16:00:00\",\"name\":\"15:00-16:00\"},{\"startTime\":\"16:00:00\",\"endTime\":\"17:00:00\",\"name\":\"16:00-17:00\"},{\"startTime\":\"17:00:00\",\"endTime\":\"18:00:00\",\"name\":\"17:00-18:00\"}]}]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7c11bda2-51db-4362-93a8-7e660abad571", "ruleName": "xqx-团员运营-团员列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/getOperateUserList", "filterId": "de4a7e33-c9a1-4a46-981d-ddeac636b06d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n      {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-07-11 14:10:41\",\n         country: \"\",\n         create_time: \"2022-06-26 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major <PERSON>\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n    ],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "916d2746-be4f-4658-bebd-1445c8d98281", "ruleName": "xqx-团员运营", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/operateList", "filterId": "8b52eb43-1a96-4272-bf32-b98e321417c3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n      {\n        key: 1,\n        name: '主力团员',\n        desc: '团员列表中的下单前五名，团队内中军力量',\n        'user_lists|5': [\n          'https://img.kuaidihelp.com/qj/miniapp/logo.png',\n        ]\n      },\n      {\n        key: 2,\n        name: '高潜力团员',\n        desc: '最近一周内（7天）寄大于3单的团员',\n        'user_lists|5': [\n          'https://img.kuaidihelp.com/qj/miniapp/logo.png',\n        ]\n      },\n      {\n        key: 3,\n        name: '潜在团员',\n        desc: '最近半个月内寄件量大于1单的团员',\n        'user_lists|4': [\n          'https://img.kuaidihelp.com/qj/miniapp/logo.png',\n        ]\n      },\n      {\n        key: 4,\n        name: '即将流失团员',\n        desc: '最近1个月内没有下单的团员',\n        'user_lists|3': [\n          'https://img.kuaidihelp.com/qj/miniapp/logo.png',\n        ]\n      },\n      {\n        key: 5,\n        name: '已流失团员',\n        desc: '最近1个月内没有下单的团员',\n        'user_lists': [\n          \n        ]\n      },\n    ],\n    msg: 'sucess'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e161077a-c856-443d-b5b8-bfe3f84fdeaa", "ruleName": "xqx-取消订单-中通批量订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/batchNumberOrderList", "filterId": "908c3296-027d-4b81-85d9-ecffa0706667", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|0-2': [\n      {\n        order_id: '@natural',\n        waybill: '@natural',\n      }\n    ],\n    msg: 'msg'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "245e8609-e093-45a2-9546-543278eb8cdb", "ruleName": "xqx-下单-检查中通是否能下单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/batchNumberOrderCount", "filterId": "72f7794d-c860-456b-92c7-43eee3737485", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    \"data\": {\n      'count|1': [0,1,2,]\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e1db0c9c-7b95-4c88-8f6f-3090dab332bf", "ruleName": "xqx-报价单-批量获取", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/quotation/getBatchQuotationList", "filterId": "8c08144f-5308-4b90-b61f-7912697e7cbb", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":[{\"price\":\"25.50\",\"discount_price\":\"30.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"25.50\",\"s_fee\":\"9.35\",\"s_total_fee\":\"0.00\",\"original_price\":\"30.00\",\"original_f_fee\":\"30.00\",\"original_s_fee\":\"11.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"sf\"},{\"price\":\"20.25\",\"discount_price\":\"27.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"20.25\",\"s_fee\":\"6.00\",\"s_total_fee\":\"0.00\",\"original_price\":\"27.00\",\"original_f_fee\":\"27.00\",\"original_s_fee\":\"8.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"jd\"},{\"price\":\"17.52\",\"discount_price\":\"24.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"17.52\",\"s_fee\":\"5.11\",\"s_total_fee\":\"0.00\",\"original_price\":\"24.00\",\"original_f_fee\":\"24.00\",\"original_s_fee\":\"7.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"dp\"},{\"price\":\"14.40\",\"discount_price\":\"22.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"14.40\",\"s_fee\":\"5.70\",\"s_total_fee\":\"0.00\",\"original_price\":\"22.00\",\"original_f_fee\":\"22.00\",\"original_s_fee\":\"8.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"yd\"},{\"price\":\"12.50\",\"discount_price\":\"20.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"12.50\",\"s_fee\":\"4.00\",\"s_total_fee\":\"0.00\",\"original_price\":\"20.00\",\"original_f_fee\":\"20.00\",\"original_s_fee\":\"8.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"yt\"},{\"price\":\"13.20\",\"discount_price\":\"22.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"13.20\",\"s_fee\":\"4.70\",\"s_total_fee\":\"0.00\",\"original_price\":\"22.00\",\"original_f_fee\":\"22.00\",\"original_s_fee\":\"8.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"sto\"},{\"price\":\"13.40\",\"discount_price\":\"22.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"13.40\",\"s_fee\":\"5.70\",\"s_total_fee\":\"0.00\",\"original_price\":\"22.00\",\"original_f_fee\":\"22.00\",\"original_s_fee\":\"6.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"jt\"},{\"price\":\"20.00\",\"discount_price\":\"20.00\",\"discount_total_amount\":\"0.00\",\"f_kg\":1,\"s_kg\":\"0\",\"f_fee\":\"20.00\",\"s_fee\":\"8.00\",\"s_total_fee\":\"0.00\",\"original_price\":\"20.00\",\"original_f_fee\":\"20.00\",\"original_s_fee\":\"8.00\",\"original_s_total_fee\":\"0.00\",\"discount_list\":[],\"commission\":\"\",\"brand\":\"zt\"}]},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8518160c-93fa-41d2-8b13-65d444dd0286", "ruleName": "xqx-物流-更新物流", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Logistics/batchUpdateStatus", "filterId": "005c5816-65bb-4b15-9b28-337bdc25e32c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"zt-78670251909766\":{\n          \"last_logistics\":\"2023-03-25 17:02:27 【上海市】 快件已由 【家门口】 签收, 签收网点: 【普陀祥和】, 如有疑问请电联:（13872428114）, 投诉电话:（021-60664055）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】\",\n          \"state\":\"signed\"\n        },\n        \"zt-78669492524953\":{\n          \"last_logistics\":\"2023-03-12 16:16:41 包裹已签收！签收人是【楼下门口楼梯】，如有问题请联系华振珊：18378235700，投诉电话：021-38926504\",\n          \"state\":\"signed\"\n        }\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "dfdebe24-824d-43b7-a8f9-dbfc3132f359", "ruleName": "xqx-下单-批量下单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/BatchOrder/batchSubmit", "filterId": "73ca7b65-9af6-4a74-9e59-c702c33c1e6d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n\"code\": 0,\n\"msg\": \"成功\",\n\"data\": {\n\"success\": [\n{\n\"array\": true,\n\"wx_after_pay_bind\": 0,\n\"wx_after_pay\": false,\n\"order_id\": \"803173726308751\",\n\"waybill_no\": \"DPK6640023693358\",\n\"third_id\": \"TLGU803173726308751\",\n\"id\": 0\n}\n],\n\"error\": [\n{\n\"id\": 0,\n\"shipping_name\": \"打的费\",\n\"shipping_mobile\": \"18899998888\",\n\"shipping_province\": \"上海市\",\n\"shipping_city\": \"上海市\",\n\"shipping_district\": \"长宁区\",\n\"shipping_address\": \"是啥时候上\",\n\"error\": \"下单错误 - 货物名称不能为空！\"\n}\n],\n\"filePath\": \"https://upload.kuaidihelp.com/regiment/courier/error_order/20230317/15ced9e9888ac1307278a7eeed7ee908.csv\"\n}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d5692c68-3617-461b-bd7b-c1e7b12bfc56", "ruleName": "xqx-加盟商管理-团长列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/regimentList", "filterId": "371ea83e-33a3-4f11-adab-de01d75d9d84", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"调用成功\",\n    \"data\":{\n        \"list\":[\n            {\n                \"id\":\"284\",\n                \"openid\":\"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n                \"nickname\":\"全寄\",\n                \"mobile\":\"15270840581\",\n                \"gender\":\"未知\",\n                \"language\":\"\",\n                \"city\":\"\",\n                \"province\":\"\",\n                \"country\":\"\",\n                \"avatar_url\":\"https://upload.kuaidihelp.com//package_pics/2023/02/17/451414798382773763eedf575e4151270852259.jpg\",\n                \"unionid\":\"oEp1_tycwyQQb015-KQSk-babrbs\",\n                \"channel\":\"wechat_mini\",\n                \"is_admin\":\"1\",\n                \"regiment_id\":\"150\",\n                \"note\":\"\",\n                \"kb_id\":\"191927231\",\n                \"complate_at\":\"2023-02-17 09:58:48\",\n                \"create_time\":\"2023-02-17 09:58:35\",\n                \"old_regiment_id\":\"0\",\n                \"is_black\":\"1\",\n                \"remarks\":\"测试\"\n            }\n        ],\n        \"total_num\":\"1\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ca8cbb22-b9ae-42c0-8b35-93bff11e162e", "ruleName": "xqx-首页-物流列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Logistics/expressList", "filterId": "e81ac5d0-430d-4071-bee2-3d29c7d59bdb", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  'data': {\n    \"code\":0,\"msg\":\"成功\",\"data\":[]}\n  ,\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "185feca0-b4cf-4c07-a2f1-5ef61f3670a9", "ruleName": "xqx-收益统计-加盟商收益明细", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/staticMonthData", "filterId": "aabee2c4-c5fc-424f-afaa-1c4ac349ae95", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    \"data|1-10\": [\n      {\n        \"league_id|+1\":1,\n        \"brand\":\"jt\",\n        \"order_num|1-100\": 1,\n        \"profit|1-10.1\":1,\n        \"static_date\":\"@date\"\n      }\n    ],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9b1adcef-dbe0-42f7-b5da-856fb2b2968f", "ruleName": "xqx-收益统计-加盟商总收益", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/staticTotalData", "filterId": "63a9d15c-3f9a-43fa-a205-cd4bc20a479c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"total_profit\":\"10\",\n        \"total_data\":{\n            \"order_num\":3,\n            \"profit\":2.7\n        },\n        \"details\":[\n            {\n                \"league_id\":3,\n                \"brand\":\"jd\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":3,\n                \"brand\":\"sf\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":3,\n                \"brand\":\"dp\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":3,\n                \"brand\":\"sto\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":3,\n                \"brand\":\"yd\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":3,\n                \"brand\":\"yt\",\n                \"order_num\":0,\n                \"profit\":0\n            },\n            {\n                \"league_id\":\"3\",\n                \"brand\":\"jt\",\n                \"order_num\":\"3\",\n                \"profit\":\"2.70\"\n            }\n        ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ef43c248-d6dc-4a69-af14-33626497c375", "ruleName": "xqx-获取微信二维码", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/RegimentUser/getWxQrCode", "filterId": "4cd68d2e-453b-4b0c-991e-6e162d1be1fd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    'data|1': [0,1],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b8fbd51b-4a77-437c-a8fc-27e6b04f0b10", "ruleName": "xqx-收益统计-明细", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/getStaticDetail", "filterId": "0a41e0a0-2173-4524-98a8-017da7247c69", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    'data|1-10': [\n      {\n        'regiment_id|+1': 1,\n        'regiment_name': '@cname',\n        \"profit|1-100.1-2\": 1,\n        'static|1-10': [\n          {\n            \"brand|1\": ['sf', 'dp', 'yt'],\n            'profit|1-100.1-2': 1,\n            'order_num|1-100': 1,\n          }\n        ]\n      }\n    ],\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b0e9ccf0-78b1-417b-81d3-1621a382cc2a", "ruleName": "xqx-加盟商管理-近7日数据", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/League/profitTrend", "filterId": "ffbee63f-3bae-4031-837a-b31f52e11db3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    'code|1': [0],\n    'data' : {\n      'yesterday_profit|1-100000': 1, \n      'regiment_num|1-100': 1,\n      \"static_data\": [\n        {\n                \"profit\":0,\n                \"order_num\":0,\n                \"static_date\":\"2023-03-06\"\n            },\n            {\n                \"profit\":0,\n                \"order_num\":0,\n                \"static_date\":\"2023-03-07\"\n            },\n            {\n                \"profit\":0,\n                \"order_num\":0,\n                \"static_date\":\"2023-03-08\"\n            },\n            {\n                \"profit\":0,\n                \"order_num\":0,\n                \"static_date\":\"2023-03-09\"\n            },\n            {\n                \"profit\":\"10.00\",\n                \"order_num\":\"5\",\n                \"static_date\":\"2023-03-10\"\n            },\n            {\n                \"profit\":\"5.00\",\n                \"order_num\":\"2\",\n                \"static_date\":\"2023-03-11\"\n            },\n            {\n                \"profit\":\"5.00\",\n                \"order_num\":\"2\",\n                \"static_date\":\"2023-03-12\"\n            }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6e5fed24-279f-4f81-8f38-a810155ccd15", "ruleName": "xqx-加盟商管理-昨日信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/getFranchiseeData", "filterId": "6420446a-c511-4a9a-84a1-583f2975583c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": ,\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    data: {\n      \"fee|1000-********.1-2\": 1,\n      \"member|1-100\": 100\n    },\n    msg: 'succsee'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a50e2574-a521-4c55-a30c-99798a180208", "ruleName": "xqx-已授权列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/courier/Account/bindAccountList", "filterId": "b2a4394f-f1af-4576-a86a-e5bb04c2928c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      'inn|3': [{\n        name: '@name',\n        phone: '***********'\n      }],\n      'courier|5': [{\n        name: '@name',\n        phone: '***********'\n      }],\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a0ce4390-d6aa-4933-b750-a098a96afaf0", "ruleName": "xqx-首页-绑定驿站授权手机号", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/api/courier/Account/accountBind", "filterId": "eaa10ee6-48ae-44f2-8048-1f02acfe200c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {},\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7619f6b2-4c35-4488-9cad-a1d6061c4f5c", "ruleName": "xqx-首页-判断是否已绑定账号", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/courier/Courier/checkIsBind", "filterId": "1ecec9c9-ec42-446c-bf55-512f0a7c8a5f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|1': [true, false],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7d4d69ed-01fa-4aa1-ad40-90c4a5f3652a", "ruleName": "xqx-通过交易流水号获取订单号", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/api/Order/queryAuthOrder", "filterId": "717245d3-e0b0-44bf-b291-c8519ffdb9d2", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      order_id: '801055751618820'\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "edbfe732-2941-4dab-8c6c-9e261f84d0d9", "ruleName": "xqx-京东服务费检查", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/checkPayServiceCharge", "filterId": "007f31bb-2d4e-4f78-99f9-0727bf7122cd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    code: 0,\n    \n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      \"is_pay|1\": [ true, false, 'true' ,'1'],\n      desc: '这是描述'\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "be8affdc-b8cc-4545-b4f6-27671e7df746", "ruleName": "xqx-停发区获取分享数据", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/StopArea/getShareData", "filterId": "8ffd3e3f-a31f-4438-a6d3-fb44c8dc7bfc", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    'data|1-10': [\n      {\n        \"测试地址|1-3\": [{\n          'brand|1': [\"zt\", 'yt', 'yd'],\n          'msg|1': [\"成功\", '失败原因'],\n        }]\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "adde19fe-b9d0-4116-b2e7-d2780329b812", "ruleName": "xqx-停发区获取分享token", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/StopArea/share", "filterId": "b1dcefb4-e713-459e-ae6d-e883b60fce10", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data : {\n      token: \"75293a5935fc7ea90ff2b43c6ad63cc6\",\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8b48b561-31a3-45d5-9937-47e5dc39c0a5", "ruleName": "xqx-停发区查询", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/StopArea/search", "filterId": "2965c94d-67aa-45ee-a802-8a51841043fb", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    'data|1-10': [\n      {\n        \"测试地址|1-3\": [{\n          'brand|1': [\"zt\", 'yt', 'yd'],\n          msg: \"成功\",\n        }]\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "970cf468-86ab-4df0-b7b3-df1c64c3eeee", "ruleName": "xqx-停发区查询支持的品牌", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/StopArea/supportBrandList", "filterId": "45f1da3e-ae18-422c-b595-bb93c819e75a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"data\": {zt: \"中通\", yt: \"圆通\", sto: \"申通\", yd: \"韵达\", fw: \"丰网\", jt: \"极兔\"},\n    msg: \"成功\",\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "89911790-c88d-40e6-9b8d-9472aae74683", "ruleName": "xqx-查询是否下单成功", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/getCallbackCreateOrderStatus", "filterId": "30ef5335-92d9-493c-8d5b-0fd78e5de377", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    'code|1': [0, 200, 4000],\n    data: {},\n    msg: '200,失败了'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "158e1491-a843-4d5a-bab8-167bc5e4707c", "ruleName": "xqx-保证金退款", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/refundDeposit", "filterId": "04555e58-67b4-4755-b038-dbfbb469ef22", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code|1\": [ 20230530],\n    data: {},\n    msg: '有未完结的订单有未完结的订单有未完结的订单有未完结的订单有未完结的订单有未完结的订单'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "0017971f-9441-4059-bd9c-1928d48604ce", "ruleName": "未命名规则", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/getMemberServiceCharge", "filterId": "848a9eb6-e14e-4cf5-98b0-47101e856876", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"\",\n    \"data|1\":[1.4, 1.2, 0.3, 3]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "54153397-af18-4a49-bedb-0cf1df0f6262", "ruleName": "xqx-服务费支付签名", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/payServiceChargeSign", "filterId": "80e71700-70bf-4646-9060-2a6165ed7fcf", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n       \"sign\":{\n            \"appId\":\"wx1ac840bddf4b78cf\",\n            \"nonceStr\":\"rwamwQOoJusIes81\",\n            \"timeStamp\":\"1662108261\",\n            \"package\":\"prepay_id=wx02164421989476d39d4affd57bdd660000\",\n            \"signType\":\"MD5\",\n            \"sign\":\"3D90E19752E4DE0DDCBD945E425B3FC5\"\n        },\n        \"order_id\":\"709023691109818\"\n    },\n    msg : 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "4543d7d6-5d4b-4b6b-9fbe-9d19ba3df067", "ruleName": "xqx-查询服务费支付状态", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/queryServiceChargePayStatus", "filterId": "b5d8b4dd-8bb4-4a95-be2f-0c3c17950080", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data|1\":['false']\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c72d7250-3291-41af-a85a-36804ba0cf0a", "ruleName": "xqx-获取京东默认服务费", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/getDefaultServiceCharge", "filterId": "1131812b-e86d-480b-8154-527731a2a3b7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: 2,\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ec93ff32-3f4d-4408-b4bb-7eeb99b8accb", "ruleName": "xqx-获取实名信息及状态", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Regiment/regimentRealnameStatus", "filterId": "3271f8d9-6aef-4923-9cee-5a23e311a31d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    data: {\n      'regiment_realname_status|1': [1],\n      id_card_name: '夏xxx',\n      id_card_number: '333xxxx000'\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b7cf4f3a-be54-4575-a620-9c4d6233a390", "ruleName": "xqx-人脸验证", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/realName", "filterId": "3a5b05a2-9310-448e-9b8f-a4c5289a28d3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    data: {},\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "cd0b9a98-2094-4b2d-a0bc-b1e4ebd4db28", "ruleName": "xqx-获取团长身份信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/getIdInfo", "filterId": "6e5c5eb0-ca6e-4884-ad20-df9030902954", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    data: {\n      name: '夏xx',\n      idCard: '33xxxxxxxxxx00'\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "867ffb64-6754-4fac-9c60-f8ae9cae6814", "ruleName": "xqx-检查是否推送账单成功", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/pushWaitPayNotice", "filterId": "bd53295a-7b77-4c49-a7e9-d8e14b1f6c32", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    'code|1': [0, 1],\n    data: 1,\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1179907e-f875-49f6-b4a6-23dfa5a5f864", "ruleName": "xqx-新人礼邀请信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/PromotionActivity/invite", "filterId": "a7735af2-4c9b-4c74-8866-b7303c4b94bd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      'count|1': [0,1,3,5,6,10,13,15,20, false],\n      \"config\":{\n        \"1\":0.2,\n        \"5\":0.5,\n        \"10\":0.9,\n        \"15\":1.4\n      },\n      \"activity_start_time\":\"2022-08-15 00:00:00\",\n      \"activity_end_time\":\"2022-10-01 00:00:00\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7519ffed-5522-47b8-95b7-6dacbe2ab6e6", "ruleName": "xqx-订单打印获取code", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/printOrder", "filterId": "47bba84c-5180-4492-b1ff-9bab4671a418", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    msg: 'success',\n    data: 'ashdfusdfohishdofi'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f79420ba-9c04-4083-bcd4-db37d2e21cb9", "ruleName": "xqx-新人礼列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/PromotionActivity/order", "filterId": "777c8938-93e7-4a26-9eb4-4b202c9925f6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    'data|5': [\n      {\n        'order_id|+1': 1,\n        'brand|1': ['sf', 'jd', 'dp'],\n        'money|1': [3.5, 0.5],\n        'waybill_no': '@id',\n        'type|1': ['first_order_back','']\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "99b410f1-b424-4d46-b2be-38a205d3d82d", "ruleName": "xqx-活动详情历史记录", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/ArrivePay/lists", "filterId": "7116952d-1138-49b5-b086-ea9e5ecb7efa", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|10': [{\n      \"id|+1\":1,\n      \"regiment_id\":\"56\",\n      \"order_num\":\"2\",\n      \"freight\":\"31.00\",\n      \"rebate\":\"2.56\",\n      \"date|1\": [\"2022-08\", '2022-09'],\n      \"created_at\":\"2022-08-22 14:24:27\",\n      \"update_at\":\"2022-08-22 18:18:46\",\n      \"year\":\"2022\",\n      \"month\":\"08\",\n      \"order_ids\":\"708224790908766,708225900904656\",\n      \"status|1\": [0,1],\n      \"stageOne|1\": [0,1],\n      \"stageTow|1\":[0,1],\n    }]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b1743041-1b5a-4994-8985-d8d1923c76bf", "ruleName": "xqx-活动详情页进度数据", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/ArrivePay/detail", "filterId": "9acd3304-7249-4d6a-b706-43255ed21c3b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"id\":\"26\",\n        \"regiment_id\":\"206\",\n        \"order_num\":\"13\",\n        \"freight\":\"224.50\",\n        \"rebate\":\"22.45\",\n        \"date\":\"2023-04\",\n        \"created_at\":\"2023-04-18 10:58:02\",\n        \"update_at\":\"2023-04-18 11:06:05\",\n        \"year\":\"2023\",\n        \"month\":\"04\",\n        \"order_ids\":\"804183804900511,804183807200512,804183810008086,804183811808087,804183813108088,804183813108088,804183814608089,804183814608089,804183816408090,804183819008091,804183819008091,804183820908092,804183822708093\",\n        \"brand\":\"dp\",\n        \"status\":0,\n        \"activity_start_time\":\"2022-08-15 00:00:00\",\n        \"activity_end_time\":\"2023-12-31 23:59:59\",\n        \"config\":{\n            \"num\":[\n                10,\n                30\n            ],\n            \"percent\":[\n                0.1,\n                0.27\n            ],\n            \"eg\":null\n        }\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "148901ad-8459-46fd-b407-e2ed4208670b", "ruleName": "xqx-创建导出对账单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/createExport", "filterId": "8cf13800-bcfd-4cb7-ba76-f69c3265883d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {},\n    msg: \"success\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d84ff13a-f539-4078-9c54-6eda7a66013d", "ruleName": "xqx-导出对账单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/exportList", "filterId": "c76c91cd-7497-4ca9-94f1-17fda10fe328", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|30' : [\n      {\n        'id|+1': 1,\n        'start_time': '@date',\n        'end_time': '@date',\n        'status|1': [0,1,2],\n        'bill_file': 'https://upload.kuaidihelp.com/vhome/excel/2022/08/22/bf0a5d271914f900a92afc88276b91be.xlsx',\n        'file_name': '@csentence',\n        'create_time': '@date',\n        'update_time': '@date',\n      }\n    ],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "3a4e6511-9e77-456b-9eb6-6d5caf2bf2a3", "ruleName": "xqx-收益统计费用明细列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/OrderStatic/getMonthProfitData", "filterId": "f318c386-1568-491a-bada-599e2175c74d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|20':[\n      {\n        'time|+1': \"@date\",\n        'money|1': [0, 1.1, 3.25, 20.09],\n        \"count|1\": [0, 1, 3, 20],\n      },\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f3940109-1035-4fab-8daa-632cf91f158a", "ruleName": "xqx-获取团长默认设置", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/getDefaultConfig", "filterId": "24436cd3-4e21-4118-a422-88c66006d897", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      'audit_order|1': ['0', '1'],\n      discount: {dp: \"9.20\", jd: \"9.00\", sf: \"8.70\"},\n      \"pay_method|1\": [1,2],\n      jd_service_charge: 1.33,\n      increase_rate: {\n        yd: {\n          s_fee: '0',\n          f_fee: '2'\n        },\n        yt: {\n          s_fee: '1',\n          f_fee: '1.5'\n        },\n        sto: {\n          s_fee: '3',\n          f_fee: '1.2'\n        },\n      },\n      disabledBrands: 'sf,yd'\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "223bf180-c4c2-447e-95bc-b17222ee7c71", "ruleName": "xqx-团长设置默认配置", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/setDefaultConfig", "filterId": "70d82344-0348-469a-98d3-0030356a20ff", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {},\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "4ba75380-46e4-4dbb-bf74-a310d1413af2", "ruleName": "xqx-提交订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/submit", "filterId": "c0c3b53f-1588-4f4e-8039-5724aaa09395", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 1009,\n    msg: '该操作权限已关闭',\n    data: {\n      \n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "314d6142-d14c-438c-bada-3fc52f7be3d6", "ruleName": "xqx-获取所有品牌", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/getAllBrands", "filterId": "42d940e9-762f-4372-9aec-008c63c52d50", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {code: 0,\"data\":{\"all\":{\"brand\":\"all\",\"name\":\"不限快递公司\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/wkd/car.png\"},\"zbao\":{\"name\":\"未知\",\"short_name\":\"未知\",\"brand\":\"zbao\",\"pinyin\":\"zhongbao\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_zbao.png\"},\"team\":{\"name\":\"快递团队\",\"brand\":\"team\",\"pinyin\":\"kuaidituan<PERSON>i\",\"short_name\":\"团队\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_team.png\"},\"wzg\":{\"name\":\"微掌柜\",\"brand\":\"wzg\",\"pinyin\":\"we<PERSON><PERSON><PERSON><PERSON>\",\"short_name\":\"微掌柜\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_wzg.png\"},\"dak\":{\"name\":\"驿站\",\"brand\":\"dak\",\"pinyin\":\"yizhan\",\"short_name\":\"驿站\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_dak.png\"},\"dada\":{\"name\":\"达达快送\",\"brand\":\"dada\",\"pinyin\":\"dada\",\"short_name\":\"达达\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_dada.png\"},\"ss\":{\"name\":\"闪送\",\"brand\":\"ss\",\"pinyin\":\"shansong\",\"short_name\":\"闪送\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_ss.png\"},\"huolala\":{\"name\":\"货拉拉\",\"brand\":\"huolala\",\"pinyin\":\"huolala\",\"short_name\":\"货拉拉\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_huolala.png?ts=1591593581730\"},\"fczy\":{\"name\":\"丰城专运\",\"brand\":\"fczy\",\"pinyin\":\"fengchengzhuanyun\",\"short_name\":\"丰城专运\",\"is_express\":\"2\",\"logo_link\":\"https://cdn-img.kuaidihelp.com/brand_logo/icon_fczy.png?ts=1591593581730\"},\"yjkd\":{\"brand\":\"yjkd\",\"short\":\"yjkd\",\"pinyin\":\"youji\",\"name\":\"智能匹配优质快递品牌\",\"short_name\":\"优寄\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_yjkd.png\",\"cp_code\":null,\"is_express\":\"2\"},\"ht\":{\"brand\":\"ht\",\"short\":\"ht\",\"pinyin\":\"baishi\",\"name\":\"百世快递\",\"short_name\":\"百世\",\"tel\":\"95320\",\"common\":\"1\",\"url\":\"http://www.800bestex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ht.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jd\":{\"brand\":\"jd\",\"short\":\"jd\",\"pinyin\":\"jingdong\",\"name\":\"京东快递\",\"short_name\":\"京东\",\"tel\":\"950616\",\"common\":\"1\",\"url\":\"http://jd-ex.jd.com/#\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_jd.png?time=1572938220\",\"cp_code\":null,\"is_express\":\"1\"},\"jj\":{\"brand\":\"jj\",\"short\":\"jj\",\"pinyin\":\"jiaji\",\"name\":\"佳吉快运\",\"short_name\":\"佳吉\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.jiaji.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jy\":{\"brand\":\"jy\",\"short\":\"jy\",\"pinyin\":\"jiayi\",\"name\":\"佳怡物流\",\"short_name\":\"佳怡\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.jiayi56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"kj\":{\"brand\":\"kj\",\"short\":\"kj\",\"pinyin\":\"kuaijie\",\"name\":\"快捷快递\",\"short_name\":\"快捷\",\"tel\":\"4008333666\",\"common\":\"0\",\"url\":\"http://www.kjkd.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_kj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"kk\":{\"brand\":\"kk\",\"short\":\"kk\",\"pinyin\":\"jingguang\",\"name\":\"京广速递\",\"short_name\":\"京广\",\"tel\":\"0769-88629888\",\"common\":\"0\",\"url\":\"http://www.szkke.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_kk.png\",\"cp_code\":null,\"is_express\":\"1\"},\"lb\":{\"brand\":\"lb\",\"short\":\"lb\",\"pinyin\":\"longbang\",\"name\":\"龙邦快运\",\"short_name\":\"龙邦\",\"tel\":\"021-59218889\",\"common\":\"0\",\"url\":\"http://www.lbex.com.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_lb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"lht\":{\"brand\":\"lht\",\"short\":\"lht\",\"pinyin\":\"lianhaotong\",\"name\":\"联昊通速递 \",\"short_name\":\"联昊通\",\"tel\":\"0769-88620000\",\"common\":\"0\",\"url\":\"http://www.lhtex.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_lht.png\",\"cp_code\":null,\"is_express\":\"1\"},\"qf\":{\"brand\":\"qf\",\"short\":\"qf\",\"pinyin\":\"quanfeng\",\"name\":\"全峰快递\",\"short_name\":\"全峰\",\"tel\":\"************\",\"common\":\"1\",\"url\":\"http://www.qfkd.com.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_qf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sad\":{\"brand\":\"sad\",\"short\":\"sad\",\"pinyin\":\"saiaodi\",\"name\":\"赛澳递\",\"short_name\":\"赛澳递\",\"tel\":\"4000-345-888\",\"common\":\"0\",\"url\":\"http://www.51cod.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sad.png\",\"cp_code\":null,\"is_express\":\"1\"},\"se\":{\"brand\":\"se\",\"short\":\"se\",\"pinyin\":\"shuer\",\"name\":\"速尔快递\",\"short_name\":\"速尔\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.sure56.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_se.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sto\":{\"brand\":\"sto\",\"short\":\"sto\",\"pinyin\":\"shentong\",\"name\":\"申通快递\",\"short_name\":\"申通\",\"tel\":\"95543\",\"common\":\"1\",\"url\":\"http://www.sto.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sto.png\",\"cp_code\":null,\"is_express\":\"1\"},\"wx\":{\"brand\":\"wx\",\"short\":\"wx\",\"pinyin\":\"wanxiang\",\"name\":\"万象物流\",\"short_name\":\"万象\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.ewinshine.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_wx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xb\":{\"brand\":\"xb\",\"short\":\"xb\",\"pinyin\":\"xinbang\",\"name\":\"新邦物流\",\"short_name\":\"新邦\",\"tel\":\"4008-000-222\",\"common\":\"0\",\"url\":\"http://www.xbwl.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yc\":{\"brand\":\"yc\",\"short\":\"yc\",\"pinyin\":\"yuancheng\",\"name\":\"远成物流\",\"short_name\":\"远成\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.ycgwl.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yc.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yd\":{\"brand\":\"yd\",\"short\":\"yd\",\"pinyin\":\"yunda\",\"name\":\"韵达快递\",\"short_name\":\"韵达\",\"tel\":\"95546\",\"common\":\"1\",\"url\":\"http://www.yundaex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ys\":{\"brand\":\"ys\",\"short\":\"ys\",\"pinyin\":\"yousu\",\"name\":\"优速快递\",\"short_name\":\"优速\",\"tel\":\"400-1111-119\",\"common\":\"0\",\"url\":\"http://www.uce.cn \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ys.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yt\":{\"brand\":\"yt\",\"short\":\"yt\",\"pinyin\":\"yuantong\",\"name\":\"圆通速递\",\"short_name\":\"圆通\",\"tel\":\"95554\",\"common\":\"1\",\"url\":\"http://www.yto.net.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zt\":{\"brand\":\"zt\",\"short\":\"zt\",\"pinyin\":\"zhongtong\",\"name\":\"中通快递\",\"short_name\":\"中通\",\"tel\":\"95311\",\"common\":\"1\",\"url\":\"http://www.zto.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ztll\":{\"brand\":\"ztll\",\"short\":\"ztll\",\"pinyin\":\"zhongtongll\",\"name\":\"中通冷链\",\"short_name\":\"中通冷链\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ztll.png\",\"cp_code\":null,\"is_express\":\"1\"},\"af\":{\"brand\":\"af\",\"short\":\"af\",\"pinyin\":\"yafeng\",\"name\":\"亚风快运\",\"short_name\":\"亚风\",\"tel\":\"4001-000-002\",\"common\":\"0\",\"url\":\"http://www.airfex.net/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_af.png\",\"cp_code\":null,\"is_express\":\"1\"},\"city100\":{\"brand\":\"city100\",\"short\":\"city100\",\"pinyin\":\"chengshi100\",\"name\":\"城市100\",\"short_name\":\"城市100\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.bjcs100.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_city100.png\",\"cp_code\":null,\"is_express\":\"1\"},\"qy\":{\"brand\":\"qy\",\"short\":\"qy\",\"pinyin\":\"quanyi\",\"name\":\"全一快递\",\"short_name\":\"全一\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.unitop-apex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_qy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tt\":{\"brand\":\"tt\",\"short\":\"tt\",\"pinyin\":\"tiantian\",\"name\":\"天天快递\",\"short_name\":\"天天\",\"tel\":\"************\",\"common\":\"1\",\"url\":\"http://www.ttkdex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ems\":{\"brand\":\"ems\",\"short\":\"ems\",\"pinyin\":\"ems\",\"name\":\"EMS\",\"short_name\":\"EMS\",\"tel\":\"11183\",\"common\":\"1\",\"url\":\"http://www.ems.com.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ems.png\",\"cp_code\":null,\"is_express\":\"1\"},\"rfd\":{\"brand\":\"rfd\",\"short\":\"rfd\",\"pinyin\":\"rufengda\",\"name\":\"如风达\",\"short_name\":\"如风达\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.rufengda.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_rfd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zjs\":{\"brand\":\"zjs\",\"short\":\"zjs\",\"pinyin\":\"zhaijisong\",\"name\":\"宅急送\",\"short_name\":\"宅急送\",\"tel\":\"400-6789-000\",\"common\":\"0\",\"url\":\"http://www.zjs.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zjs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fedex\":{\"brand\":\"fedex\",\"short\":\"fedex\",\"pinyin\":\"fedex\",\"name\":\"中国联邦快递\",\"short_name\":\"中国联邦\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.fedex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fedex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sf\":{\"brand\":\"sf\",\"short\":\"sf\",\"pinyin\":\"shunfeng\",\"name\":\"顺丰速运\",\"short_name\":\"顺丰\",\"tel\":\"95338\",\"common\":\"1\",\"url\":\"http://www.sf-express.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sf.png\",\"cp_code\":\"SF\",\"is_express\":\"1\"},\"dhl\":{\"brand\":\"dhl\",\"short\":\"dhl\",\"pinyin\":\"dhl\",\"name\":\"dhl中国\",\"short_name\":\"dhl中国\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.cn.dhl.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dhl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zy\":{\"brand\":\"zy\",\"short\":\"zy\",\"pinyin\":\"zhengyi\",\"name\":\"增益速递\",\"short_name\":\"增益\",\"tel\":\"4008-456-789\",\"common\":\"0\",\"url\":\"http://www.zeny-express.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dp\":{\"brand\":\"dp\",\"short\":\"dp\",\"pinyin\":\"debang\",\"name\":\"德邦快递\",\"short_name\":\"德邦\",\"tel\":\"95353\",\"common\":\"0\",\"url\":\"https://www.deppon.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"rrs\":{\"brand\":\"rrs\",\"short\":\"rrs\",\"pinyin\":\"ririshun\",\"name\":\"日日顺物流\",\"short_name\":\"日日顺\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"www.rrs.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_rrs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hmj\":{\"brand\":\"hmj\",\"short\":\"hmj\",\"pinyin\":\"huangmaja\",\"name\":\"黄马甲物流\",\"short_name\":\"黄马甲\",\"tel\":\"029-96128\",\"common\":\"0\",\"url\":\"http://www.huangmajia.com \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hmj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xy\":{\"brand\":\"xy\",\"short\":\"xy\",\"pinyin\":\"xinyi\",\"name\":\"心怡物流\",\"short_name\":\"心怡\",\"tel\":\"4008-02-4008\",\"common\":\"0\",\"url\":\"tms.alog.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fedexInter\":{\"brand\":\"fedexInter\",\"short\":\"fedexInter\",\"pinyin\":\"fedexInter\",\"name\":\"联邦国际\",\"short_name\":\"联邦国际\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.fedex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fedex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tdhy\":{\"brand\":\"tdhy\",\"short\":\"tdhy\",\"pinyin\":\"tiandihuayu\",\"name\":\"天地华宇\",\"short_name\":\"天地华宇\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.hoau.net\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tdhy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hw\":{\"brand\":\"hw\",\"short\":\"hw\",\"pinyin\":\"huiwen\",\"name\":\"汇文配送\",\"short_name\":\"汇文\",\"tel\":\"400-0000-266\",\"common\":\"0\",\"url\":\"http://www.hefeihuiwen.com/ \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hw.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dtd\":{\"brand\":\"dtd\",\"short\":\"dtd\",\"pinyin\":\"menduimen\",\"name\":\"门对门\",\"short_name\":\"门对门\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.szdod.com \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dtd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ql\":{\"brand\":\"ql\",\"short\":\"ql\",\"pinyin\":\"qinglv\",\"name\":\"青旅物流\",\"short_name\":\"青旅\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.zqlwl.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ql.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ezs\":{\"brand\":\"ezs\",\"short\":\"ezs\",\"pinyin\":\"suda\",\"name\":\"速达快递\",\"short_name\":\"速达\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.ez2cn.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ezs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zzjh\":{\"brand\":\"zzjh\",\"short\":\"zzjh\",\"pinyin\":\"jianhua\",\"name\":\"郑州建华\",\"short_name\":\"建华\",\"tel\":\"0371-65995266\",\"common\":\"0\",\"url\":\"http://www.zzjhtd.com/ \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zzjh.png\",\"cp_code\":null,\"is_express\":\"1\"},\"aae\":{\"brand\":\"aae\",\"short\":\"aae\",\"pinyin\":\"aae\",\"name\":\"aae全球速递\",\"short_name\":\"aae\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://cn.aaeweb.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_aae.png\",\"cp_code\":null,\"is_express\":\"1\"},\"aol\":{\"brand\":\"aol\",\"short\":\"aol\",\"pinyin\":\"aol\",\"name\":\"aol澳通速递\",\"short_name\":\"aol\",\"tel\":\"0424047888\",\"common\":\"0\",\"url\":\"http://www.aol-au.com/ \",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_aol.png\",\"cp_code\":null,\"is_express\":\"1\"},\"gt\":{\"brand\":\"gt\",\"short\":\"gt\",\"pinyin\":\"guotong\",\"name\":\"国通快递\",\"short_name\":\"国通\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.gto365.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_gt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"pj\":{\"brand\":\"pj\",\"short\":\"pj\",\"pinyin\":\"pinjun\",\"name\":\"品骏（唯品会）\",\"short_name\":\"品骏（唯品会）\",\"tel\":\"400-9789-888\",\"common\":\"0\",\"url\":\"http://www.pjbest.com/index\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_pj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ups\":{\"brand\":\"ups\",\"short\":\"ups\",\"pinyin\":\"zhongguoups\",\"name\":\"中国UPS\",\"short_name\":\"UPS\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.ups.com/cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ups.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cb\":{\"brand\":\"cb\",\"short\":\"cb\",\"pinyin\":\"chengbang\",\"name\":\"晟邦物流\",\"short_name\":\"晟邦\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.3856.cc/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"bt\":{\"brand\":\"bt\",\"short\":\"bt\",\"pinyin\":\"yimidida\",\"name\":\"壹米滴答\",\"short_name\":\"壹米滴答\",\"tel\":\"0531-89005678\",\"common\":\"0\",\"url\":\"http://www.jnbenteng.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_bt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dd\":{\"brand\":\"dd\",\"short\":\"dd\",\"pinyin\":\"dada\",\"name\":\"大达物流\",\"short_name\":\"大达\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.idada56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dt\":{\"brand\":\"dt\",\"short\":\"dt\",\"pinyin\":\"datian\",\"name\":\"大田物流\",\"short_name\":\"大田\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.dtw.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xf\":{\"brand\":\"xf\",\"short\":\"xf\",\"pinyin\":\"xinfeng\",\"name\":\"信丰物流\",\"short_name\":\"信丰\",\"tel\":\"0769-81510412\",\"common\":\"0\",\"url\":\"http://www.xf-express.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"bdt\":{\"brand\":\"bdt\",\"short\":\"bdt\",\"pinyin\":\"badatong\",\"name\":\"八达通\",\"short_name\":\"八达通\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.8dt.us/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_bdt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ge\":{\"brand\":\"ge\",\"short\":\"ge\",\"pinyin\":\"huanqiu\",\"name\":\"环球速运\",\"short_name\":\"环球\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.globle-express.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ge.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jde\":{\"brand\":\"jde\",\"short\":\"jde\",\"pinyin\":\"junda\",\"name\":\"骏达快递\",\"short_name\":\"骏达\",\"tel\":\"**************\",\"common\":\"0\",\"url\":\"http://www.jdexpressusa.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jde.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ky\":{\"brand\":\"ky\",\"short\":\"ky\",\"pinyin\":\"kuayue\",\"name\":\"跨越速运\",\"short_name\":\"跨越\",\"tel\":\"4008-098-098\",\"common\":\"0\",\"url\":\"http://www.ky-express.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ky.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sh\":{\"brand\":\"sh\",\"short\":\"sh\",\"pinyin\":\"shenghui\",\"name\":\"盛辉物流\",\"short_name\":\"盛辉\",\"tel\":\"4008-222222\",\"common\":\"0\",\"url\":\"http://www.shenghui56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sh.png\",\"cp_code\":null,\"is_express\":\"1\"},\"wt\":{\"brand\":\"wt\",\"short\":\"wt\",\"pinyin\":\"yuntong\",\"name\":\"运通速运\",\"short_name\":\"运通\",\"tel\":\"0769-81156999\",\"common\":\"0\",\"url\":\"http://www.wto-ex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_wt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xd\":{\"brand\":\"xd\",\"short\":\"xd\",\"pinyin\":\"xunda\",\"name\":\"迅达速递\",\"short_name\":\"迅达\",\"tel\":\"（03）9544 7322/(\",\"common\":\"0\",\"url\":\"http://track.xdexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yhc\":{\"brand\":\"yhc\",\"short\":\"yhc\",\"pinyin\":\"yihaocang\",\"name\":\"1号仓\",\"short_name\":\"一号仓\",\"tel\":\"0755-89391959\",\"common\":\"0\",\"url\":\"http://www.1hcang.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yhc.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cnp\":{\"brand\":\"cnp\",\"short\":\"cnp\",\"pinyin\":\"zhongyou\",\"name\":\"中邮快递\",\"short_name\":\"中邮\",\"tel\":\"************ \",\"common\":\"0\",\"url\":\"http://www.cnpex.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cnp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hre\":{\"brand\":\"hre\",\"short\":\"hre\",\"pinyin\":\"gaotiesudi\",\"name\":\"高铁速递\",\"short_name\":\"高铁\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.hre-e.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hre.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ax\":{\"brand\":\"ax\",\"short\":\"ax\",\"pinyin\":\"anxun\",\"name\":\"安迅物流\",\"short_name\":\"安迅\",\"tel\":\"4008179880\",\"common\":\"0\",\"url\":\"http://www.anxl.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ax.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zs\":{\"brand\":\"zs\",\"short\":\"zs\",\"pinyin\":\"zhunshi\",\"name\":\"准实快运\",\"short_name\":\"准实\",\"tel\":\"4000-200-288\",\"common\":\"0\",\"url\":\"http://www.lnzsky.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ccd\":{\"brand\":\"ccd\",\"short\":\"ccd\",\"pinyin\":\"cichenda\",\"name\":\"次晨达物流\",\"short_name\":\"次晨达\",\"tel\":\"0371-55002990\",\"common\":\"0\",\"url\":\"http://www.ccd56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ccd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cg\":{\"brand\":\"cg\",\"short\":\"cg\",\"pinyin\":\"chengguang\",\"name\":\"程光快递\",\"short_name\":\"程光\",\"tel\":\"0064 9 948 2780\",\"common\":\"0\",\"url\":\"http://www.flywayex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ch\":{\"brand\":\"ch\",\"short\":\"ch\",\"pinyin\":\"chunhui\",\"name\":\"春辉物流\",\"short_name\":\"春辉\",\"tel\":\"400-81192-21\",\"common\":\"0\",\"url\":\"http://www.ch56.cc/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ch.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cky\":{\"brand\":\"cky\",\"short\":\"cky\",\"pinyin\":\"chukouyi\",\"name\":\"出口易跨境物流\",\"short_name\":\"出口易\",\"tel\":\"4006 908 223\",\"common\":\"0\",\"url\":\"http://www.chukou1.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cky.png\",\"cp_code\":null,\"is_express\":\"1\"},\"coe\":{\"brand\":\"coe\",\"short\":\"coe\",\"pinyin\":\"dongfang\",\"name\":\"东方快递\",\"short_name\":\"东方\",\"tel\":\"+86-755-8357500\",\"common\":\"0\",\"url\":\"http://www.coe.com.hk/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_coe.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ct\":{\"brand\":\"ct\",\"short\":\"ct\",\"pinyin\":\"chengtong\",\"name\":\"诚通物流\",\"short_name\":\"诚通\",\"tel\":\"0371-55331001\",\"common\":\"0\",\"url\":\"http://www.ctg56.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ct.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cx\":{\"brand\":\"cx\",\"short\":\"cx\",\"pinyin\":\"chuanxi\",\"name\":\"传喜物流\",\"short_name\":\"传喜\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.cxcod.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yx\":{\"brand\":\"yx\",\"short\":\"yx\",\"pinyin\":\"yuxin\",\"name\":\"宇鑫物流\",\"short_name\":\"宇鑫\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.yx56.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dby\":{\"brand\":\"dby\",\"short\":\"dby\",\"pinyin\":\"dibiyi\",\"name\":\"迪比翼快递\",\"short_name\":\"迪比翼\",\"tel\":\"0755-8829 7707 \",\"common\":\"0\",\"url\":\"http://www.szdpex.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dby.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ddw\":{\"brand\":\"ddw\",\"short\":\"ddw\",\"pinyin\":\"dadao\",\"name\":\"大道物流\",\"short_name\":\"大道\",\"tel\":\"027-83550339\",\"common\":\"0\",\"url\":\"http://www.ddwl.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ddw.png\",\"cp_code\":null,\"is_express\":\"1\"},\"df\":{\"brand\":\"df\",\"short\":\"df\",\"pinyin\":\"defang\",\"name\":\"德方物流\",\"short_name\":\"徳方\",\"tel\":\"0551-65883415\",\"common\":\"0\",\"url\":\"http://www.ahdf56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_df.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fb\":{\"brand\":\"fb\",\"short\":\"fb\",\"pinyin\":\"feibao\",\"name\":\"飞豹快运\",\"short_name\":\"飞豹\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.crlg.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fd\":{\"brand\":\"fd\",\"short\":\"fd\",\"pinyin\":\"Fardar\",\"name\":\"Fardar\",\"short_name\":\"Fardar\",\"tel\":\"021-39281555\",\"common\":\"0\",\"url\":\"http://www.fardar.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fy\":{\"brand\":\"fy\",\"short\":\"fy\",\"pinyin\":\"feiying\",\"name\":\"飞鹰物流\",\"short_name\":\"飞鹰\",\"tel\":\"0371-960065\",\"common\":\"0\",\"url\":\"http://www.hnfy56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"gd\":{\"brand\":\"gd\",\"short\":\"gd\",\"pinyin\":\"guanda\",\"name\":\"冠达快递\",\"short_name\":\"冠达\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.gda-e.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_gd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"gk\":{\"brand\":\"gk\",\"short\":\"gk\",\"pinyin\":\"gangkuai\",\"name\":\"港快速递\",\"short_name\":\"港快\",\"tel\":\"400-11-33333\",\"common\":\"0\",\"url\":\"http://www.gksd.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_gk.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hl\":{\"brand\":\"hl\",\"short\":\"hl\",\"pinyin\":\"henglu\",\"name\":\"恒路物流\",\"short_name\":\"恒路\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"https://www.e-henglu.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hly\":{\"brand\":\"hly\",\"short\":\"hly\",\"pinyin\":\"haolaiyun\",\"name\":\"好来运快递\",\"short_name\":\"好来运\",\"tel\":\"020-86293333\",\"common\":\"0\",\"url\":\"http://www.hlyex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hly.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hq\":{\"brand\":\"hq\",\"short\":\"hq\",\"pinyin\":\"huaqi\",\"name\":\"华企快运\",\"short_name\":\"华企\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.hqkd.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hq.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yw\":{\"brand\":\"yw\",\"short\":\"yw\",\"pinyin\":\"yanwen\",\"name\":\"燕文物流\",\"short_name\":\"燕文\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.yw56.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yw.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ydt\":{\"brand\":\"ydt\",\"short\":\"ydt\",\"pinyin\":\"yitongda\",\"name\":\"易达通快递\",\"short_name\":\"易通达\",\"tel\":\"09-8388681\",\"common\":\"0\",\"url\":\"http://www.qexpress.co.nz/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ydt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ykm\":{\"brand\":\"ykm\",\"short\":\"ykm\",\"pinyin\":\"yikeman\",\"name\":\"易客满\",\"short_name\":\"易客满\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.ecmsglobal.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ykm.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yhx\":{\"brand\":\"yhx\",\"short\":\"yhx\",\"pinyin\":\"yihaoxian\",\"name\":\"一号线国际速递\",\"short_name\":\"一号线\",\"tel\":\"1300 546 310\",\"common\":\"0\",\"url\":\"http://www.line-1.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yhx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tnt\":{\"brand\":\"tnt\",\"short\":\"tnt\",\"pinyin\":\"tnt\",\"name\":\"TNT\",\"short_name\":\"TNT\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"https://www.tnt.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tnt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jg\":{\"brand\":\"jg\",\"short\":\"jg\",\"pinyin\":\"jingguang\",\"name\":\"景光物流\",\"short_name\":\"景光\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.jgwl.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jhe\":{\"brand\":\"jhe\",\"short\":\"jhe\",\"pinyin\":\"jiahuier\",\"name\":\"佳惠尔快递物流\",\"short_name\":\"佳惠尔\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.jhekd.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jhe.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ld\":{\"brand\":\"ld\",\"short\":\"ld\",\"pinyin\":\"lindong\",\"name\":\"林道国际\",\"short_name\":\"林道\",\"tel\":\" 4008200112 \",\"common\":\"0\",\"url\":\"http://www.ldxpress.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ld.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ljs\":{\"brand\":\"ljs\",\"short\":\"ljs\",\"pinyin\":\"lijisong\",\"name\":\"立即送\",\"short_name\":\"立即送\",\"tel\":\"028-86740011\",\"common\":\"0\",\"url\":\"http://www.cdljs.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ljs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"max\":{\"brand\":\"max\",\"short\":\"max\",\"pinyin\":\"maisu\",\"name\":\"澳洲迈速\",\"short_name\":\"迈速\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.maxeedexpress.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_max.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ml\":{\"brand\":\"ml\",\"short\":\"ml\",\"pinyin\":\"mingliang\",\"name\":\"明亮物流\",\"short_name\":\"明亮\",\"tel\":\"400-0356-568\",\"common\":\"0\",\"url\":\"http://www.szml56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ml.png\",\"cp_code\":null,\"is_express\":\"1\"},\"oto\":{\"brand\":\"oto\",\"short\":\"oto\",\"pinyin\":\"zhongou\",\"name\":\"中欧快运\",\"short_name\":\"中欧\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.otobv.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_oto.png\",\"cp_code\":null,\"is_express\":\"1\"},\"pad\":{\"brand\":\"pad\",\"short\":\"pad\",\"pinyin\":\"pinganda\",\"name\":\"平安达腾飞快递\",\"short_name\":\"平安达\",\"tel\":\"0769-85712366\",\"common\":\"0\",\"url\":\"http://www.padtf.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_pad.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ry\":{\"brand\":\"ry\",\"short\":\"ry\",\"pinyin\":\"riyu\",\"name\":\"日昱物流\",\"short_name\":\"日昱\",\"tel\":\"4009-888-588\",\"common\":\"0\",\"url\":\"http://www.rywl.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ry.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sa\":{\"brand\":\"sa\",\"short\":\"sa\",\"pinyin\":\"shengan\",\"name\":\"圣安物流\",\"short_name\":\"圣安\",\"tel\":\"400-6618-169\",\"common\":\"0\",\"url\":\"http://www.sa56.net/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sa.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sj\":{\"brand\":\"sj\",\"short\":\"sj\",\"pinyin\":\"sujie\",\"name\":\"速捷快递\",\"short_name\":\"速捷\",\"tel\":\"0371-66612770\",\"common\":\"0\",\"url\":\"http://www.sujievip.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sjwl\":{\"brand\":\"sjwl\",\"short\":\"sjwl\",\"pinyin\":\"suijia\",\"name\":\"穗佳物流\",\"short_name\":\"穗佳\",\"tel\":\"020-28221617\",\"common\":\"0\",\"url\":\"http://www.suijiawl.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sjwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"suf\":{\"brand\":\"suf\",\"short\":\"suf\",\"pinyin\":\"sufang\",\"name\":\"速方国际物流\",\"short_name\":\"速方\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.sufast.net/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_suf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cre\":{\"brand\":\"cre\",\"short\":\"cre\",\"pinyin\":\"zhongtie\",\"name\":\"中铁快运\",\"short_name\":\"中铁\",\"tel\":\"95572\",\"common\":\"0\",\"url\":\"http://www.cre.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cre.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zmkm\":{\"brand\":\"zmkm\",\"short\":\"zmkm\",\"pinyin\":\"zhimakaimen\",\"name\":\"芝麻开门\",\"short_name\":\"芝麻开门\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.zmkmex.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zmkm.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sut\":{\"brand\":\"sut\",\"short\":\"sut\",\"pinyin\":\"sutong\",\"name\":\"速通物流\",\"short_name\":\"速通\",\"tel\":\"************\",\"common\":\"0\",\"url\":\"http://www.sut56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sut.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zjb\":{\"brand\":\"zjb\",\"short\":\"zjb\",\"pinyin\":\"zhaijibian\",\"name\":\"宅急便\",\"short_name\":\"宅急便\",\"tel\":\"4008-56-56-56\",\"common\":\"0\",\"url\":\"http://sh.cn.ta-q-bin.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zjb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ane\":{\"brand\":\"ane\",\"short\":\"ane\",\"pinyin\":\"anneng\",\"name\":\"安能快运\",\"short_name\":\"安能快运\",\"tel\":\"************\",\"common\":\"1\",\"url\":\"http://www.ane56.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ane.png\",\"cp_code\":null,\"is_express\":\"1\"},\"anekd\":{\"brand\":\"anekd\",\"short\":\"anekd\",\"pinyin\":\"anneng\",\"name\":\"安能快递\",\"short_name\":\"安能快递\",\"tel\":\"************\",\"common\":\"1\",\"url\":\"http://www.ane66.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ane2.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sn\":{\"brand\":\"sn\",\"short\":\"sn\",\"pinyin\":\"suning\",\"name\":\"苏宁快递\",\"short_name\":\"苏宁\",\"tel\":\"95315\",\"common\":\"0\",\"url\":\"http://wuliu.suning.com/slp/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sn.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ztky\":{\"brand\":\"ztky\",\"short\":\"ztky\",\"pinyin\":\"zhongtong\",\"name\":\"中通快运\",\"short_name\":\"中通\",\"tel\":\"95311\",\"common\":\"1\",\"url\":\"http://www.zto56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ztky.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dpky\":{\"brand\":\"dpky\",\"short\":\"dpky\",\"pinyin\":\"debangkuaiyun\",\"name\":\"德邦快运\",\"short_name\":\"德邦快运\",\"tel\":\"95353\",\"common\":\"0\",\"url\":\"https://www.deppon.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"post\":{\"brand\":\"post\",\"short\":\"post\",\"pinyin\":\"youzheng\",\"name\":\"邮政快包\",\"short_name\":\"邮政快包\",\"tel\":\"11185\",\"common\":\"0\",\"url\":\"http://yjcx.chinapost.com.cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_post.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yad\":{\"brand\":\"yad\",\"short\":\"yad\",\"pinyin\":\"yuananda\",\"name\":\"源安达快递\",\"short_name\":\"源安达\",\"tel\":\"0769-85157789\",\"common\":\"0\",\"url\":\"http://www.yadex.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yad.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ewe\":{\"brand\":\"ewe\",\"short\":\"ewe\",\"pinyin\":\"ewe\",\"name\":\"EWE全球快递\",\"short_name\":\"EWE\",\"tel\":\"1300-09-6655\",\"common\":\"0\",\"url\":\"https://www.ewe.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ewe.png\",\"cp_code\":null,\"is_express\":\"1\"},\"kbtc\":{\"brand\":\"kbtc\",\"short\":\"kbtc\",\"pinyin\":\"kuaibaotongcheng\",\"name\":\"快宝同城\",\"short_name\":\"快宝同城\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://kuaidihelp.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_kbtc.png\",\"cp_code\":null,\"is_express\":\"1\"},\"otp\":{\"brand\":\"otp\",\"short\":\"otp\",\"pinyin\":\"chengnuoda\",\"name\":\"承诺达特快\",\"short_name\":\"承诺达\",\"tel\":\"4001895554\",\"common\":\"1\",\"url\":\"https://m.kuaidihelp.com/help/otp\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_otp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yjt\":{\"brand\":\"yjt\",\"short\":\"yjt\",\"pinyin\":\"yijiantong\",\"name\":\"一键通快递\",\"short_name\":\"一键通\",\"tel\":\"\",\"common\":\"1\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yjt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"mzgj\":{\"brand\":\"mzgj\",\"short\":\"mzgj\",\"pinyin\":\"mingzhangguoji\",\"name\":\"明彰国际快递\",\"short_name\":\"明彰国际\",\"tel\":\"021-34120053\",\"common\":\"1\",\"url\":\"http://www.mingz-tech.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_mzgj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sfky\":{\"brand\":\"sfky\",\"short\":\"sfky\",\"pinyin\":\"shunfengkuaiyun\",\"name\":\"顺丰快运\",\"short_name\":\"顺丰快运\",\"tel\":\"95338\",\"common\":\"0\",\"url\":\"https://www.sf-express.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sfky.png\",\"cp_code\":\"\",\"is_express\":\"1\"},\"jt\":{\"brand\":\"jt\",\"short\":\"jt\",\"pinyin\":\"jitu\",\"name\":\"极兔速递\",\"short_name\":\"极兔\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_jt.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dn\":{\"brand\":\"dn\",\"short\":\"dn\",\"pinyin\":\"danniao\",\"name\":\"丹鸟快递\",\"short_name\":\"丹鸟\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_dn.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sxjd\":{\"brand\":\"sxjd\",\"short\":\"sxjd\",\"pinyin\":\"sunxinjieda\",\"name\":\"顺心捷达\",\"short_name\":\"顺心捷达\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://sxne.sxjdfreight.com\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_sxjd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zykd\":{\"brand\":\"zykd\",\"short\":\"zykd\",\"pinyin\":\"zhongyou\",\"name\":\"众邮快递\",\"short_name\":\"众邮\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_zykd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tmcs\":{\"brand\":\"tmcs\",\"short\":\"tmcs\",\"pinyin\":\"tmcs\",\"name\":\"天猫超市\",\"short_name\":\"\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_tmcs.png\",\"cp_code\":null,\"is_express\":\"2\"},\"ddmc\":{\"brand\":\"ddmc\",\"short\":\"ddmc\",\"pinyin\":\"ddmc\",\"name\":\"多多买菜\",\"short_name\":\"多多\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_ddmc.png\",\"cp_code\":null,\"is_express\":\"2\"},\"mtyx\":{\"brand\":\"mtyx\",\"short\":\"mtyx\",\"pinyin\":\"mtyx\",\"name\":\"美团优选\",\"short_name\":\"美团\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_mtyx.png\",\"cp_code\":null,\"is_express\":\"2\"},\"cxyx\":{\"brand\":\"cxyx\",\"short\":\"cxyx\",\"pinyin\":\"cxyx\",\"name\":\"橙心优选\",\"short_name\":\"橙心\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_cxyx.png\",\"cp_code\":null,\"is_express\":\"2\"},\"xsyx\":{\"brand\":\"xsyx\",\"short\":\"xsyx\",\"pinyin\":\"xsyx\",\"name\":\"兴盛优选\",\"short_name\":\"兴盛\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_xsyx.png\",\"cp_code\":null,\"is_express\":\"2\"},\"tcsh\":{\"brand\":\"tcsh\",\"short\":\"tcsh\",\"pinyin\":\"tcsh\",\"name\":\"同程生活\",\"short_name\":\"同程\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_tcsh.png\",\"cp_code\":null,\"is_express\":\"2\"},\"sht\":{\"brand\":\"sht\",\"short\":\"sht\",\"pinyin\":\"sht\",\"name\":\"十荟团\",\"short_name\":\"十荟团\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_sht.png\",\"cp_code\":null,\"is_express\":\"2\"},\"fw\":{\"brand\":\"fw\",\"short\":\"fw\",\"pinyin\":\"fengwang\",\"name\":\"丰网速运\",\"short_name\":\"丰网\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_fw.png\",\"cp_code\":null,\"is_express\":\"1\"},\"htky\":{\"brand\":\"htky\",\"short\":\"htky\",\"pinyin\":\"baishiky\",\"name\":\"百世快运\",\"short_name\":\"百世快运\",\"tel\":\"95320\",\"common\":\"1\",\"url\":\"http://www.800bestex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ht.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ydky\":{\"brand\":\"ydky\",\"short\":\"ydky\",\"pinyin\":\"yundakuaiyun\",\"name\":\"韵达快运\",\"short_name\":\"韵达快运\",\"tel\":\"95320\",\"common\":\"1\",\"url\":\"http://www.yunda56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ydky.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dswl\":{\"brand\":\"dswl\",\"short\":\"dswl\",\"pinyin\":\"dsuwuliu\",\"name\":\"D速物流\",\"short_name\":\"D速\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.d-exp.net/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dswl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tjs\":{\"brand\":\"tjs\",\"short\":\"tjs\",\"pinyin\":\"tjs\",\"name\":\"特急送\",\"short_name\":\"特急送\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.lntjs.cn/web_index/index.aspx\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tjs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sbd\":{\"brand\":\"sbd\",\"short\":\"sbd\",\"pinyin\":\"sbd\",\"name\":\"速必达物流\",\"short_name\":\"速必达\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://scc.4006005656.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sbd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"adwl\":{\"brand\":\"adwl\",\"short\":\"adwl\",\"pinyin\":\"adwl\",\"name\":\"安得物流\",\"short_name\":\"安得\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.annto.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_adwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zzsy\":{\"brand\":\"zzsy\",\"short\":\"zzsy\",\"pinyin\":\"zuozhisuyun\",\"name\":\"卓志速运\",\"short_name\":\"卓志速运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.esdex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zzsy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"usps\":{\"brand\":\"usps\",\"short\":\"usps\",\"pinyin\":\"usps\",\"name\":\"USPS\",\"short_name\":\"USPS\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://zh.usps.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_usps.png\",\"cp_code\":null,\"is_express\":\"1\"},\"azfy\":{\"brand\":\"azfy\",\"short\":\"azfy\",\"pinyin\":\"azfy\",\"name\":\"澳洲飞跃物流\",\"short_name\":\"澳洲飞跃物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.rlgaustralia.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_azfy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"stky\":{\"brand\":\"stky\",\"short\":\"stky\",\"pinyin\":\"stky\",\"name\":\"苏通快运\",\"short_name\":\"苏通快运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\\thttp://www.zjstky.com/index.htm\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_stky.png\",\"cp_code\":null,\"is_express\":\"1\"},\"wtp\":{\"brand\":\"wtp\",\"short\":\"wtp\",\"pinyin\":\"weitepai\",\"name\":\"微特派\",\"short_name\":\"微特派\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_wtp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"mkInter\":{\"brand\":\"mkInter\",\"short\":\"mkInter\",\"pinyin\":\"mkInter\",\"name\":\"美快国际物流\",\"short_name\":\"美快国际\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_mkInter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ubonex\":{\"brand\":\"ubonex\",\"short\":\"ubonex\",\"pinyin\":\"ubonex\",\"name\":\"优邦速运\",\"short_name\":\"优邦速运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ubonex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"suteng\":{\"brand\":\"suteng\",\"short\":\"suteng\",\"pinyin\":\"suteng\",\"name\":\"速腾快递\",\"short_name\":\"速腾\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_suteng.png\",\"cp_code\":null,\"is_express\":\"1\"},\"spkd\":{\"brand\":\"spkd\",\"short\":\"spkd\",\"pinyin\":\"spkd\",\"name\":\"速派快递\",\"short_name\":\"速派\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_spkd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"lhkd\":{\"brand\":\"lhkd\",\"short\":\"lhkd\",\"pinyin\":\"lhkd\",\"name\":\"联合快递\",\"short_name\":\"联合\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_lhkd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dml\":{\"brand\":\"dml\",\"short\":\"dml\",\"pinyin\":\"dml\",\"name\":\"大马鹿\",\"short_name\":\"大马鹿\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dml.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dsf\":{\"brand\":\"dsf\",\"short\":\"dsf\",\"pinyin\":\"dsf\",\"name\":\"递四方\",\"short_name\":\"递四方\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dsf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zyzg\":{\"brand\":\"zyzg\",\"short\":\"zyzg\",\"pinyin\":\"zyzg\",\"name\":\"转运中国\",\"short_name\":\"转运中国\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zyzg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"auexpress\":{\"brand\":\"auexpress\",\"short\":\"auexpress\",\"pinyin\":\"auexpress\",\"name\":\"澳邮中国快运\",\"short_name\":\"澳邮中国快运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_auexpress.png\",\"cp_code\":null,\"is_express\":\"1\"},\"postx\":{\"brand\":\"postx\",\"short\":\"postx\",\"pinyin\":\"youzheng\",\"name\":\"邮政快包\",\"short_name\":\"邮政快包\",\"tel\":\"11185\",\"common\":\"0\",\"url\":\"http://yjcx.chinapost.com.cn\",\"logo_link\":\"https://img.kuaidihelp.com/brand_logo/icon_post.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sto44\":{\"brand\":\"sto44\",\"short\":\"sto44\",\"pinyin\":\"shentong44\",\"name\":\"申通44\",\"short_name\":\"申通44\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sto.png\",\"cp_code\":null,\"is_express\":\"2\"},\"xlobo\":{\"brand\":\"xlobo\",\"short\":\"xlobo\",\"pinyin\":\"xlobo\",\"name\":\"Xlobo贝海国际\",\"short_name\":\"xlobo\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.xlobo.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xlobo.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jiuyes\":{\"brand\":\"jiuyes\",\"short\":\"jiuyes\",\"pinyin\":\"jiuyes\",\"name\":\"九曳供应链\",\"short_name\":\"jiuyes\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.jiuyescm.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jiuyes.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hxwl\":{\"brand\":\"hxwl\",\"short\":\"hxwl\",\"pinyin\":\"hxwl\",\"name\":\"海信物流\",\"short_name\":\"海信物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.savor.com.cn/wuliu/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hxwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tmxd\":{\"brand\":\"tmxd\",\"short\":\"tmxd\",\"pinyin\":\"tmxd\",\"name\":\"天马迅达\",\"short_name\":\"天马迅达\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.worldcps.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tmxd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zyehq\":{\"brand\":\"zyehq\",\"short\":\"zyehq\",\"pinyin\":\"zyehq\",\"name\":\"中远e环球\",\"short_name\":\"中远e环球\",\"tel\":\"\",\"common\":\"1\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zyehq.png\",\"cp_code\":null,\"is_express\":\"1\"},\"btexpress\":{\"brand\":\"btexpress\",\"short\":\"btexpress\",\"pinyin\":\"btexpress\",\"name\":\"邦泰物流\",\"short_name\":\"邦泰物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.bt-express.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_btexpress.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zhkd\":{\"brand\":\"zhkd\",\"short\":\"zhkd\",\"pinyin\":\"zhkd\",\"name\":\"中环快递\",\"short_name\":\"中环快递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.zhexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zhkd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fjinter\":{\"brand\":\"fjinter\",\"short\":\"fjinter\",\"pinyin\":\"fjinter\",\"name\":\"泛捷国际速递\",\"short_name\":\"泛捷国际速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.asendia.hk/cn\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fjinter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cjinter\":{\"brand\":\"cjinter\",\"short\":\"cjinter\",\"pinyin\":\"cjinter\",\"name\":\"长江国际速递\",\"short_name\":\"长江国际速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.changjiangexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cjinter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hssy\":{\"brand\":\"hssy\",\"short\":\"hssy\",\"pinyin\":\"hssy\",\"name\":\"汇森速运\",\"short_name\":\"汇森速运\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.lbex.com.cn/survey\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hssy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xjwl\":{\"brand\":\"xjwl\",\"short\":\"xjwl\",\"pinyin\":\"xjwl\",\"name\":\"新杰物流\",\"short_name\":\"新杰\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.sunjex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xjwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fzgj\":{\"brand\":\"fzgj\",\"short\":\"fzgj\",\"pinyin\":\"fzgj\",\"name\":\"方舟国际速递\",\"short_name\":\"方舟国际速递\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://a.arkexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fzgj.png\",\"cp_code\":null,\"is_express\":\"1\"},\"superb\":{\"brand\":\"superb\",\"short\":\"superb\",\"pinyin\":\"superb\",\"name\":\"Superb-Express\",\"short_name\":\"Superb-Express\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.superb-express.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_superb.png\",\"cp_code\":null,\"is_express\":\"1\"},\"crazyexpress\":{\"brand\":\"crazyexpress\",\"short\":\"crazyexpress\",\"pinyin\":\"fengkuang\",\"name\":\"疯狂快递\",\"short_name\":\"疯狂快递\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.crazyexpress.ca/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_crazyexpress.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zysf\":{\"brand\":\"zysf\",\"short\":\"zysf\",\"pinyin\":\"zysf\",\"name\":\"转运四方\",\"short_name\":\"转运四方\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.transrush.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zysf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"bmwl\":{\"brand\":\"bmwl\",\"short\":\"bmwl\",\"pinyin\":\"bmwl\",\"name\":\"斑马物流\",\"short_name\":\"斑马物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_bmwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sanhu\":{\"brand\":\"sanhu\",\"short\":\"sanhu\",\"pinyin\":\"sanhu\",\"name\":\"叁虎物流\",\"short_name\":\"叁虎物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.sanhuwuliu.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sanhu.png\",\"cp_code\":null,\"is_express\":\"1\"},\"aramex\":{\"brand\":\"aramex\",\"short\":\"aramex\",\"pinyin\":\"aramex\",\"name\":\"Aramex\",\"short_name\":\"Aramex\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.aramex.com/cn/zh\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_aramex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ftd\":{\"brand\":\"ftd\",\"short\":\"ftd\",\"pinyin\":\"ftd\",\"name\":\"富腾达国际货运\",\"short_name\":\"富腾达国际货运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.ftd.nz/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ftd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jidi\":{\"brand\":\"jidi\",\"short\":\"jidi\",\"pinyin\":\"jidi\",\"name\":\"极地快递\",\"short_name\":\"极地快递\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.polarexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jidi.png\",\"cp_code\":null,\"is_express\":\"1\"},\"wanjia\":{\"brand\":\"wanjia\",\"short\":\"wanjia\",\"pinyin\":\"wanjia\",\"name\":\"万家物流\",\"short_name\":\"万家物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://shwanjia.5656tong.com/default.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_wanjia.png\",\"cp_code\":null,\"is_express\":\"1\"},\"santai\":{\"brand\":\"santai\",\"short\":\"santai\",\"pinyin\":\"santai\",\"name\":\"三态速递\",\"short_name\":\"三态速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.sfcservice.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_santai.png\",\"cp_code\":null,\"is_express\":\"1\"},\"youyou\":{\"brand\":\"youyou\",\"short\":\"youyou\",\"pinyin\":\"youyou\",\"name\":\"优优速递\",\"short_name\":\"优优速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.2uex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_youyou.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xyInter\":{\"brand\":\"xyInter\",\"short\":\"xyInter\",\"pinyin\":\"xyInter\",\"name\":\"新元国际\",\"short_name\":\"新元国际\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://xynyc.com/admin/contact.html?v=1\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xyInter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ocs\":{\"brand\":\"ocs\",\"short\":\"ocs\",\"pinyin\":\"ocs\",\"name\":\"OCS\",\"short_name\":\"OCS\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.ocschina.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ocs.png\",\"cp_code\":null,\"is_express\":\"1\"},\"bfdf\":{\"brand\":\"bfdf\",\"short\":\"bfdf\",\"pinyin\":\"bfdf\",\"name\":\"百福东方\",\"short_name\":\"百福东方\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.ees-logistics.net/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_bfdf.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dekun\":{\"brand\":\"dekun\",\"short\":\"dekun\",\"pinyin\":\"dekun\",\"name\":\"德坤物流\",\"short_name\":\"德坤物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.dekuncn.com/index.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dekun.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jumeng\":{\"brand\":\"jumeng\",\"short\":\"jumeng\",\"pinyin\":\"jumeng\",\"name\":\"聚盟共建\",\"short_name\":\"聚盟共建\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.jumstc.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jumeng.png\",\"cp_code\":null,\"is_express\":\"1\"},\"oneexpress\":{\"brand\":\"oneexpress\",\"short\":\"oneexpress\",\"pinyin\":\"yisudi\",\"name\":\"一速递\",\"short_name\":\"一速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.one-express.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_oneexpress.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fuji\":{\"brand\":\"fuji\",\"short\":\"fuji\",\"pinyin\":\"fuji\",\"name\":\"富吉速运\",\"short_name\":\"富吉速运\",\"tel\":\"\",\"common\":\"0\",\"url\":\"https://www.fujisuyun.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fuji.png\",\"cp_code\":null,\"is_express\":\"1\"},\"7e\":{\"brand\":\"7e\",\"short\":\"7e\",\"pinyin\":\"7e\",\"name\":\"7E速递\",\"short_name\":\"7E\",\"tel\":\"\",\"common\":\"1\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_7e.png\",\"cp_code\":null,\"is_express\":\"1\"},\"value\":{\"brand\":\"value\",\"short\":\"value\",\"pinyin\":\"meitong\",\"name\":\"美通\",\"short_name\":\"美通\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.valueway.net/main.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_value.png\",\"cp_code\":null,\"is_express\":\"1\"},\"anda\":{\"brand\":\"anda\",\"short\":\"anda\",\"pinyin\":\"anda\",\"name\":\"安达速递\",\"short_name\":\"安达\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://cn.ada-post.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_anda.png\",\"cp_code\":null,\"is_express\":\"1\"},\"aode\":{\"brand\":\"aode\",\"short\":\"aode\",\"pinyin\":\"aode\",\"name\":\"澳德物流\",\"short_name\":\"澳德\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.auodexpress.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_aode.png\",\"cp_code\":null,\"is_express\":\"1\"},\"guexp\":{\"brand\":\"guexp\",\"short\":\"guexp\",\"pinyin\":\"quanlian\",\"name\":\"全联速运\",\"short_name\":\"全联\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.guexp.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_guexp.png\",\"cp_code\":null,\"is_express\":\"1\"},\"youban\":{\"brand\":\"youban\",\"short\":\"youban\",\"pinyin\":\"youbang\",\"name\":\"邮邦国际\",\"short_name\":\"邮邦\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.youban.de/CN/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_youban.png\",\"cp_code\":null,\"is_express\":\"1\"},\"shidatong\":{\"brand\":\"shidatong\",\"short\":\"shidatong\",\"pinyin\":\"shidatong\",\"name\":\"时达通\",\"short_name\":\"时达通\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.jssdt56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_shidatong.png\",\"cp_code\":null,\"is_express\":\"1\"},\"fyinter\":{\"brand\":\"fyinter\",\"short\":\"fyinter\",\"pinyin\":\"fyinter\",\"name\":\"泛远国际物流\",\"short_name\":\"泛远国际\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.sfyf.cn/basics/index.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_fyinter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"bflg\":{\"brand\":\"bflg\",\"short\":\"bflg\",\"pinyin\":\"shbinfen\",\"name\":\"上海缤纷物流\",\"short_name\":\"上海缤纷\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.bf-lg.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_bflg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"aotsd\":{\"brand\":\"aotsd\",\"short\":\"aotsd\",\"pinyin\":\"aotsd\",\"name\":\"澳天速运\",\"short_name\":\"澳天速运\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://aotsd.com/express/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_aotsd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hd\":{\"brand\":\"hd\",\"short\":\"hd\",\"pinyin\":\"hd\",\"name\":\"宏递快运\",\"short_name\":\"宏递快运\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.hd.com.cn/outlets\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hzzy\":{\"brand\":\"hzzy\",\"short\":\"hzzy\",\"pinyin\":\"hzzy\",\"name\":\"海中转运\",\"short_name\":\"海中转运\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.haizhongzhuanyun.com\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hzzy.png\",\"cp_code\":null,\"is_express\":\"1\"},\"lfexpress\":{\"brand\":\"lfexpress\",\"short\":\"lfexpress\",\"pinyin\":\"lfexpress\",\"name\":\"龙枫国际快递\",\"short_name\":\"龙枫国际快递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.lf-express.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_lfexpress.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sendchina\":{\"brand\":\"sendchina\",\"short\":\"sendchina\",\"pinyin\":\"sendchina\",\"name\":\"速递中国\",\"short_name\":\"速递中国\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://send2china.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sendchina.png\",\"cp_code\":null,\"is_express\":\"1\"},\"shangqiao\":{\"brand\":\"shangqiao\",\"short\":\"shangqiao\",\"pinyin\":\"shangqiao\",\"name\":\"商桥物流\",\"short_name\":\"商桥物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.shangqiao56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_shangqiao.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sx\":{\"brand\":\"sx\",\"short\":\"sx\",\"pinyin\":\"sx\",\"name\":\"三象速递\",\"short_name\":\"三象速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.sxexpress.com.au/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"szyd\":{\"brand\":\"szyd\",\"short\":\"szyd\",\"pinyin\":\"szyd\",\"name\":\"三真驿道\",\"short_name\":\"三真驿道\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.3zlink.com/3zlink/index.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_szyd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yaofei\":{\"brand\":\"yaofei\",\"short\":\"yaofei\",\"pinyin\":\"yaofei\",\"name\":\"耀飞同城\",\"short_name\":\"耀飞同城\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.1fkd.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yaofei.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ybg\":{\"brand\":\"ybg\",\"short\":\"ybg\",\"pinyin\":\"ybg\",\"name\":\"洋包裹\",\"short_name\":\"洋包裹\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.yangbaoguo.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ybg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zjly\":{\"brand\":\"zjly\",\"short\":\"zjly\",\"pinyin\":\"zhongji\",\"name\":\"中集冷云\",\"short_name\":\"中集\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.cccc58.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zjly.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xyyg\":{\"brand\":\"xyyg\",\"short\":\"xyyg\",\"pinyin\":\"xueyu\",\"name\":\"雪域易购\",\"short_name\":\"雪域易购\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.qhxyyg.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xyyg.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jswl\":{\"brand\":\"jswl\",\"short\":\"jswl\",\"pinyin\":\"jingshun\",\"name\":\"景顺物流\",\"short_name\":\"景顺\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.jingshunwuliu.com/#jl\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jswl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xfx\":{\"brand\":\"xfx\",\"short\":\"xfx\",\"pinyin\":\"xiaofeixia\",\"name\":\"小飞侠速递\",\"short_name\":\"小飞侠\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.cyxfx.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xfx.png\",\"cp_code\":null,\"is_express\":\"1\"},\"exfresh\":{\"brand\":\"exfresh\",\"short\":\"exfresh\",\"pinyin\":\"anxianda\",\"name\":\"安鲜达\",\"short_name\":\"安鲜达\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.exfresh.com.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_exfresh.png\",\"cp_code\":null,\"is_express\":\"1\"},\"mingtong\":{\"brand\":\"mingtong\",\"short\":\"mingtong\",\"pinyin\":\"mingtong\",\"name\":\"明通国际快递\",\"short_name\":\"明通国际\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.mountain-int.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_mingtong.png\",\"cp_code\":null,\"is_express\":\"1\"},\"feiyang\":{\"brand\":\"feiyang\",\"short\":\"feiyang\",\"pinyin\":\"feiyang\",\"name\":\"飞洋快递\",\"short_name\":\"飞洋快递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://express.shipgce.com/index.htm\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_feiyang.png\",\"cp_code\":null,\"is_express\":\"1\"},\"com1express\":{\"brand\":\"com1express\",\"short\":\"com1express\",\"pinyin\":\"shangyi\",\"name\":\"商壹国际物流\",\"short_name\":\"商壹国际\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.com1express.net/home.html\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_com1express.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jiacheng\":{\"brand\":\"jiacheng\",\"short\":\"jiacheng\",\"pinyin\":\"jiacheng\",\"name\":\"佳成快递\",\"short_name\":\"佳成\",\"tel\":\"\",\"common\":\"1\",\"url\":\"\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jiacheng.png\",\"cp_code\":null,\"is_express\":\"1\"},\"yzswl\":{\"brand\":\"yzswl\",\"short\":\"yzswl\",\"pinyin\":\"yazhoushun\",\"name\":\"亚洲顺物流\",\"short_name\":\"亚洲顺物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.yzs56.net.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_yzswl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hpinter\":{\"brand\":\"hpinter\",\"short\":\"hpinter\",\"pinyin\":\"haipai\",\"name\":\"海派国际速递\",\"short_name\":\"海派国际速递\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.hpexpress.com.au/index\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hpinter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"esd\":{\"brand\":\"esd\",\"short\":\"esd\",\"pinyin\":\"esuda\",\"name\":\"E速达\",\"short_name\":\"E速达\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.exsuda.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_esd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"beteng\":{\"brand\":\"beteng\",\"short\":\"beteng\",\"pinyin\":\"baiteng\",\"name\":\"百腾物流\",\"short_name\":\"百腾物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.beteng.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_beteng.png\",\"cp_code\":null,\"is_express\":\"1\"},\"hrex\":{\"brand\":\"hrex\",\"short\":\"hrex\",\"pinyin\":\"jianadajincheng\",\"name\":\"加拿大锦程快递\",\"short_name\":\"加拿大锦程\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.hr-ex.com/zh-CN/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_hrex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"wjkwl\":{\"brand\":\"wjkwl\",\"short\":\"wjkwl\",\"pinyin\":\"wanjiakang\",\"name\":\"万家康物流\",\"short_name\":\"万家康\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.wjkwl.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_wjkwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"mdinter\":{\"brand\":\"mdinter\",\"short\":\"mdinter\",\"pinyin\":\"mingda\",\"name\":\"明达国际速递\",\"short_name\":\"明达国际\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.tmw-express.com.au/#/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_mdinter.png\",\"cp_code\":null,\"is_express\":\"1\"},\"huida\":{\"brand\":\"huida\",\"short\":\"huida\",\"pinyin\":\"huida\",\"name\":\"汇达物流\",\"short_name\":\"汇达\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.huidaex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_huida.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cht\":{\"brand\":\"cht\",\"short\":\"cht\",\"pinyin\":\"chenghetong\",\"name\":\"诚和通\",\"short_name\":\"诚和通\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.cht361.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cht.png\",\"cp_code\":null,\"is_express\":\"1\"},\"cywl\":{\"brand\":\"cywl\",\"short\":\"cywl\",\"pinyin\":\"chuangyun\",\"name\":\"创运物流\",\"short_name\":\"创运\",\"tel\":\"\",\"common\":\"1\",\"url\":\"http://www.zjcy56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_cywl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"huif\":{\"brand\":\"huif\",\"short\":\"huif\",\"pinyin\":\"huifeng\",\"name\":\"汇峰物流\",\"short_name\":\"汇峰物流\",\"tel\":\"\",\"common\":\"1\",\"url\":\"https://www.huif56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_huif.png\",\"cp_code\":null,\"is_express\":\"1\"},\"ht56\":{\"brand\":\"ht56\",\"short\":\"ht56\",\"pinyin\":\"hongtai\",\"name\":\"鸿泰物流\",\"short_name\":\"鸿泰物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.ht56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_ht56.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tarrive\":{\"brand\":\"tarrive\",\"short\":\"tarrive\",\"pinyin\":\"tongda\",\"name\":\"通达物流\",\"short_name\":\"通达物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.tarrive.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tarrive.png\",\"cp_code\":null,\"is_express\":\"1\"},\"shihuatong\":{\"brand\":\"shihuatong\",\"short\":\"shihuatong\",\"pinyin\":\"shihuatong\",\"name\":\"世华通物流\",\"short_name\":\"世华通物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.szshihuatong56.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_shihuatong.png\",\"cp_code\":null,\"is_express\":\"1\"},\"sutonghd\":{\"brand\":\"sutonghd\",\"short\":\"sutonghd\",\"pinyin\":\"sutonghd\",\"name\":\"速通鸿达\",\"short_name\":\"速通鸿达\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.sutonghd.cn/service-query.php\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_sutonghd.png\",\"cp_code\":null,\"is_express\":\"1\"},\"zhonghongwl\":{\"brand\":\"zhonghongwl\",\"short\":\"zhonghongwl\",\"pinyin\":\"zhonghongwl\",\"name\":\"中宏物流\",\"short_name\":\"中宏物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.zhonghongwl.cn/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_zhonghongwl.png\",\"cp_code\":null,\"is_express\":\"1\"},\"tianma\":{\"brand\":\"tianma\",\"short\":\"tianma\",\"pinyin\":\"tianma\",\"name\":\"天马物流\",\"short_name\":\"天马物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.expresstochina.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_tianma.png\",\"cp_code\":null,\"is_express\":\"1\"},\"dfglobalex\":{\"brand\":\"dfglobalex\",\"short\":\"dfglobalex\",\"pinyin\":\"dongfangqq\",\"name\":\"东方全球速递\",\"short_name\":\"东方全球\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.dfglobalex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_dfglobalex.png\",\"cp_code\":null,\"is_express\":\"1\"},\"haobo\":{\"brand\":\"haobo\",\"short\":\"haobo\",\"pinyin\":\"haobo\",\"name\":\"浩博物流\",\"short_name\":\"浩博物流\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.njhaobo.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_haobo.png\",\"cp_code\":null,\"is_express\":\"1\"},\"xjdaishu\":{\"brand\":\"xjdaishu\",\"short\":\"xjdaishu\",\"pinyin\":\"daishu\",\"name\":\"袋鼠速递\",\"short_name\":\"袋鼠速递\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.xjdaishu.com/#/query\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_xjdaishu.png\",\"cp_code\":null,\"is_express\":\"1\"},\"jcsuda\":{\"brand\":\"jcsuda\",\"short\":\"jcsuda\",\"pinyin\":\"jiachengsd\",\"name\":\"嘉城速达\",\"short_name\":\"嘉城速达\",\"tel\":\"\",\"common\":\"0\",\"url\":\"http://www.dfglobalex.com/\",\"logo_link\":\"http://img.kuaidihelp.com/brand_logo/icon_jcsuda.png\",\"cp_code\":null,\"is_express\":\"1\"}},\"ts\":1682477266820},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b09c7846-7cb8-4c10-b89d-640d8e411015", "ruleName": "xqx-检查支付分", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/queryAfterServiceStatus", "filterId": "f67cb7af-7210-4b0a-a4ed-69b26cf34322", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: 0,\n    msg: ''\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7c05212c-dd1d-42fa-8921-5c2973df2e70", "ruleName": "xqx-获取可提现金额上限", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/whiteCashLimit", "filterId": "19795fc9-33df-452d-9062-369998c862be", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data|1': [200, 500],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "458314e0-67bb-4b79-afae-d66426497268", "ruleName": "xqx-收益统计月度总览", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/OrderStatic/staticMonthData", "filterId": "ffb1b562-73d8-4b6f-9da0-8be8f892497b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": [\n        {\n            \"brand\": \"sf\",\n            \"total_order_num\": 40,\n            \"collect_order_num\": 2400,\n            \"original_price\": 645,\n            \"profit\": \"12004.68\",\n            \"settle_order_num\": \"24.00\",\n            \"send_rate\": \"60.00\"\n        },\n        {\n            \"brand\": \"jd\",\n            \"total_order_num\": 0,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 103330,\n            \"settle_order_num\": 33333,\n            \"send_rate\": \"0.00\"\n        },\n        {\n            \"brand\": \"dp\",\n            \"total_order_num\": 0,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 201213,\n            \"settle_order_num\": 2231,\n            \"send_rate\": \"0.00\"\n        },\n        {\n            \"brand\": \"yd\",\n            \"total_order_num\": 0,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 66123,\n            \"settle_order_num\": 10177,\n            \"send_rate\": \"0.00\"\n        },\n        {\n            \"brand\": \"yt\",\n            \"total_order_num\": 0,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 90,\n            \"settle_order_num\": 090,\n            \"send_rate\": \"0.00\"\n        },\n        {\n            \"brand\": \"sto\",\n            \"total_order_num\": 4,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 23,\n            \"settle_order_num\": 199,\n            \"send_rate\": \"0.00\"\n        },\n        {\n            \"brand\": \"jt\",\n            \"total_order_num\": 24,\n            \"collect_order_num\": 0,\n            \"original_price\": 0,\n            \"profit\": 124.68,\n            \"settle_order_num\": 24,\n            \"send_rate\": \"0.00\"\n        },\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "cd397f96-5e80-4d59-90c0-8d1a6c92bc25", "ruleName": "xqx-收益统计总览", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/OrderStatic/staticTotalData", "filterId": "2dceae34-e210-4507-8c91-bfb285704a81", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"total_order_num\":\"150\",\n        \"collect_order_num\":\"135\",\n        \"original_price\":\"4500.00\",\n        \"profit\":\"1000000.00\",\n        \"send_rate\":\"90.00\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9aee5a57-4656-4618-9f51-bddbec0bb7ba", "ruleName": "xqx-资金流水类型", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/getAllBusinessType", "filterId": "a638d5e5-9589-4ef4-be01-18a6d2f65869", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"order_cz\":\"充值\",\n        \"order_comm\":\"订单补款\",\n        \"freight_refund_in\":\"订单退款\",\n        \"deposit_back_in\":\"保证金退回\",\n        \"cash\":\"提现\",\n        \"freight_out\":\"下单预支付\",\n        \"refund_out\":\"取消退款\",\n        \"deposit_out\":\"保证金划扣\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "de63b722-4b3e-43be-9ca2-30d632833b33", "ruleName": "xqx-删除订单照片", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/deletePackagePics", "filterId": "e1a178b1-44d4-46a1-acc6-97490a543304", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":1\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "411807e6-a4a0-442a-8f8c-d48e80999082", "ruleName": "xqx-订单上传包裹图片", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/uploadPackagePics", "filterId": "a00c5201-9499-4fb8-b496-ac232436fc58", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":1\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c4ca93ec-5188-453d-9e6f-73afaac923bf", "ruleName": "xqx-地址智能识别", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/AddressBook/getAddrInfoByString", "filterId": "68e44939-e872-48c9-b8a4-0524edc9761a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    code: '\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n      {\n        address: \"真源小区\",\n        city: \"上海市\",\n        county: \"普陀区\",\n        name: \"夏起翔\",\n        province: \"上海市\",\n        tel: \"15277778888\",\n      }\n    ],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1e3e071b-0310-4129-9288-cfcf435dbc1a", "ruleName": "xqx-上传照片", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/upload", "filterId": "529b6657-2a0a-4233-bd5c-7b3c82c80992", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":{\n      \"url\":\"/package_pics/2022/07/04/573717777983254862c29d06a5f128216924435.jpg\"\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a80863ef-72f7-4cd6-a129-771af700dccc", "ruleName": "xqx-添加地址", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/AddressBook/add", "filterId": "94213fed-2867-47e7-bfcd-14ef97359e01", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"data\":0,\n    \"msg\":\"成功\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ac6494b0-2bff-4222-aa0e-d3955231f9e6", "ruleName": "xqx-校验地址是否合法", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/checkAddress", "filterId": "afd92602-4d4e-46a3-9f25-f265272c9260", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n      \"code|1\":[0,1],\n      \"msg\":\"该地址不支持派送服务\",\n      \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5fb12750-c041-4b04-bb01-4bf2f115b3e1", "ruleName": "xqx-团员设置", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/setMemberConfig", "filterId": "545e0b28-12b5-4e62-827d-359419b6a3e7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\":{\n        \"code\":0,\n        \"data\":0,\n        \"msg\":\"成功\"\n    },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "fbb79f43-db4f-4421-bf7b-2fce9c6ce36c", "ruleName": "xqx-运费折扣", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Freight/list", "filterId": "9066d051-3aec-485c-adbe-790a1160d663", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n        \"code\":0,\n    \"data\":[\n        {\n            \"brand\":\"sf\",\n            \"create_time\":\"2022-05-12 03:11:34\",\n            \"discount_rate\":\"7.1\",\n            \"id\":\"1\",\n            \"is_deleted\":\"0\",\n            \"regiment_id\":\"691\"\n        },\n        {\n            \"brand\":\"jd\",\n            \"create_time\":\"2022-05-12 03:12:23\",\n            \"discount_rate\":\"0.1\",\n            \"id\":\"2\",\n            \"is_deleted\":\"0\",\n            \"regiment_id\":\"691\"\n        }\n    ],\n    \"msg\":\"成功\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "9670dc08-93dd-419f-a031-875881372974", "ruleName": "xqx-批量设置团员", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/batchSetMemberConfig", "filterId": "0ff335b5-1c7e-4a3e-ad9e-85ba9723ddbd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"code\":\"0\",\n      \"msg\":\"success\",\n      \"data\":{}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "09bde1c7-dc52-444d-8650-5b32df9eca0d", "ruleName": "xqx-团员详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/memberDetail", "filterId": "73a9c72a-454b-41a2-8f96-66696b011cbd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"调用成功\",\"data\":{\"id\":\"2264485\",\"openid\":\"ozHEa5Ug7iVUHvjKIi5R-4uRxN0E\",\"nickname\":\"全寄\",\"mobile\":\"13810473144\",\"gender\":\"未知\",\"language\":\"\",\"city\":\"\",\"province\":\"\",\"country\":\"\",\"avatar_url\":\"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\"unionid\":\"oEp1_t0xy89tdbxXMKXVuuMXC4gM\",\"channel\":\"wechat_mini\",\"is_admin\":\"0\",\"regiment_id\":\"12834\",\"note\":\"\",\"kb_id\":\"261733950\",\"complate_at\":\"2023-05-16 10:42:03\",\"create_time\":\"2023-05-16 10:40:56\",\"old_regiment_id\":\"0\",\"service_charge_orders\":\"\",\"regiment_user_id\":\"2264485\",\"pay_method\":\"1\",\"audit_order\":\"0\",\"is_black\":\"0\",\"jd_service_charge\":\"0.00\",\"disable_brand\":\"\",\"remarks\":\"全寄\",\"update_time\":\"2023-05-16 10:42:03\",\"order_num\":0,\"total_freight\":\"0.00\",\"regiment_profit\":\"0.00\",\"wait_pay_price\":\"0.00\",\"brand_freight_config\":[{\"id\":\"648984\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sf\",\"f_fee\":\"9.50\",\"s_fee\":\"0.00\",\"fee_type\":\"discount\",\"area_type\":\"0\",\"create_time\":\"2023-08-24 09:44:57\",\"update_time\":\"2023-08-24 09:44:57\"},{\"id\":\"648985\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sfky\",\"f_fee\":\"10.00\",\"s_fee\":\"0.00\",\"fee_type\":\"discount\",\"area_type\":\"0\",\"create_time\":\"2023-08-24 09:44:57\",\"update_time\":\"2023-08-24 09:44:57\"},{\"id\":\"648986\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"jd\",\"f_fee\":\"9.50\",\"s_fee\":\"0.00\",\"fee_type\":\"discount\",\"area_type\":\"0\",\"create_time\":\"2023-08-24 09:44:57\",\"update_time\":\"2023-08-24 09:44:57\"},{\"id\":\"648987\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"dp\",\"f_fee\":\"9.20\",\"s_fee\":\"0.00\",\"fee_type\":\"discount\",\"area_type\":\"0\",\"create_time\":\"2023-08-24 09:44:57\",\"update_time\":\"2023-08-24 09:44:57\"},{\"id\":\"648988\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"ems\",\"f_fee\":\"8.60\",\"s_fee\":\"0.00\",\"fee_type\":\"discount\",\"area_type\":\"0\",\"create_time\":\"2023-08-24 09:44:57\",\"update_time\":\"2023-08-24 09:44:57\"},{\"id\":\"648989\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yd\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"1\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648990\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"1\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648991\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sto\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"1\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648992\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"zt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"1\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648993\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yd\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"2\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648994\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"2\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648995\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sto\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"2\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648996\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"zt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"2\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648997\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yd\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"3\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648998\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"3\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"648999\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sto\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"3\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"649000\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"zt\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"3\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"649001\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yd\",\"f_fee\":\"1.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"4\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"649002\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"yt\",\"f_fee\":\"0.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"4\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"649003\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"sto\",\"f_fee\":\"0.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"4\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"},{\"id\":\"649004\",\"regiment_id\":\"12834\",\"regiment_user_id\":\"2264485\",\"brand\":\"zt\",\"f_fee\":\"0.00\",\"s_fee\":\"0.00\",\"fee_type\":\"append\",\"area_type\":\"4\",\"create_time\":\"2023-08-24 09:45:58\",\"update_time\":\"2023-08-24 09:45:58\"}]}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6c2692be-54b2-4872-a3e2-e6187ec8210a", "ruleName": "xqx-收益统计-每月订单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/perMonthOrders", "filterId": "8f977bde-b471-4337-a13f-b10ec05bbb82", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    'data|10': [\n      {\n        month: 12,\n        totalMoney: 1604.59,\n        totalOrders: 500,\n        brands: [\n          {\n            brand: 'jd',\n            money: 300,\n            order:100,\n          },\n          {\n            brand: 'sf',\n            money: 100,\n            order: 50,\n          },\n          {\n            brand: 'dp',\n            money: 150,\n            order: 70,\n          }\n        ]\n      }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "af238dfa-6009-45cb-9622-626f1c0b59e7", "ruleName": "xqx-收益统计-订单图表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/getOrdersByMonth", "filterId": "7f7cb3a9-22c9-40c5-bac5-f246147dea9f", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: [\n      {\n        brand: 'jd',\n        orders: 100,\n        profit: 200,\n      },\n      {\n        brand: 'sf',\n        orders: 300,\n        profit: 400,\n      },\n      {\n        brand: 'dp',\n        orders: 500,\n        profit: 600,\n      },\n      {\n        brand: 'yd',\n        orders: 500,\n        profit: 600,\n      },\n      {\n        brand: 'yt',\n        orders: 500,\n        profit: 600,\n      },\n      {\n        brand: 'sto',\n        orders: 500,\n        profit: 600,\n      },\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "514f2748-8b40-4373-9eca-9b7eaeebdf7c", "ruleName": "xqx-收益统计-利润总览", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "api/getProfitInfo", "filterId": "974bdb04-8699-4f3b-ae55-e831d8c10d70", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      totalProfit: '2000',\n      orders: '300',\n      sended: '200',\n      sendRate: '90%'\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c3cad7b3-966e-49ef-8c5f-7b49cd6bd3d0", "ruleName": "sjy-获取用户团配信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/userRegimentConfig", "filterId": "0d1cff6e-e9a8-435b-b4b5-641d38b57edd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n     \"code\":0,\n     \"data\":{\n        \"is_black\":0,\n        \"audit_order\":1\n     }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"code\":0,\n     \"data\":{\n        \"is_black|1\":[0,1],\n        \"audit_order|1\":[0,1]\n     }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "16dd1a0b-1635-48ca-b657-8c24ed908ccd", "ruleName": "sjy-团长欠款", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentArrearsInfo", "filterId": "29c7cf32-f0c0-4a57-94f2-0bb3acf2464e", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\"arrears_money\":123},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"data\":{\"arrears_money\":0},\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "55180097-d119-40eb-9e02-3bc3a4314ff4", "ruleName": "sjy-团员获取欠款", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getUserArrearsInfo", "filterId": "f0d871b3-7ee6-4aa6-9de6-338efc8b2868", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"data\":{\n      \"order_num\":10,\n      \"money\":2\n    },\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n     \"data\":{\n      \"order_num\":0,\n      \"money\":0\n    },\n    \"code\":0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "27b2c287-c6f9-4e4a-8a41-121583ae0b8a", "ruleName": "xqx-订单确认", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/batchSubmit", "filterId": "0892c467-631c-4c74-9bb0-8fd98035d00b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {},\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "cebf2be6-26bf-4a40-a0ef-c5f679634dd0", "ruleName": "xqx-物流数据", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Logistics/track", "filterId": "3cbee616-15fc-495d-ba8a-261cc46f0304", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: [\n      \n        ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "498489be-b9b0-4d6c-9566-c27216f4390d", "ruleName": "xqx-流水详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/detail", "filterId": "3ddec7be-de8a-4a84-a6ad-7db329e1869a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      brand: 'sf',\n      bill_source: \"\",\n      bill_time: '@date',\n      bill_type: 1,\n      business_desc: '流水',\n      business_type: 1,\n      is_monthly: 1,\n      pay_order_id: 123123123,\n      price: 20,\n      user_note: \"备注\",\n      waybill_no: \"sf32748293400\",\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c9f6e241-0b82-4967-b8ea-e9d02b6e9d9a", "ruleName": "xqx-团员发起补款支付", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/paySign", "filterId": "3a29c5bc-6bec-433e-88d2-4b85eb7a7790", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\"code\":0,\"msg\":\"成功\",\"data\":{\"appId\":\"wx1ac840bddf4b78cf\",\"nonceStr\":\"G1qhpztDHEpuOBFO\",\"timeStamp\":\"1653731353\",\"package\":\"prepay_id=wx281749139328610a6c2c83d08cc8e50000\",\"signType\":\"MD5\",\"sign\":\"AA181A2D2D1B6BAA6BB2B1ADD6A4B488\"}},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "2e15498e-c709-44f9-94ff-1701e727fc56", "ruleName": "xqx-资金-资金流水", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/list", "filterId": "32bdcade-3f15-4154-a6c9-1be64f08f3f5", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    msg: 'success',\n    data: {\n      in: \"57.55\",\n      page: 1,\n      total: 20,\n      'list|10': \n        [\n          {\n            \"bill_desc|1\": [\"订单运费退回 (来自平台)\", \"item-extraitem-extraitem-extraitem-extraitem-extraitem-extra\"],\n            'bill_id|+1': 1,\n            bill_type: \"in\",\n            'price': '20000.00',\n            bill_time: \"@date\",\n            \"total_money\": \"10096.37\",\n            \"business_desc\": \"订单补款\",\n            \"desc\": \"来自风云\",\n            'bill_desc_icon|1': ['预', '退', '补']\n          }\n        ],\n      out: \"57.45\",\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "d9fa03db-3767-4ca9-9f9b-50d63220c6bc", "ruleName": "xqx-加入团", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Regiment/join", "filterId": "69a88944-50f8-4cd3-918c-97cf2feeb3f6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8210b0dd-7700-4418-8ae3-8535add73d5a", "ruleName": "xqx-资金-提现", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/withdraw", "filterId": "3dd6a697-7132-4d9c-acf5-a65919448ac0", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [],\n    msg: '提现成功！！！！！！！！！！'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "62acd386-ca1a-4829-9430-da1495bb27de", "ruleName": "xqx-资金-钱包余额", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/wallet", "filterId": "b9d8c127-30ea-4be4-ba5d-6defa309fcff", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n\t\"code\": 0,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"price\": \"1200.12\",\n\t\t\"total_price\": \"1200.12\",\n\t\t\"bail_price\": \"0.00\",\n\t\t\"is_deposit\": 1,\n\t\t\"deposit_source\": \"payScore\"\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b88f781d-5554-4ba5-a6e2-cf74997e7428", "ruleName": "xqx-获取团员待补款信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getUserArrearsInfo", "filterId": "ed9b5dee-3047-4074-8ec8-2e8ce85bc233", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    msg: 'success',\n    data: {\n      \"money\":10,\n      \"order_num\":1,\n      \"order_number\":89348393492323\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1be14b07-4d46-4905-a4e2-6cbe5250d912", "ruleName": "xqx-获取团长待补款信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentArrearsInfo", "filterId": "6b21b829-5907-403b-afbe-f3522ea8d589", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    data: {\n      \"money\":10,\n      \"order_num\":2,\n      \"order_number\":89348393492323\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ec2432ed-332d-4868-98ab-53e48f033ba6", "ruleName": "xqx-团长订单详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentInfo", "filterId": "ed9f7bc4-82c7-4585-858f-ea520a556fa3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": ,\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n\"data\": {\n\"code\": 0,\n\"msg\": \"成功\",\n\"data\": {\n  \"order_number\": \"805315321406789\",\n  \"user_id\": \"433\",\n  \"brand\": \"dp\",\n  \"waybill\": \"DPK6567504338197\",\n  \"delivery_type\": \"PACKAGE\",\n  \"shipper_name\": \"啥玩意\",\n  \"shipper_tel\": \"\",\n  \"shipper_mobile\": \"15201736791\",\n  \"shipper_province\": \"四川省\",\n  \"shipper_city\": \"宜宾市\",\n  \"shipper_county\": \"屏山县\",\n  \"shipper_address\": \"新泾镇晋义恒刀削面1\",\n  \"change_shipper_address_order\": 0,\n  \"shipping_name\": \"还***\",\n  \"shipping_tel\": \"\",\n  \"shipping_mobile\": \"152****9988\",\n  \"shipping_province\": \"安徽省\",\n  \"shipping_city\": \"蚌埠市\",\n  \"shipping_county\": \"\",\n  \"shipping_address\": \"\",\n  \"order_status\": 4,\n  \"pay_status\": 1,\n  \"logistic_status\": 0,\n  \"logistic_status_txt\": \"已发货\",\n  \"package_weight\": 1,\n  \"package_info\": \"日用品\",\n  \"order_package_pics\": [],\n  \"package_note\": \"请带个防水袋请带个防水袋请带个防水袋请带个防水袋请\",\n  \"freight\": \"18\",\n  \"charging_weight\": 2,\n  \"place_volume\": 0,\n  \"settlement_volume\": 12000,\n  \"f_fee\": \"12.60\",\n  \"s_fee\": \"3.60\",\n  \"f_kg\": 1,\n  \"regiment_estimate_price\": 10.22,\n  \"regiment_wait_pay_price\": 0,\n  \"regiment_pay_price\": 23.14,\n  \"other_money\": 0,\n  \"pay_price\": '',\n  \"wait_pay_price\": 28,\n  \"claiming_value\": 0,\n  \"warrant_price\": 0,\n  \"real_claiming_value\": 200,\n  \"real_warrant_price\": 2,\n  \"calculate_price_type\": 1,\n  \"regiment_profit\": 4.86,\n  \"settlement_price_details\": [\n  {\n  \"fee\": \"2\",\n  \"type\": 1,\n  \"name\": \"保价费\"\n  },\n  {\n  \"fee\": \"3.00\",\n  \"type\": 1,\n  \"name\": \"包装费\"\n  },\n  {\n  \"fee\": \"5.00\",\n  \"type\": 1,\n  \"name\": \"送货费\"\n  }\n  ],\n  \"collect_courier_mobile\": \"17788889999\",\n  \"collect_courier_name\": \"快递员111\",\n  \"reserve_start_time\": \"2023-05-31 14:47:24\",\n  \"reserve_end_time\": \"2023-05-31 15:47:24\",\n  \"create_at\": \"2023-05-31 14:46:54\",\n  \"regiment_remark\": \"阿秦\",\n  \"can_label\": 0,\n  \"can_push_pay\": 1,\n  \"arrive_pay\": 0,\n  \"third_create_at\": \"2023-05-31 15:47:24\",\n  \"pay_method\": \"2\",\n  \"order_type\": \"dp\",\n  \"courier\": [],\n  \"regiment_id\": \"189\",\n  \"batch_number\": \"\",\n  \"batch_number_count\": 0,\n  \"estimate_profit\": \"\",\n  \"pay_type\": \"\",\n  \"certificate_path\": \"\"\n  }\n},\n\"statusCode\": \"\",\n\"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1923a02f-c33a-4bd2-af04-4b96b4faf535", "ruleName": "xqx-团员订单详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getUserInfo", "filterId": "da53f157-ec75-4000-802a-5f9d405dcde4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    code: 0,\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      \"order_number\":\"@id\",\n      \"user_id|+1\":1,\n      \"brand|1\":[\"sf\",'jd','dp'],\n      \"waybill\":\"SF1354944431476\",\n      \"delivery_type\":\"offer\",\n      \"shipper_name\":\"黄生\",\n      \"shipper_tel\":\"\",\n      \"shipper_mobile\":\"15201736794\",\n      \"shipper_province\":\"湖南省\",\n      \"shipper_city\":\"长沙市\",\n      \"shipper_county\":\"雨花区\",\n      \"shipper_address\":\"桃花塅路168号\",\n      \"shipping_name\":\"黄*\",\n      \"shipping_tel\":\"\",\n      \"shipping_mobile\":\"152****6794\",\n      \"shipping_province\":\"湖南省\",\n      \"shipping_city\":\"长沙市\",\n      \"shipping_county\":\"雨花区\",\n      \"shipping_address\":\"桃花塅路168号\",\n      \"order_status\":1,\n      \"pay_status|1\":[1],\n      \"logistic_status|1\":[0,1,2],\n      \"logistic_status_txt|1\":[\"已下单\", '运输中', '派件中', '已签收'],\n      \"package_weight\":1,\n      \"package_info\":\"日用品\",\n      \"package_note\":\"\",\n      \"charging_weight\":0,\n      \"place_volume\":0,\n      \"settlement_volume\":0,\n      \"f_fee\":14.4,\n      \"s_fee\":4,\n      \"regiment_estimate_price\":11.5,\n      \"other_money\":0,\n      \"pay_price\":0,\n      \"wait_pay_price|1\": [0,10],\n      \"claiming_value\":0,\n      \"warrant_price\":0,\n      \"real_claiming_value\":0,\n      \"real_warrant_price\":0,\n      \"calculate_price_type\":1,\n      \"collect_courier_mobile\":\"\",\n      \"collect_courier_name\":\"\",\n      \"reserve_start_time\":\"@datetime\",\n      \"reserve_end_time\":\"@datetime\",\n      \"create_at\":\"@datetime\",\n      \"regiment_remark\":\"\",\n      \"certificate_path\": \"/x_preview_img_x/eGZ42A2mkBk/CloudPrint/2023/04/12/10/14/88619335712981539112331604037990.jpg\",\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f79377fe-3d90-4a4e-a63d-3c7d5b0d1df2", "ruleName": "xqx-团员列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/memberList", "filterId": "3805c591-b8e0-41aa-ac4d-7a8dc67f9fc8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      'total_num': 20,\n      'list': [\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-07-11 14:10:41\",\n         country: \"\",\n         create_time: \"2022-06-26 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-07-11 16:10:41\",\n         country: \"\",\n         create_time: \"2022-06-26 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-06-26 14:10:41\",\n         country: \"\",\n         create_time: \"2022-06-26 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-06-26 10:10:41\",\n         country: \"\",\n         create_time: \"2022-06-26 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-06-24 10:10:41\",\n         country: \"\",\n         create_time: \"2022-06-23 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        },\n        {\n         avatar_url: \"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJI4Q67iaIxGK1dicVx20bte4QCNW0pNcSfmlJCbX6iaxU2aztf7R8H4ibP1u36kWcYJhDjYo9mQCEEIg/132\",\n         channel: \"wechat_mini\",\n         city: \"\",\n         complate_at: \"2022-07-01 10:10:41\",\n         country: \"\",\n         create_time: \"2022-06-23 14:10:41\",\n         gender: \"未知\",\n         'id|+1': '@id',\n         is_admin: \"1\",\n         \"is_black|1\": [1,0],\n         kb_id: \"124321521\",\n         language: \"\",\n         mobile: \"15270840581\",\n         nickname: \"Major Tom\",\n         note: \"\",\n         openid: \"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n         province: \"\",\n         regiment_id: \"13\",\n         \"remarks|1\": ['这是备注', ''],\n         unionid: \"\",\n        }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "f0bb4d5e-eb97-42d6-adf7-bfbab2cd9020", "ruleName": "xqx-确认订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/batchSubmit", "filterId": "9c6574fc-2f45-4cae-a0aa-32aeeeeef824", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    code\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: '0',\n    msg: 'success',\n    data: {\n      \"error\":[\n        {\n          \"order_id\":\"705176771000201\",\n          \"error_msg\":\"1009:订单已取消\"\n        },\n        {\n          \"order_id\":\"705176882403540\",\n          \"error_msg\":\"500:取消失败，只允许待取件订单取消，请联系管理员\"\n        }\n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "018b5246-b2a9-4c31-910d-647fb7bf22ad", "ruleName": "xqx-取消订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/cancel", "filterId": "f0e58393-b858-4572-afb9-a8cf88864378", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      \"success\":[\n        \"705176878403539\"\n      ],\n      \"error\":[\n        \n      ]\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1de1912d-fb4c-45b7-a2d7-fd6c2907cc5b", "ruleName": "xqx-将团长订单分配给团员", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/assignOrder", "filterId": "f8f80724-2d22-4013-9ddc-a031a18d2735", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    data: {},\n    code: 0,\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "874b2e18-d853-4cbf-8fca-dbfb89b09e52", "ruleName": "xqx-团员订单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getUserList", "filterId": "ce1e3c1a-cd05-4294-bc5d-68d4e60e0ec2", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data': {\n      'list|10': [{\n        \"order_number\":\"@id\",\n        \"user_id|+1\":1,\n        \"brand|1\":[\"sf\",'jd','dp'],\n        \"waybill\":\"SF1354944431476\",\n        \"delivery_type\":\"offer\",\n        \"shipper_name\":\"黄生\",\n        \"shipper_tel\":\"\",\n        \"shipper_mobile\":\"15201736794\",\n        \"shipper_province\":\"湖南省\",\n        \"shipper_city\":\"长沙市\",\n        \"shipper_county\":\"雨花区\",\n        \"shipper_address\":\"桃花塅路168号\",\n        \"shipping_name\":\"黄*\",\n        \"shipping_tel\":\"\",\n        \"shipping_mobile\":\"152****6794\",\n        \"shipping_province\":\"湖南省\",\n        \"shipping_city\":\"长沙市\",\n        \"shipping_county\":\"雨花区\",\n        \"shipping_address\":\"桃花塅路168号\",\n        \"order_status\":2,\n        \"pay_status|1\":[0,1,2],\n        \"logistic_status|1\":[0,1,2],\n        \"logistic_status_txt|1\":[\"已下单\", '运输中', '派件中', '已签收'],\n        \"package_weight\":1,\n        \"package_info\":\"日用品\",\n        \"package_note\":\"\",\n        \"charging_weight\":0,\n        \"place_volume\":0,\n        \"settlement_volume\":0,\n        \"f_fee\":14.4,\n        \"s_fee\":4,\n        \"regiment_estimate_price\":11.5,\n        \"other_money\":0,\n        \"pay_price\":0,\n        \"wait_pay_price|1\": [0,10],\n        \"claiming_value\":0,\n        \"warrant_price\":0,\n        \"real_claiming_value\":0,\n        \"real_warrant_price\":0,\n        \"calculate_price_type\":0,\n        \"collect_courier_mobile\":\"\",\n        \"collect_courier_name\":\"\",\n        \"reserve_start_time\":\"@datetime\",\n        \"reserve_end_time\":\"@datetime\",\n        \"create_at\":\"@datetime\",\n        \"regiment_remark\":\"\"\n      }],\n      page:1,\n      pageSize:10,\n      total: 100\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "2966038d-ff5f-46a4-8760-936ac2a94b84", "ruleName": "xqx-团长订单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentList", "filterId": "24472994-56b8-4d4c-8007-58f36e19ebef", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data': {\n      \"list|10\": [{\n        \"order_number\":\"@id\",\n        \"user_id|+1\":1,\n        \"brand|1\":['zt', 'sf', 'sto', 'yd', 'jd', 'dp'],\n        \"waybill\":\"SF1354944431476\",\n        \"delivery_type\":\"offer\",\n        \"shipper_name\":\"黄生\",\n        \"shipper_tel\":\"\",\n        \"shipper_mobile\":\"15201736794\",\n        \"shipper_province\":\"湖南省\",\n        \"shipper_city\":\"长沙市\",\n        \"shipper_county\":\"雨花区\",\n        \"shipper_address\":\"桃花塅路168号\",\n        \"shipping_name\":\"黄*\",\n        \"shipping_tel\":\"\",\n        \"shipping_mobile\":\"152****6794\",\n        \"shipping_province\":\"湖南省\",\n        \"shipping_city\":\"长沙市\",\n        \"shipping_county\":\"雨花区\",\n        \"shipping_address\":\"桃花塅路168号\",\n        \"order_status|1\":[0,1,2,3,4,5],\n        \"pay_status|1\":['0','1','2'],\n        \"logistic_status|1\":['0','1','2', '3'],\n        \"logistic_status_txt|1\":['已取消',\"已下单\",'派件中', '已签收'],\n        \"package_weight\":1,\n        \"package_info\":\"日用品\",\n        \"package_note\":\"请带个防水袋请带个防水袋请带个防水袋请带个防水袋请\",\n        \"charging_weight\":0,\n        \"place_volume\":0,\n        \"settlement_volume\":0,\n        \"f_fee\":14.4,\n        \"s_fee\":4,\n        \"regiment_estimate_price\":11.5,\n        \"other_money\":0,\n        \"pay_price\":0,\n        \"wait_pay_price|1\":[0,10,20],\n        \"claiming_value\":0,\n        \"warrant_price\":0,\n        \"real_claiming_value\":0,\n        \"real_warrant_price\":0,\n        \"calculate_price_type\":0,\n        \"collect_courier_mobile\":\"\",\n        \"collect_courier_name\":\"\",\n        \"reserve_start_time\":\"@datetime\",\n        \"reserve_end_time\":\"@datetime\",\n        \"create_at\":\"@datetime\",\n        \"regiment_remark|1\":['','团长给团员的备注'],\n        'can_label':0,\n        'batch_number|1': ['3231', '444', null],\n        'batch_number_count': 10\n      }],\n      page:1,\n      pageSize: 10,\n      total: 100\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "010cce74-1603-49e0-8e2e-337a96947d01", "ruleName": "xqx-注册团长", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Regiment/register", "filterId": "4ff6ead6-83fc-439c-a9f1-c03fb1226b10", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    data: \"\",\n    code: '\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    data: {\n      \"regiment_id\":11\n    },\n    code: 0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6b01e933-907c-4a05-8e87-c271dcd83374", "ruleName": "xqx-绑定手机", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/bindMobile", "filterId": "56fb32ee-8686-4841-be40-d0987de5a187", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: 'success',\n    data: {\n      mobile: '15270840581'\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "68ec8954-58a5-4508-b1a6-4c10d839123d", "ruleName": "xqx-获取手机号", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/authEncryptedMobile", "filterId": "b059747e-69f2-4966-a1a2-39b2767debef", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    data: {\n      \"mobile\":\"13838389438\"\n    },\n    code: 0\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "79e643a3-9036-482e-a154-64373bd341dd", "ruleName": "dgs-发票-重新开票", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/reCreateInvoice", "filterId": "23d23229-3ad4-4e04-a1bb-e3c81e0e8ae7", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"发起重新发票成功\",\n    \"data\":{\n        \"invoiceId\":123232\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a00dc3e2-2b0e-4404-8521-33e51a1ee30b", "ruleName": "dgs-发票-申请开票", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/createInvoice", "filterId": "89b7e14a-7f7f-453f-bcee-e7c870738118", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"申请开票成功\",\n    \"data\":{\n        \"invoiceId\":111111\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b3c15a44-cbe8-48b3-af16-9369be232f3e", "ruleName": "dgs-发票-历史公司税号列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/historyCompanies", "filterId": "b91ff80d-7e6b-4140-b595-2383b86a5e6c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    msg: '获取历史公司税号成功',\n    data: [\n        {\n            \"title\":\"快宝（上海）网络技术有限公司\",\n            \"taxNumber\":\"616464649166464\",\n            \"companyId\":\"30\",\n            \"create_at\":\"2021-08-25 15:06:56\"\n        },\n        {\n            \"title\":\"快宝（上海）网络技术有限公司\",\n            \"taxNumber\":\"913101050559323669\",\n            \"companyId\":\"22\",\n            \"create_at\":\"2020-11-12 15:31:06\"\n        },\n        {\n            \"title\":\"睡觉咯\",\n            \"taxNumber\":\"122576664646414\",\n            \"companyId\":\"20\",\n            \"create_at\":\"2020-10-22 14:08:04\"\n        },\n        {\n            \"title\":\"131313\",\n            \"taxNumber\":\"12345678901234567\",\n            \"companyId\":\"18\",\n            \"create_at\":\"2020-09-24 09:58:00\"\n        },\n        {\n            \"title\":\"16656\",\n            \"taxNumber\":\"*********\",\n            \"companyId\":\"16\",\n            \"create_at\":\"2020-09-22 15:09:28\"\n        },\n        {\n            \"title\":\"睡觉咯\",\n            \"taxNumber\":\"1225\",\n            \"companyId\":\"15\",\n            \"create_at\":\"2020-09-21 10:44:38\"\n        },\n        {\n            \"title\":\"131313\",\n            \"taxNumber\":\"dddid\",\n            \"companyId\":\"10\",\n            \"create_at\":\"2020-09-16 15:33:24\"\n        }\n    ]\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b6ec9b74-347e-492d-9741-85410c657dcf", "ruleName": "dgs-发票-提示信息-未确认", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/getBrands", "filterId": "f874a420-f322-455b-8369-aebbaa7567a4", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: ['注：只能开具订单结算状态为已结清的订单发票'],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "098af8d3-458f-4be5-ad92-5084d9e249f2", "ruleName": "dgs-发票-详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/invoiceDetail", "filterId": "d5e9b650-5b3b-4cb0-a042-7e140394857e", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n        \"invoiceData\":{\n            \"id\":\"225\",\n            \"status\":\"申请中\",\n            \"type\":\"preson\",\n            \"title\":\"哦poor我Roy模特图\",\n            \"remark\":\"\",\n            \"content\":\"收派服务费\",\n            \"mobile\":\"\",\n            \"email\":\"<EMAIL>\",\n            \"drawer\":\"快宝网络科技有限公司\",\n            \"ticket_sn\":\"0\",\n            \"is_yhj\":\"0\",\n            \"yhj_date\":\"\",\n            \"invoiceId\":\"210820530800000002\",\n            \"userId\":\"1951024187\",\n            \"taxNumber\":\"\",\n            \"totalPrice\":\"13.00\",\n            \"isSyncMina\":\"0\",\n            \"gdInvoiceId\":\"\",\n            \"gdPdfUrl\":\"\",\n            \"createAt\":\"2021-08-20 14:45:29\",\n            \"updateAt\":\"2021-08-20 14:45:29\",\n            \"gdResult\":\"\",\n            \"remind\":\"\"\n        },\n        \"invoiceOrderData\":[\n            {\n                \"id\":\"264\",\n                \"brand\":\"优寄申通\",\n                \"price\":\"6.50\",\n                \"invoiceId\":\"210820530800000002\",\n                \"brandType\":\"express\",\n                \"wayBill\":\"772009544691732\",\n                \"orderId\":\"509045939600002\",\n                \"sendName\":\"黄生12\",\n                \"sendMobile\":\"15201736791\",\n                \"sendAddress\":\"上海市上海市松江区你你你莫莫莫\",\n                \"receiverName\":\"保存1\",\n                \"receiverMobile\":\"15201736791\",\n                \"receiverAddress\":\"上海上海长宁同协路269号\",\n                \"orderTime\":\"2021-02-23 10:13:22\",\n                \"createAt\":\"2021-08-20 14:45:29\",\n                \"updateAt\":\"2021-08-20 14:45:29\"\n            },\n            {\n                \"id\":\"265\",\n                \"brand\":\"优寄申通\",\n                \"price\":\"6.50\",\n                \"invoiceId\":\"210820530800000002\",\n                \"brandType\":\"express\",\n                \"wayBill\":\"772009544691732\",\n                \"orderId\":\"509046159200001\",\n                \"sendName\":\"黄生12\",\n                \"sendMobile\":\"15201736791\",\n                \"sendAddress\":\"上海市上海市松江区你你你莫莫莫\",\n                \"receiverName\":\"公众号\",\n                \"receiverMobile\":\"15201736791\",\n                \"receiverAddress\":\"上海上海长宁同协路269号\",\n                \"orderTime\":\"2021-02-23 10:13:22\",\n                \"createAt\":\"2021-08-20 14:45:29\",\n                \"updateAt\":\"2021-08-20 14:45:29\"\n            }\n        ]\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "18ce4314-7f98-4e0a-b140-16acf819af44", "ruleName": "dgs-发票-可开发票订单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/getInvoiceOrders", "filterId": "a51cf190-0cd7-4ea4-b7e5-742901852313", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [\n        {\n            \"orderId\":\"705075449607414\",\n            \"brand\":\"优寄极兔\",\n            \"price\":\"13.00\",\n            \"dateTime\":\"2022-05-07 15:08:16\",\n            \"sendName\":\"分类\",\n            \"sendMobile\":\"13816055287\",\n            \"receiverName\":\"还能干嘛\",\n            \"receiverMobile\":\"18516390739\",\n            \"sendAddress\":\"浙江省温州市乐清市新泾镇北翟路980弄76号通协小区\",\n            \"receiverAddress\":\"浙江省温州市乐清市给您明咯我了在老哇路\",\n            \"brandType\":\"express\",\n            waybillNo: '12731286371828'\n        },\n        {\n            \"orderId\":\"705067717607919\",\n            \"brand\":\"优寄德邦\",\n            \"price\":\"16.80\",\n            \"dateTime\":\"2022-05-06 21:26:16\",\n            \"sendName\":\"格局\",\n            \"sendMobile\":\"15201736791\",\n            \"receiverName\":\"哦搜狗\",\n            \"receiverMobile\":\"15201736791\",\n            \"sendAddress\":\"浙江省杭州市拱墅区新泾镇松井口腔建滔商业广场\",\n            \"receiverAddress\":\"黑龙江省哈尔滨市南岗区红明敏让我给你\",\n            \"brandType\":\"express\"\n        },\n        {\n            \"orderId\":\"705066623102053\",\n            \"brand\":\"优寄德邦\",\n            \"price\":\"7.00\",\n            \"dateTime\":\"2022-05-06 18:23:51\",\n            \"sendName\":\"格局\",\n            \"sendMobile\":\"15201736791\",\n            \"receiverName\":\"红\",\n            \"receiverMobile\":\"15201736791\",\n            \"sendAddress\":\"浙江省杭州市拱墅区新泾镇松井口腔建滔商业广场\",\n            \"receiverAddress\":\"浙江省杭州市上城区严官巷34号\",\n            \"brandType\":\"express\"\n        }\n    ],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "38ead13f-7933-4445-aa34-49db68b1f2b1", "ruleName": "dgs-发票-历史发票列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Invoice/historyInvoice", "filterId": "b48686f1-5746-44d5-8fa5-65ffc61bcd2b", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": ,\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: [        {\n            \"id\":\"230\",\n            \"status\":\"开票成功\",\n            \"type\":\"company\",\n            \"title\":\"睡觉咯\",\n            \"remark\":\"龙\",\n            \"content\":\"收派服务费\",\n            \"mobile\":\"\",\n            \"email\":\"<EMAIL>\",\n            \"drawer\":\"快宝网络科技有限公司\",\n            \"gd_invoice_id\":\"\",\n            \"ticket_sn\":\"0\",\n            \"is_yhj\":\"0\",\n            \"yhj_date\":\"\",\n            \"userId\":\"1950998\",\n            \"taxNumber\":\"122576664646414\",\n            \"totalPrice\":\"1.01\",\n            \"isSyncMina\":\"0\",\n            \"createAt\":\"2021-08-25 15:00:44\",\n            \"updateAt\":\"2021-08-25 15:00:44\",\n            \"invoiceId\":\"210823470600000002\",\n            \"gdPdfUrl\":\"\",\n            \"gdResult\":\"\"\n        },\n        {\n            \"id\":\"231\",\n            \"status\":\"开票失败\",\n            \"type\":\"preson\",\n            \"title\":\"快宝（上海）网络技术有限公司\",\n            \"remark\":\"\",\n            \"content\":\"收派服务费\",\n            \"mobile\":\"\",\n            \"email\":\"<EMAIL>\",\n            \"drawer\":\"快宝网络科技有限公司\",\n            \"gd_invoice_id\":\"\",\n            \"ticket_sn\":\"0\",\n            \"is_yhj\":\"0\",\n            \"yhj_date\":\"\",\n            \"userId\":\"1950998\",\n            \"taxNumber\":\"\",\n            \"totalPrice\":\"18.00\",\n            \"isSyncMina\":\"0\",\n            \"createAt\":\"2021-08-25 15:00:39\",\n            \"updateAt\":\"2021-08-25 15:00:39\",\n            \"invoiceId\":\"210823389800000003\",\n            \"gdPdfUrl\":\"\",\n            \"gdResult\":\"\"\n        },\n        {\n            \"id\":\"233\",\n            \"status\":\"开票失败\",\n            \"type\":\"company\",\n            \"title\":\"快宝（上海）网络技术有限公司\",\n            \"remark\":\"\",\n            \"content\":\"收派服务费\",\n            \"mobile\":\"\",\n            \"email\":\"<EMAIL>\",\n            \"drawer\":\"快宝网络科技有限公司\",\n            \"gd_invoice_id\":\"\",\n            \"ticket_sn\":\"0\",\n            \"is_yhj\":\"0\",\n            \"yhj_date\":\"\",\n            \"userId\":\"1950998\",\n            \"taxNumber\":\"616464649166464\",\n            \"totalPrice\":\"8.00\",\n            \"isSyncMina\":\"0\",\n            \"createAt\":\"2021-08-25 15:07:07\",\n            \"updateAt\":\"2021-08-25 15:07:07\",\n            \"invoiceId\":\"210825815400000001\",\n            \"gdPdfUrl\":\"\",\n            \"gdResult\":\"\"\n        },\n        {\n            \"id\":\"235\",\n            \"status\":\"申请中\",\n            \"type\":\"company\",\n            \"title\":\"快宝（上海）网络技术有限公司\",\n            \"remark\":\"\",\n            \"content\":\"收派服务费\",\n            \"mobile\":\"\",\n            \"email\":\"<EMAIL>\",\n            \"drawer\":\"快宝网络科技有限公司\",\n            \"gd_invoice_id\":\"\",\n            \"ticket_sn\":\"0\",\n            \"is_yhj\":\"0\",\n            \"yhj_date\":\"\",\n            \"userId\":\"1950998\",\n            \"taxNumber\":\"913101050559323669\",\n            \"totalPrice\":\"41.50\",\n            \"isSyncMina\":\"0\",\n            \"createAt\":\"2022-01-12 14:52:37\",\n            \"updateAt\":\"2022-01-12 14:52:37\",\n            \"invoiceId\":\"220112418600000000\",\n            \"gdPdfUrl\":\"\",\n            \"gdResult\":\"\"\n        }],\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "329caa4c-8e98-4cd2-b4c6-c790865b4b61", "ruleName": "dgs-我的-团队信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/RegimentUser/detail", "filterId": "4187fab0-bf00-45bc-8d25-22d745b70f4c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    code: 0,\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      user_id: '18',\n      user_name: '杜国松',\n      nackname: '松孩儿',\n      avatar_url: 'https://upload.kuaidihelp.com/avatar/2022/04/29/21/57/52/user_66601796.jpg',\n      mobile: '13783636923',\n      is_admin: 1,\n      regiment_admin_name: 'Express',\n      regiment_admin_mobile: '12345678910',\n      regiment_admin_avatar_url: 'https://upload.kuaidihelp.com/avatar/2022/04/29/21/57/52/user_66601796.jpg',\n      wx_card: 'https://upload.kuaidihelp.com/package_pics/2023/03/14/3359322088323941640fe3f1338f99203897124.jpg'\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "62b27067-33f2-4b96-bfbc-7d89e3658fd1", "ruleName": "dgs-我的-保证金信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/detail", "filterId": "aa659239-e3fc-44c3-b455-954b293cf034", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      price: 1999,\n      bail_price: 199,\n      total_price: 2198\n    },\n    msg: 'success'\n  },\n  \"statusCode\": \"200\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bb6fc18d-ec96-4936-af01-1883a1f078f2", "ruleName": "xqx-更新用户信息接口", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/fillUserInfo", "filterId": "eac2aac5-3466-40ed-b25b-228e8c641cc0", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":{\n    data: {\n      \"nickname\":\"hhhhh\",\n      \"avatar_url\":\"http://mp.weixin.qq.com/fasdafafds\"\n    },\n    code: 0,\n    msg: '更新session失败1'\n  }\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "429bc85d-fb93-4883-96b0-76ba<PERSON>a64bc7", "ruleName": "xqx-登录接口", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/api/User/login", "filterId": "975e1697-dddb-4fa4-9660-138a0473e532", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":\n    {\n      \"user_id\":\"2\",\n      \"nickname\":\"\",\n      \"mobile\":\"\",\n      \"avatar_url\": \"\",\n      \"kb_id\":\"124321495\",\n      \"is_admin\":\"1\",\n      \"openid\":\"ozHEa5e3C0abUQF6vdZIZV3Miy48\",\n      \"regiment_id\":\"3\",\n      \"session_id\":\"39c4057a5569b2d3134b203ab1415c15\",\n      \"session_salt\":\"0750cb0c749e2d0bf338269c0652f18f\",\n      \"refresh_session_id\":\"801b4e08d0c6b052d06e353c70de5c8f\",\n      \"expire_at\":\"2022-05-20 21:56:21\"\n    }\n  }"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":\n    {\"code\":0,\"msg\":\"成功\",\"data\":{\n      \"user_id\": \"1481820\",\n      \"nickname\": \"呵呵哈哈哈\",\n      \"mobile\": \"\",\n      \"avatar_url\": \"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n      \"kb_id\": \"218972960\",\n      \"is_admin\": \"1\",\n      \"openid\": \"ozHEa5c7EQ6Dooblr12shoPSDLj4\",\n      \"regiment_id\": \"10\",\n      \"league_id\": 20,\n      \"league_info\": \"{\\\"league_id\\\":\\\"36\\\",\\\"user_id\\\":\\\"454\\\",\\\"regiment_id\\\":\\\"246\\\",\\\"league_name\\\":\\\"王现勇加盟商\\\",\\\"contacts_name\\\":\\\"17601614094\\\",\\\"contacts_mobile\\\":\\\"\\\",\\\"create_time\\\":\\\"2023-06-15 15:22:25\\\",\\\"invite_str\\\":\\\"6I7qFddXTPCX4guo5tDUKA\\\"}\",\n      \"session_id\": \"8da823911dc099f9353fea8345053b8d11\",\n      \"session_salt\": \"e8adca74a38b10476d401e42d72348b311\",\n      \"refresh_session_id\": \"c1bad0f288d2d1c402f0ab82045733c611\",\n      \"expire_at\": \"2022-12-20 10:04:09\",\n      \"bind_unionid\": 1,\n      \"regiment_realname_status\": \"0\",\n      'is_league': 1,\n      'hide_mobile': 1,\n      'is_custom': 0,\n      \"invite_user\": \"QMLxfrxmKcIqiN2hcSGi0w\",\n      \"is_partner\": 0,\n      }}\n  }"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "8962b1e9-0288-4799-bff7-b29735a6ce3f", "ruleName": "xqx-地址列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/AddressBook/list", "filterId": "0c0cd7be-1112-4e70-bb8b-bf50b597d8fa", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    'data': {\n      'list|10': [{\n        \"address\":\"北地路2000弄51zhi\",\n        \"city\":\"上海市\",\n        \"county\":\"闵行区\",\n        \"create_time\":\"2022-05-13 03:41:18\",\n        \"hash_val\":\"2735ba4285f883641624bb726eeef4e1\",\n        \"id|+1\":1,\n        \"is_deleted\":\"0\",\n        \"is_top\":\"0\",\n        \"mobile\":\"17521161691\",\n        \"name\":\"@cname\",\n        \"note\":null,\n        \"province\":\"上海市\",\n        \"tel\":\"3513693\",\n        \"update_time\":\"2022-05-13 03:49:57\",\n        \"user_id\":\"691\"\n      }],\n      'page': 1,\n    },\n    'code': 0,\n    'msg': 'success'\n  },\n  \"code\": \"0\",\n  \"msg\": \"success\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}