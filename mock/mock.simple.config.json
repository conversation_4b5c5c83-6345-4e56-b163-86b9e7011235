{"useApiMock": false, "apiMockConfig": {"globalOpen": false, "rules": [{"ruleId": "0ccb9dba-ae94-4bb4-8249-d08c880d2063", "ruleName": "注销异常订单统计", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/unfinishedOrderTypeStatistic", "filterId": "aace0e71-51dd-4013-ab87-647829eefbbd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"uncollected_count\": \"16\",\n        \"unsigned_count\": \"2\",\n        \"unsettled_count\": 0,\n        \"unconfirmed_count\": 0\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "4ac33784-339d-4de6-ba7d-20556c56a9b4", "ruleName": "检查注销状态", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/User/logoutCondition", "filterId": "b7cb44cd-03db-4e99-9984-fa9c7ce4ad11", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"order\": false,\n        \"wallet\": true,\n        \"account\": [\n\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b950f443-396b-40ab-ae7c-a088eec33c1d", "ruleName": "流水导出记录", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Bill/paidRecordExportList", "filterId": "cc424559-573a-4c72-a770-b3c4f5c24974", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": [\n        {\n            \"task_id\": \"1\",\n            \"task_file\": \"https:\\/\\/upload-test.kuaidihelp.com\\/regiment\\/wallet_excel\\/\\/upload\\/regiment\\/2024\\/11\\/04\\/13044158533218027167289a3e2e6ad0232009455.xlsx\",\n            \"export_file\": \"https:\\/\\/upload-test.kuaidihelp.com\\/regiment\\/wallet_excel\\/\\/Users\\/<USER>\\/Desktop\\/.\\/99b92ca93599b867a6ac773f6cd0b534.xlsx\",\n            \"error_reason\": \"\",\n            \"remark\": \"这里是一段非常长的导出备注\",\n            \"import_time\": \"2024-11-04 17:56:14\",\n            \"export_time\": \"2024-11-05 10:49:54\",\n            \"success_count\": 10,\n            \"total_count\": 10,\n            \"status\": \"2\"\n        },\n        {\n            \"task_id\": \"1\",\n            \"task_file\": \"https:\\/\\/upload-test.kuaidihelp.com\\/regiment\\/wallet_excel\\/\\/upload\\/regiment\\/2024\\/11\\/04\\/13044158533218027167289a3e2e6ad0232009455.xlsx\",\n            \"export_file\": \"\",\n            \"error_reason\": \"单号非常的不合法\",\n            \"remark\": \"\",\n            \"import_time\": \"2024-11-04 17:56:14\",\n            \"export_time\": \"2024-11-05 10:49:54\",\n            \"success_count\": 10,\n            \"total_count\": 10,\n            \"status\": \"2\"\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "806ace50-37d8-4397-a4e6-2648385c51fc", "ruleName": "领取添加红包活动奖励", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/receiveRewardRequest", "filterId": "834fcf10-2311-4a73-a478-c75083ebfe19", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "895cfbbf-9927-4bb0-9c59-86d46c49ad6a", "ruleName": "检查是否获取过添加小程序活动", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/checkIsReceiveAddMyMiniAppReward", "filterId": "22fb82a2-9ee5-4716-97dd-9fd3992f0f3c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"is_new\": 0,\n        \"is_order\": 0,\n        \"is_coupon\": 0,\n        \"is_receive\": 0,\n        \"is_use\": 0,\n        \"coupon_nums\": 1914\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "e34aec8f-fcf8-498c-a7c6-e058bb9ca355", "ruleName": "优惠券列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/cardList", "filterId": "bf36afe3-99d7-463c-9c61-dbd1dc28d0c5", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n      \"list\":[\n        {\n            \"id\": \"1951024962\",\n            \"coupon_id\": \"35\",\n            \"coupon_num\": \"1\",\n            \"expiration_time\": \"2024-11-01 23:59:59\",\n            \"extra\": \"\",\n            \"coupon_info\": {\n                \"coupon_id\": \"35\",\n                \"activity\": \"inflate_coupon\",\n                \"brand\": \"jd\",\n                \"brand_name\": \"京东快递\",\n                \"rate\": \"7.5\",\n                \"rate_type\": \"discount\",\n                \"max_discount_money\": \"0.00\",\n                \"rebate\": \"0.00\",\n                \"rebate_type\": \"\",\n                \"pay_method\": \"2\",\n                \"min_freight\": \"0.00\",\n                \"max_freight\": \"0.00\",\n                \"channel\": \"mina\",\n                \"card_name\": \"京东快递\",\n                \"desc\": [\n                    \"1、周末特惠福利券，不与其他活动优惠同享。\",\n                    \"2、本券适用快递公司有: 京东快递。\",\n                    \"3、本券为平台补贴，限在微快递微信小程序寄快递线上微信支付分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用；\",\n                    \"4、多张优惠券不可叠加使用，本券仅限“寄快递”业务使用，批量寄件、商家件、电商退货不可使用本优惠券。\",\n                    \"5、京东快递限重30KG，货物超出限重无法使用优惠券，成功下单将按照官方无折扣原价计费扣款。\"\n                ],\n                \"note\": \"仅限微信小程序寄件专享\",\n                \"card_tag\": \"\"\n            }\n        },\n        {\n            \"id\": \"1951024961\",\n            \"coupon_id\": \"36\",\n            \"coupon_num\": \"1\",\n            \"expiration_time\": \"2024-11-01 23:59:59\",\n            \"extra\": \"\",\n            \"coupon_info\": {\n                \"coupon_id\": \"36\",\n                \"activity\": \"inflate_coupon\",\n                \"brand\": \"ems\",\n                \"brand_name\": \"EMS快递\",\n                \"rate\": \"8.5\",\n                \"rate_type\": \"discount\",\n                \"max_discount_money\": \"0.00\",\n                \"rebate\": \"0.00\",\n                \"rebate_type\": \"\",\n                \"pay_method\": \"2\",\n                \"min_freight\": \"0.00\",\n                \"max_freight\": \"0.00\",\n                \"channel\": \"mina\",\n                \"card_name\": \"EMS快递\",\n                \"desc\": [\n                    \"1、周末特惠福利券，不与其他活动优惠同享。\",\n                    \"2、本券适用快递公司有: EMS快递。\",\n                    \"3、本券为平台补贴，限在微快递微信小程序寄快递线上微信支付分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用；\",\n                    \"4、多张优惠券不可叠加使用，本券仅限“寄快递”业务使用，批量寄件、商家件、电商退货不可使用本优惠券。\",\n                    \"5、EMS快递限重40KG，货物超出限重无法使用优惠券，成功下单将按照官方无折扣原价计费扣款。\"\n                ],\n                \"note\": \"仅限微信小程序寄件专享\",\n                \"card_tag\": \"\"\n            }\n        },\n        {\n            \"id\": \"1951024960\",\n            \"coupon_id\": \"34\",\n            \"coupon_num\": \"1\",\n            \"expiration_time\": \"2024-11-01 23:59:59\",\n            \"extra\": \"\",\n            \"coupon_info\": {\n                \"coupon_id\": \"34\",\n                \"activity\": \"inflate_coupon\",\n                \"brand\": \"dp\",\n                \"brand_name\": \"德邦快递\",\n                \"rate\": \"8.3\",\n                \"rate_type\": \"discount\",\n                \"max_discount_money\": \"0.00\",\n                \"rebate\": \"0.00\",\n                \"rebate_type\": \"\",\n                \"pay_method\": \"2\",\n                \"min_freight\": \"0.00\",\n                \"max_freight\": \"0.00\",\n                \"channel\": \"mina\",\n                \"card_name\": \"德邦快递\",\n                \"desc\": [\n                    \"1、周末特惠福利券，不与其他活动优惠同享。\",\n                    \"2、本券适用快递公司有: 德邦快递。\",\n                    \"3、本券为平台补贴，限在微快递微信小程序寄快递线上微信支付分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用；\",\n                    \"4、多张优惠券不可叠加使用，本券仅限“寄快递”业务使用，批量寄件、商家件、电商退货不可使用本优惠券。\",\n                    \"5、德邦快递限重60KG，货物超出限重无法使用优惠券，成功下单将按照官方无折扣原价计费扣款。\"\n                ],\n                \"note\": \"仅限微信小程序寄件专享\",\n                \"card_tag\": \"\"\n            }\n        }\n    ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6e44031a-3376-412f-a3b3-09e10e22b0d5", "ruleName": "批量下单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/BatchOrder/batchSubmit", "filterId": "4f0b5f32-9495-44a6-b21e-c8ed258cad12", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 202410151009,\n    \"msg\": \"成功\",\n    \"data\": {\n        \n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7a7ed9a4-5a55-4d58-8522-24ca9ffecf9d", "ruleName": "订单详情-待补款", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentInfo", "filterId": "dbb1ad88-12aa-49d1-b204-666cf1937cda", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\t\"code\": 0,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"is_exception_sign_order\": 3,\n\t\t\"order_status\": 1,\n\t\t\"logistic_status\": 3,\n\t\t\"logistic_status_txt\": \"已下单\",\n    \"order_mark\":\"cngg\",\n\t\t\"order_number\": \"903186201408931\",\n\t\t\"user_id\": \"1282\",\n\t\t\"brand\": \"ems\",\n\t\t\"waybill\": \"1243608197935\",\n\t\t\"delivery_type\": \"\",\n\t\t\"shipper_name\": \"阿里\",\n\t\t\"shipper_tel\": \"\",\n\t\t\"shipper_mobile\": \"15556668865\",\n\t\t\"shipper_province\": \"浙江省\",\n\t\t\"shipper_city\": \"绍兴市\",\n\t\t\"shipper_county\": \"上虞区\",\n\t\t\"shipper_address\": \"新泾镇SKYBRIDGEHQ天会3号楼SkybridgeHQ天会快宝王现勇\",\n\t\t\"change_shipper_address_order\": 0,\n\t\t\"shipping_name\": \"理工\",\n\t\t\"shipping_tel\": \"\",\n\t\t\"shipping_mobile\": \"16464616555\",\n\t\t\"shipping_province\": \"上海市\",\n\t\t\"shipping_city\": \"上海市\",\n\t\t\"shipping_county\": \"长宁区\",\n\t\t\"shipping_address\": \"咯给你摸够咯够咯哄你了手机\",\n\t\t\"pay_status\": 1,\n\t\t\"package_weight\": 1,\n\t\t\"package_info\": \"日用品\",\n\t\t\"order_package_pics\": [],\n\t\t\"package_note\": \"(此订单月结，开单后请勿更改产品和重量体积，若运输途中更改参数导致费用增加，额外产生的金额到付。运输途中保持月结金额不变。若是有异常联系13122911354)\",\n\t\t\"freight\": \"14.00\",\n\t\t\"charging_weight\": 2,\n\t\t\"place_volume\": 0,\n\t\t\"settlement_volume\": 0,\n\t\t\"f_fee\": \"12.00\",\n\t\t\"s_fee\": \"2.00\",\n\t\t\"f_kg\": 1,\n\t\t\"regiment_estimate_price\": 9.96,\n\t\t\"regiment_wait_pay_price\": 1,\n\t\t\"regiment_pay_price\": 11.62,\n\t\t\"other_money\": 0,\n\t\t\"pay_price\": 0,\n\t\t\"wait_pay_price\": 14,\n\t\t\"claiming_value\": 0,\n\t\t\"warrant_price\": 0,\n\t\t\"real_claiming_value\": 0,\n\t\t\"real_warrant_price\": 0,\n\t\t\"calculate_price_type\": 1,\n\t\t\"regiment_profit\": \"2.38\",\n\t\t\"settlement_price_details\": [],\n\t\t\"collect_courier_mobile\": \"18067268053\",\n\t\t\"collect_courier_name\": \"陈佳锋\",\n\t\t\"reserve_start_time\": \"2024-03-19 08:00:00\",\n\t\t\"reserve_end_time\": \"2024-03-19 09:00:00\",\n\t\t\"reserve_time_desc\": \"\",\n\t\t\"create_at\": \"2024-03-18 17:13:34\",\n\t\t\"regiment_remark\": \"\",\n\t\t\"can_label\": 1,\n\t\t\"can_push_pay\": 1,\n\t\t\"arrive_pay\": 0,\n\t\t\"third_create_at\": \"\",\n\t\t\"pay_method\": \"2\",\n\t\t\"order_type\": \"gf\",\n\t\t\"courier\": [],\n\t\t\"channel_type\": null,\n\t\t\"regiment_id\": \"722\",\n\t\t\"batch_number\": \"\",\n\t\t\"batch_number_count\": 0,\n\t\t\"estimate_profit\": \"\",\n\t\t\"certificate_path\": \"\",\n\t\t\"pay_type\": \"\",\n\t\t\"isDiscount\": \"\",\n\t\t\"pickup_code\": \"\",\n\t\t\"order_fee_repeat\": {\n\t\t\t\"id\": \"180\",\n\t\t\t\"order_id\": \"903186201408931\",\n\t\t\t\"charging_weight\": \"5.00\",\n\t\t\t\"settlement_volume\": \"0.00\",\n\t\t\t\"calculate_price_type\": \"1\",\n\t\t\t\"f_fee\": \"12.00\",\n\t\t\t\"s_fee\": \"2.00\",\n\t\t\t\"other_money\": \"0.00\",\n\t\t\t\"settlement_price_details\": [],\n\t\t\t\"original_price\": \"20.00\",\n\t\t\t\"wait_pay_price\": \"20.00\",\n\t\t\t\"regiment_pay_price\": \"16.60\",\n\t\t\t\"regiment_profit\": \"3.40\",\n\t\t\t\"league_pay_price\": \"0.00\",\n\t\t\t\"league_profit\": \"0.00\",\n\t\t\t\"update_at\": \"2024-03-18 17:15:03\",\n\t\t\t\"create_at\": \"2024-03-18 17:15:03\",\n\t\t\t\"head_league_profit\": \"0.00\",\n\t\t\t\"league_is_reward\": \"1\",\n\t\t\t\"delivery_method\": \"1\",\n\t\t\t\"freight\": \"20.00\"\n\t\t},\n\t\t\"cancel_reason\": \"\",\n\t\t\"freightBase\": \"\",\n\t\t\"is_partner_order\": \"\",\n    \"cabinet_record\":{\n      \"pickup_code\":\"5758585\",\n      \"locker_number\":\"010012\",\n\t\t\t\"into_cabinet\":false,\n\t\t\t\"device_id\":\"5478484\",\n\t\t\t\"cabinet_code\":\"447848484\",\n      \"cabinet_id\":\"666888999\"\n    },\n    \"change_address\": {\n\t\t\t\"shipper_province\": \"上海市\",\n\t\t\t\"shipper_city\": \"上海市\",\n\t\t\t\"shipper_county\": \"长宁区\",\n\t\t\t\"shipper_address\": \"天会广场二号楼\",\n\t\t\t\"shipper_mobile\": \"19494546655\",\n\t\t\t\"shipper_tel\": \"\"\n\t\t}\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"brand_type\":\"big_package\",\n        \"pay_method\": \"2\",\n        \"arrive_pay\": 0,\n        \"order_number\": \"908297399105887\",\n        \"user_id\": \"41186406\",\n        \"brand\": \"ky\",\n        \"order_mark\": \"\",\n        \"waybill\": \"KY5000239152317\",\n        \"delivery_type\": \"50\",\n        \"shipper_name\": \"明年\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16164959548\",\n        \"shipper_province\": \"上海市\",\n        \"shipper_city\": \"上海市\",\n        \"shipper_county\": \"浦东新区\",\n        \"shipper_address\": \"秀沿路汤巷馨村8号楼502\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"袖子\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"15497959598\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"闵行区\",\n        \"shipping_address\": \"新虹街道申滨路爱博三村7号楼1002\",\n        \"order_status\": 4,\n        \"pay_status\": 1,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 30,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [\n\n        ],\n        \"package_note\": \"\",\n        \"freight\": \"69.00\",\n        \"charging_weight\": 30,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 51.75,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 59.75,\n        \"other_money\": 8,\n        \"pay_price\": 0,\n        \"wait_pay_price\": 77,\n        \"claiming_value\": 0,\n        \"warrant_price\": 2,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 8,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 17.25,\n        \"settlement_price_details\": [\n            {\n                \"type\": 1,\n                \"fee\": 8,\n                \"name\": \"保价费\"\n            }\n        ],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-30 08:00:00\",\n        \"reserve_end_time\": \"2024-08-30 09:00:00\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-29 20:33:11\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 1,\n        \"third_create_at\": \"\",\n        \"order_type\": \"gf\",\n        \"courier\": [\n\n        ],\n        \"channel_type\": null,\n        \"regiment_id\": \"1008\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"\",\n        \"isDiscount\": \"\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [\n\n        ],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [\n\n        ],\n        \"change_address\": [\n\n        ],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "fcfcc103-9603-47ed-aefd-db0ce0591aaa", "ruleName": "批量标记支付", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Pay/batchMark", "filterId": "c8d5e0ab-c501-44c1-9af6-0c5adcc15f5a", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"success\": [\n\n        ],\n        \"error\": [\n            {\n                \"order_id\": \"908195611904686\",\n                \"message\": \"无权操作！\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"success\": [\n\n        ],\n        \"error\":[]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6de180d2-a7e3-477a-9f45-32efaf4da3fe", "ruleName": "大货物流-快递品牌列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/getBrands", "filterId": "3282704e-50cb-4653-a1cb-a0b93f5610cb", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n  \"code\": 0,\n  \"msg\": \"成功\",\n  \"data\": [\n    {\n      \"brand\": \"bt\",\n      \"hot\": 1,\n      \"message\": \"经济货运30kg起重\",\n      \"type\": \"big_package\",\n      \"pay\": \"2\",\n      \"cutPayDesc\": \"在快递员上门揽收后\",\n      \"weightLimitMax\": \"\",\n      \"weightLimitMin\": \"\",\n      \"isYjkd\": 1,\n      \"arrivePay\": 1,\n      \"payTypes\": [\n        0,\n        1,\n        2\n      ],\n      \"rate\": 0.5,\n      \"rate_type\": \"append\",\n      \"sort_info\": {\n        \"key\": 2,\n        \"title\": \"经济实惠\",\n        \"sub_title\": \"42%的人选择\"\n      },\n      \"product_types\": {\n        \"online\": [],\n        \"offline\": []\n      },\n      \"disable\": 0\n    },\n    {\n      \"brand\": \"sxjd\",\n      \"hot\": 1,\n      \"message\": \"经济货运30kg起重\",\n      \"type\": \"big_package\",\n      \"pay\": \"2\",\n      \"cutPayDesc\": \"在快递员上门揽收后\",\n      \"weightLimitMax\": \"\",\n      \"weightLimitMin\": \"\",\n      \"isYjkd\": 1,\n      \"arrivePay\": 0,\n      \"payTypes\": [\n        0\n      ],\n      \"rate\": 0.5,\n      \"rate_type\": \"append\",\n      \"sort_info\": {\n        \"key\": 2,\n        \"title\": \"经济实惠\",\n        \"sub_title\": \"42%的人选择\"\n      },\n      \"product_types\": {\n        \"online\": [],\n        \"offline\": []\n      },\n      \"disable\": 0\n    },\n    {\n      \"brand\": \"ky\",\n      \"hot\": 1,\n      \"message\": \"经济货运30kg起重\",\n      \"type\": \"big_package\",\n      \"pay\": \"2\",\n      \"cutPayDesc\": \"在快递员上门揽收后\",\n      \"weightLimitMax\": \"\",\n      \"weightLimitMin\": \"\",\n      \"isYjkd\": 1,\n      \"arrivePay\": 1,\n      \"payTypes\": [\n        0,\n        1,\n        2\n      ],\n      \"rate\": 7.5,\n      \"rate_type\": \"append\",\n      \"sort_info\": {\n        \"key\": 2,\n        \"title\": \"经济实惠\",\n        \"sub_title\": \"42%的人选择\"\n      },\n      \"product_types\": {\n        \"online\": [],\n        \"offline\": []\n      },\n      \"disable\": 0\n    },\n    {\n      \"brand\": \"htky\",\n      \"hot\": 1,\n      \"message\": \"经济货运30kg起重\",\n      \"type\": \"big_package\",\n      \"pay\": \"2\",\n      \"cutPayDesc\": \"在快递员上门揽收后\",\n      \"weightLimitMax\": \"\",\n      \"weightLimitMin\": \"\",\n      \"isYjkd\": 1,\n      \"arrivePay\": 0,\n      \"payTypes\": [\n        0\n      ],\n      \"rate\": 7.5,\n      \"rate_type\": \"rate\",\n      \"sort_info\": {\n        \"key\": 2,\n        \"title\": \"经济实惠\",\n        \"sub_title\": \"42%的人选择\"\n      },\n      \"product_types\": {\n        \"online\": [],\n        \"offline\": []\n      },\n      \"disable\": 0\n    }\n  ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "134edfa2-68cb-45cc-918f-d2f20be060b1", "ruleName": "大货物流-报价单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/quotation/batchBigPackageQuotationList", "filterId": "b2646c3f-712a-4b41-ba53-510cbf98f4bc", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": {\n  \"code\": 0,\n  \"msg\": \"成功\",\n  \"data\": [\n    {\n      \"price\": \"45.00\",\n      \"discount_price\": \"45.00\",\n      \"discount_total_amount\": 0,\n      \"f_kg\": 30,\n      \"s_kg\": \"0\",\n      \"f_fee\": \"45\",\n      \"s_fee\": \"1.36\",\n      \"s_total_fee\": \"0.00\",\n      \"original_price\": \"45.00\",\n      \"original_f_fee\": 45,\n      \"original_s_fee\": 1.36,\n      \"original_s_total_fee\": \"0.00\",\n      \"discount_list\": [],\n      \"commission\": \"\",\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"brand\": \"htky\",\n      \"available\": 1,\n      \"delivery_type\": \"\",\n      \"delivery_type_name\": \"\"\n    },\n    {\n      \"price\": \"47\",\n      \"discount_price\": \"47\",\n      \"discount_total_amount\": \"0\",\n      \"f_kg\": 30,\n      \"s_kg\": 0,\n      \"f_fee\": \"\",\n      \"s_fee\": \"1.39\",\n      \"s_total_fee\": 0,\n      \"discount_list\": [],\n      \"original_f_fee\": \"\",\n      \"original_s_fee\": \"\",\n      \"original_price\": \"\",\n      \"original_s_total_fee\": \"\",\n      \"commission\": \"\",\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"brand\": \"sxjd\",\n      \"available\": 1,\n      \"delivery_type\": \"\",\n      \"delivery_type_name\": \"\"\n    },\n    {\n      \"price\": \"69.00\",\n      \"discount_price\": \"69.00\",\n      \"discount_total_amount\": 0,\n      \"f_kg\": 1,\n      \"s_kg\": \"29\",\n      \"f_fee\": \"11\",\n      \"s_fee\": \"2\",\n      \"s_total_fee\": \"58.00\",\n      \"original_price\": \"69.00\",\n      \"original_f_fee\": 11,\n      \"original_s_fee\": 2,\n      \"original_s_total_fee\": \"58.00\",\n      \"discount_list\": [],\n      \"commission\": \"\",\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"brand\": \"ky\",\n      \"available\": 1,\n      \"delivery_type\": 50,\n      \"delivery_type_name\": \"同城次日\"\n    },\n    {\n      \"price\": \"125.76\",\n      \"discount_price\": \"125.76\",\n      \"discount_total_amount\": 0,\n      \"f_kg\": 1,\n      \"s_kg\": \"29\",\n      \"f_fee\": \"84\",\n      \"s_fee\": \"1.44\",\n      \"s_total_fee\": \"41.76\",\n      \"original_price\": \"125.76\",\n      \"original_f_fee\": 84,\n      \"original_s_fee\": 1.44,\n      \"original_s_total_fee\": \"41.76\",\n      \"discount_list\": [],\n      \"commission\": \"\",\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"brand\": \"bt\",\n      \"available\": 1,\n      \"delivery_type\": \"\",\n      \"delivery_type_name\": \"\"\n    },\n    {\n      \"brand\": \"ky\",\n      \"available\": 0,\n      \"price\": 0,\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n      \"delivery_type\": 30,\n      \"delivery_type_name\": \"隔日达\"\n    },\n    {\n      \"brand\": \"ky\",\n      \"available\": 0,\n      \"price\": 0,\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n      \"delivery_type\": 40,\n      \"delivery_type_name\": \"陆运件\"\n    },\n    {\n      \"brand\": \"ky\",\n      \"available\": 0,\n      \"price\": 0,\n      \"service_price\": [\n        {\n          \"service_code\": \"upstairsFee\",\n          \"service_name\": \"上楼费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"teshuquyu_fee\",\n          \"service_name\": \"特殊区域费用\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"lanshouchaoqu\",\n          \"service_name\": \"揽收超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"cqf_fee\",\n          \"service_name\": \"派送超区费\",\n          \"amount\": \"1.00\"\n        },\n        {\n          \"service_code\": \"pickup_way\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        },\n        {\n          \"service_code\": \"insuranceFee\",\n          \"service_name\": \"保价费\",\n          \"amount\": \"0.00\"\n        }\n      ],\n      \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n      \"delivery_type\": 160,\n      \"delivery_type_name\": \"省内次日\"\n    }\n  ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\":{\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": [\n        {\n            \"price\": \"67.36\",\n            \"discount_price\": \"122.00\",\n            \"discount_total_amount\": 0,\n            \"f_kg\": 30,\n            \"s_kg\": \"21\",\n            \"f_fee\": \"43.00\",\n            \"s_fee\": \"1.16\",\n            \"s_total_fee\": \"24.36\",\n            \"original_price\": \"122.00\",\n            \"original_f_fee\": 80,\n            \"original_s_fee\": 2,\n            \"original_s_total_fee\": \"42.00\",\n            \"discount_list\": [\n\n            ],\n            \"commission\": \"\",\n            \"service_price\": [\n                {\n                    \"service_code\": \"upstairsFee\",\n                    \"service_name\": \"上楼费\",\n                    \"amount\": \"0.00\"\n                },\n                {\n                    \"service_code\": \"teshuquyu_fee\",\n                    \"service_name\": \"特殊区域费用\",\n                    \"amount\": \"0.00\"\n                },\n                {\n                    \"service_code\": \"lanshouchaoqu\",\n                    \"service_name\": \"揽收超区费\",\n                    \"amount\": \"0.00\"\n                },\n                {\n                    \"service_code\": \"cqf_fee\",\n                    \"service_name\": \"派送超区费\",\n                    \"amount\": \"0.00\"\n                },\n                {\n                    \"service_code\": \"pickup_way\",\n                    \"service_name\": \"保价费\",\n                    \"amount\": \"0.00\"\n                },\n                {\n                    \"service_code\": \"insuranceFee\",\n                    \"service_name\": \"保价费\",\n                    \"amount\": \"0.00\"\n                }\n            ],\n            \"brand\": \"htky\",\n            \"available\": 1,\n            \"delivery_type\": \"\",\n            \"delivery_type_name\": \"\",\n            \"is_recommend\": 1,\n            \"is_cheap\": 1\n        },\n        {\n            \"price\": 77.52,\n            \"discount_price\": 77.52,\n            \"discount_total_amount\": \"0\",\n            \"f_kg\": 30,\n            \"s_kg\": 21,\n            \"f_fee\": 0,\n            \"s_fee\": \"1.52\",\n            \"s_total_fee\": 0,\n            \"discount_list\": [\n\n            ],\n            \"original_f_fee\": \"\",\n            \"original_s_fee\": \"\",\n            \"original_price\": \"\",\n            \"original_s_total_fee\": \"\",\n            \"commission\": \"\",\n            \"service_price\": [\n                {\n                    \"service_code\": \"back_sign_bill\",\n                    \"service_name\": \"回单费\",\n                    \"amount\": 10\n                },\n                {\n                    \"service_code\": \"pickup_way\",\n                    \"service_name\": \"上楼费\",\n                    \"amount\": 20.4\n                },\n                {\n                    \"service_code\": \"insuranceFee\",\n                    \"service_name\": \"保价费\",\n                    \"amount\": 3\n                }\n            ],\n            \"brand\": \"sxjd\",\n            \"available\": 1,\n            \"delivery_type\": \"\",\n            \"delivery_type_name\": \"\"\n        },\n        {\n            \"price\": \"92.52\",\n            \"discount_price\": \"119.13\",\n            \"discount_total_amount\": 0,\n            \"f_kg\": 30,\n            \"s_kg\": \"21\",\n            \"f_fee\": \"60.81\",\n            \"s_fee\": \"1.51\",\n            \"s_total_fee\": \"31.71\",\n            \"original_price\": \"119.13\",\n            \"original_f_fee\": 87,\n            \"original_s_fee\": 1.53,\n            \"original_s_total_fee\": \"32.13\",\n            \"discount_list\": [\n\n            ],\n            \"commission\": \"\",\n            \"service_price\": [\n                {\n                    \"service_code\": \"back_sign_bill\",\n                    \"service_name\": \"回单费\",\n                    \"amount\": 10\n                },\n                {\n                    \"service_code\": \"pickup_way\",\n                    \"service_name\": \"上楼费\",\n                    \"amount\": 20\n                }\n            ],\n            \"brand\": \"bt\",\n            \"available\": 1,\n            \"delivery_type\": \"\",\n            \"delivery_type_name\": \"\"\n        },\n        {\n            \"price\": \"112.50\",\n            \"discount_price\": \"112.50\",\n            \"discount_total_amount\": 0,\n            \"f_kg\": 1,\n            \"s_kg\": \"50\",\n            \"f_fee\": \"7.50\",\n            \"s_fee\": \"2.10\",\n            \"s_total_fee\": \"105.00\",\n            \"original_price\": \"150.00\",\n            \"original_f_fee\": 10,\n            \"original_s_fee\": 2.8,\n            \"original_s_total_fee\": \"140.00\",\n            \"discount_list\": [\n\n            ],\n            \"commission\": \"\",\n            \"service_price\": [\n                {\n                    \"service_code\": \"insuranceFee\",\n                    \"service_name\": \"保价费\",\n                    \"amount\": \"2\"\n                },\n                {\n                    \"service_name\": \"回单费\",\n                    \"amount\": \"2\"\n                }\n            ],\n            \"brand\": \"ky\",\n            \"available\": 1,\n            \"delivery_type\": 160,\n            \"delivery_type_name\": \"省内次日\"\n        },\n        {\n            \"brand\": \"ky\",\n            \"available\": 0,\n            \"price\": 0,\n            \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n            \"delivery_type\": 30,\n            \"delivery_type_name\": \"隔日达\"\n        },\n        {\n            \"brand\": \"ky\",\n            \"available\": 0,\n            \"price\": 0,\n            \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n            \"delivery_type\": 40,\n            \"delivery_type_name\": \"陆运件\"\n        },\n        {\n            \"brand\": \"ky\",\n            \"available\": 0,\n            \"price\": 0,\n            \"unavailable_msg\": \"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\",\n            \"delivery_type\": 50,\n            \"delivery_type_name\": \"同城次日\"\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "b6a76980-7886-483e-99a8-9c636a36fdea", "ruleName": "xqx-登录接口", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_regiment/api/User/login", "filterId": "975e1697-dddb-4fa4-9660-138a0473e532", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":\n    {\n    \"user_id\":\"2318405\",\n    \"nickname\":\"全寄\",\n    \"avatar_url\":\"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n    \"kb_id\":\"272513974\",\n    \"openid\":\"ozHEa5aq78u35Sy2XBqnu1VK_ZQo\",\n    \"sessionid\":\"0b8cd9793c9bf8c5601b5623723e4a67\",\n    \"mobile\":\"13454120966\",\n    \"regiment_id\":\"14579\",\n    \"is_admin\":\"0\",\n    \"is_league\":0,\n    \"league_id\":\"80\",\n    \"invite_str\":\"kfpIFmvaXh2g20dywnw/mQ\",\n    \"hide_mobile\":0,\n    \"is_custom\":0,\n    \"invite_user\":\"jDf2bVq83MXJclHKDC8wWQ\",\n    \"is_partner\":0\n}\n  }"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"code\":0,\n  \"msg\":\"成功\",\n  \"data\":\n    {\"code\":0,\"msg\":\"成功\",\"data\":{\n      \"user_id\": \"1481820\",\n      \"nickname\": \"呵呵哈哈哈\",\n      \"mobile\": \"\",\n      \"avatar_url\": \"https://img.kuaidihelp.com/qj/miniapp/logo.png\",\n      \"kb_id\": \"218972960\",\n      \"openid\": \"ozHEa5c7EQ6Dooblr12shoPSDLj4\",\n      \"league_info\": \"{\\\"league_id\\\":\\\"36\\\",\\\"user_id\\\":\\\"454\\\",\\\"regiment_id\\\":\\\"246\\\",\\\"league_name\\\":\\\"王现勇加盟商\\\",\\\"contacts_name\\\":\\\"17601614094\\\",\\\"contacts_mobile\\\":\\\"\\\",\\\"create_time\\\":\\\"2023-06-15 15:22:25\\\",\\\"invite_str\\\":\\\"6I7qFddXTPCX4guo5tDUKA\\\"}\",\n      \"session_id\": \"8da823911dc099f9353fea8345053b8d11\",\n      \"session_salt\": \"e8adca74a38b10476d401e42d72348b311\",\n      \"refresh_session_id\": \"c1bad0f288d2d1c402f0ab82045733c611\",\n      \"expire_at\": \"2022-12-20 10:04:09\",\n      \"bind_unionid\": 1,\n      \"regiment_realname_status\": \"0\",\n      'hide_mobile': 1,\n      \"invite_user\": \"QMLxfrxmKcIqiN2hcSGi0w\",\n      \"league_id\": \"10\",\n      \"regiment_id\": \"10\",\n      \"is_admin\": '1',\n      'is_league': 0,\n      \"is_partner\": 0,\n      'is_custom': 0,\n      \"custom_type\": \"\",\n      \"user_platform\":\"jinlianguoji\",\n      \"auth_login_user_id\":1\n      }}\n  }"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "1b175bea-5360-43d5-b5fe-fc5f03b6181d", "ruleName": "xqx-团长订单列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Order/getRegimentList", "filterId": "24472994-56b8-4d4c-8007-58f36e19ebef", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n  \"code\": 0,\n  \"msg\": \"成功\",\n  \"data\": {\n    \"page\": 1,\n    \"pageSize\": 10,\n    \"list\": [\n      {\n        \"is_exception_sign_order\": 1,\n        \"order_number\": \"908195611904686\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6294243681124\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐·牛肉冒菜是一绝(凌空SOHO店)Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 1,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"(此订单月结，开单后请勿更改产品和重量体积，若运输途中更改参数导致费用增加，额外产生的金额到付。运输途中保持月结金额不变。若是有异常联系13122911354)\",\n        \"freight\": \"11.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 8.03,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 8.8,\n        \"other_money\": 0,\n        \"pay_price\": 0,\n        \"wait_pay_price\": 11,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 2,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 2.2,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-19 15:35:49\",\n        \"reserve_end_time\": \"2024-08-19 16:35:49\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-19 15:35:19\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 1,\n        \"arrive_pay\": 0,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"2\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"\",\n        \"isDiscount\": \"\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"非平台订单\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"is_exception_sign_order\": 2,\n        \"order_number\": \"908156104901280\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6458882870384\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐牛肉冒菜是一绝(凌空SOHO店)SkybridgeHQ天会快宝王现勇\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"8.80\",\n        \"s_fee\": \"1.60\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:57:59\",\n        \"reserve_end_time\": \"2024-08-15 17:57:59\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:57:29\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp_rate\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"0.80\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"8折\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"is_exception_sign_order\": 3,\n        \"order_number\": \"908156047300833\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6541213290541\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐牛肉冒菜是一绝(凌空SOHO店)SkybridgeHQ天会快宝王现勇\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:48:23\",\n        \"reserve_end_time\": \"2024-08-15 17:48:23\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:47:53\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"1.80\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"原价\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908156040300832\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6670357692427\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐牛肉冒菜是一绝(凌空SOHO店)SkybridgeHQ天会快宝王现勇\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"8.80\",\n        \"s_fee\": \"1.60\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0.8,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:47:13\",\n        \"reserve_end_time\": \"2024-08-15 17:47:13\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:46:43\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp_rate\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"0.80\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"8折\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908156031700831\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6225483069894\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐牛肉冒菜是一绝(凌空SOHO店)SkybridgeHQ天会快宝王现勇\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 1.8,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:45:47\",\n        \"reserve_end_time\": \"2024-08-15 17:45:47\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:45:17\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"1.80\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"原价\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908155951007465\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6311439284648\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐·牛肉冒菜是一绝(凌空SOHO店)Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:32:20\",\n        \"reserve_end_time\": \"2024-08-15 17:32:20\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:31:50\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"原价\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"非平台订单\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908155941007464\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6717448333373\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐·牛肉冒菜是一绝(凌空SOHO店)Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 1,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"(此订单月结，开单后请勿更改产品和重量体积，若运输途中更改参数导致费用增加，额外产生的金额到付。运输途中保持月结金额不变。若是有异常联系13122911354)\",\n        \"freight\": \"11.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 8.03,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 8.03,\n        \"other_money\": 0,\n        \"pay_price\": 0,\n        \"wait_pay_price\": 11,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 2,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 2.97,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:30:40\",\n        \"reserve_end_time\": \"2024-08-15 17:30:40\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:30:10\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 1,\n        \"arrive_pay\": 0,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"2\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"\",\n        \"isDiscount\": \"\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"非平台订单\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908155922706709\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6429728469723\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐·牛肉冒菜是一绝(凌空SOHO店)Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 1,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"(此订单月结，开单后请勿更改产品和重量体积，若运输途中更改参数导致费用增加，额外产生的金额到付。运输途中保持月结金额不变。若是有异常联系13122911354)\",\n        \"freight\": \"11.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 8.03,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 8.8,\n        \"other_money\": 0,\n        \"pay_price\": 0,\n        \"wait_pay_price\": 11,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 2,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 2.2,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 16:27:37\",\n        \"reserve_end_time\": \"2024-08-15 17:27:37\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 16:27:07\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 1,\n        \"arrive_pay\": 0,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"2\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"\",\n        \"isDiscount\": \"\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"非平台订单\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      },\n      {\n        \"order_number\": \"908155529304691\",\n        \"user_id\": \"41186371\",\n        \"brand\": \"sto\",\n        \"order_mark\": \"\",\n        \"waybill\": \"884000379715192\",\n        \"delivery_type\": \"\",\n        \"shipper_name\": \"西宁\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16437646455\",\n        \"shipper_province\": \"上海市\",\n        \"shipper_city\": \"上海市\",\n        \"shipper_county\": \"长宁区\",\n        \"shipper_address\": \"新泾镇凌空SOHO3号楼Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"铭*\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"154****7655\",\n        \"shipping_province\": \"安徽省\",\n        \"shipping_city\": \"蚌埠市\",\n        \"shipping_county\": \"\",\n        \"shipping_address\": \"\",\n        \"order_status\": 0,\n        \"pay_status\": 1,\n        \"logistic_status\": 0,\n        \"logistic_status_txt\": \"已取消\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"1408.00\",\n        \"charging_weight\": 700,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": 10,\n        \"s_fee\": 2,\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 5.48,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 1408,\n        \"other_money\": 0,\n        \"pay_price\": 0,\n        \"wait_pay_price\": 1408,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 09:00:00\",\n        \"reserve_end_time\": \"2024-08-15 19:00:00\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 15:21:33\",\n        \"regiment_remark\": \"全寄\",\n        \"can_label\": 0,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 0,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"1\",\n        \"order_type\": \"gf\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"\",\n        \"isDiscount\": \"\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"\"\n      },\n      {\n        \"order_number\": \"908154985505137\",\n        \"user_id\": \"41186358\",\n        \"brand\": \"dp\",\n        \"order_mark\": \"\",\n        \"waybill\": \"DPK6388719277605\",\n        \"delivery_type\": \"PACKAGE\",\n        \"shipper_name\": \"匿名\",\n        \"shipper_tel\": \"\",\n        \"shipper_mobile\": \"16496559555\",\n        \"shipper_province\": \"安徽省\",\n        \"shipper_city\": \"合肥市\",\n        \"shipper_county\": \"瑶海区\",\n        \"shipper_address\": \"新泾镇成都你六姐·牛肉冒菜是一绝(凌空SOHO店)Skybridge HQ天会\",\n        \"change_shipper_address_order\": 0,\n        \"shipping_name\": \"几年了\",\n        \"shipping_tel\": \"\",\n        \"shipping_mobile\": \"18795656465\",\n        \"shipping_province\": \"上海市\",\n        \"shipping_city\": \"上海市\",\n        \"shipping_county\": \"长宁区\",\n        \"shipping_address\": \"你一搜搜狗敏敏破\",\n        \"order_status\": 4,\n        \"pay_status\": 2,\n        \"logistic_status\": 3,\n        \"logistic_status_txt\": \"已签收\",\n        \"package_weight\": 1,\n        \"package_info\": \"日用品\",\n        \"order_package_pics\": [],\n        \"package_note\": \"\",\n        \"freight\": \"10.00\",\n        \"charging_weight\": 1,\n        \"place_volume\": 0,\n        \"settlement_volume\": 0,\n        \"f_fee\": \"11.00\",\n        \"s_fee\": \"2.00\",\n        \"f_kg\": 1,\n        \"regiment_estimate_price\": 0,\n        \"regiment_wait_pay_price\": 0,\n        \"regiment_pay_price\": 10,\n        \"other_money\": 0,\n        \"pay_price\": 10,\n        \"wait_pay_price\": 10,\n        \"claiming_value\": 0,\n        \"warrant_price\": 0,\n        \"real_claiming_value\": 0,\n        \"real_warrant_price\": 0,\n        \"calculate_price_type\": 1,\n        \"regiment_profit\": 0,\n        \"settlement_price_details\": [],\n        \"collect_courier_mobile\": \"\",\n        \"collect_courier_name\": \"\",\n        \"reserve_start_time\": \"2024-08-15 13:51:24\",\n        \"reserve_end_time\": \"2024-08-15 14:51:24\",\n        \"reserve_time_desc\": \"\",\n        \"create_at\": \"2024-08-15 13:50:55\",\n        \"regiment_remark\": \"\",\n        \"can_label\": 1,\n        \"can_push_pay\": 0,\n        \"arrive_pay\": 2,\n        \"third_create_at\": \"\",\n        \"pay_method\": \"4\",\n        \"order_type\": \"dp\",\n        \"courier\": [],\n        \"channel_type\": null,\n        \"regiment_id\": \"991\",\n        \"batch_number\": \"\",\n        \"batch_number_count\": 0,\n        \"estimate_profit\": \"\",\n        \"certificate_path\": \"\",\n        \"pay_type\": \"offline\",\n        \"isDiscount\": \"原价\",\n        \"pickup_code\": \"\",\n        \"order_fee_repeat\": [],\n        \"cancel_reason\": \"\",\n        \"freightBase\": \"\",\n        \"is_partner_order\": \"非平台订单\",\n        \"cabinet_record\": [],\n        \"change_address\": [],\n        \"is_exception_sign_order\": 0,\n        \"delivery_type_name\": \"标准快递\"\n      }\n    ],\n    \"total\": 0\n  }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    'data': {\n      \"list|10\": [{\n        \"is_exception_sign_order\":\"1\",\n        \"order_number\":\"@id\",\n        \"user_id|+1\":1,\n        \"brand|1\":['zt', 'sf', 'sto', 'yd', 'jd', 'dp'],\n        \"waybill\":\"SF1354944431476\",\n        \"delivery_type\":\"offer\",\n        \"shipper_name\":\"黄生\",\n        \"shipper_tel\":\"\",\n        \"shipper_mobile\":\"15201736794\",\n        \"shipper_province\":\"湖南省\",\n        \"shipper_city\":\"长沙市\",\n        \"shipper_county\":\"雨花区\",\n        \"shipper_address\":\"桃花塅路168号\",\n        \"shipping_name\":\"黄*\",\n        \"shipping_tel\":\"\",\n        \"shipping_mobile\":\"152****6794\",\n        \"shipping_province\":\"湖南省\",\n        \"shipping_city\":\"长沙市\",\n        \"shipping_county\":\"雨花区\",\n        \"shipping_address\":\"桃花塅路168号\",\n        \"order_status|1\":[0,1,2,3,4,5],\n        \"pay_status|1\":['0','1','2'],\n        \"logistic_status|1\":['0','1','2', '3'],\n        \"logistic_status_txt|1\":['已取消',\"已下单\",'派件中', '已签收'],\n        \"package_weight\":1,\n        \"package_info\":\"日用品\",\n        \"package_note\":\"请带个防水袋请带个防水袋请带个防水袋请带个防水袋请\",\n        \"charging_weight\":0,\n        \"place_volume\":0,\n        \"settlement_volume\":0,\n        \"f_fee\":14.4,\n        \"s_fee\":4,\n        \"regiment_estimate_price\":11.5,\n        \"other_money\":0,\n        \"pay_price\":0,\n        \"wait_pay_price|1\":[0,10,20],\n        \"claiming_value\":0,\n        \"warrant_price\":0,\n        \"real_claiming_value\":0,\n        \"real_warrant_price\":0,\n        \"calculate_price_type\":0,\n        \"collect_courier_mobile\":\"\",\n        \"collect_courier_name\":\"\",\n        \"reserve_start_time\":\"@datetime\",\n        \"reserve_end_time\":\"@datetime\",\n        \"create_at\":\"@datetime\",\n        \"regiment_remark|1\":['','团长给团员的备注'],\n        'can_label':0,\n        'batch_number|1': ['3231', '444', null],\n        'batch_number_count': 10\n      }],\n      page:1,\n      pageSize: 10,\n      total: 100\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "43ecbcee-ad85-42b3-842d-2045c4707bfe", "ruleName": "xqx-团员列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/memberList", "filterId": "3805c591-b8e0-41aa-ac4d-7a8dc67f9fc8", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"list\": [\n            {\n                \"id\": \"2430405\",\n                \"openid\": \"ozHEa5faIRsP_zWXsge5Xw75bfPY\",\n                \"nickname\": \"全寄老太\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_ty6cqC6icVJB0HgzGaYyHWY\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"357507814\",\n                \"complate_at\": \"2024-09-02 10:39:18\",\n                \"create_time\": \"2024-09-02 10:39:17\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄老太\"\n            },\n            {\n                \"id\": \"2430371\",\n                \"openid\": \"ozHEa5Z7q1TcDYOUkSsAN6AKm1E8\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t1Xd2CLLipE1Gt9oPYedCUs\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"357469459\",\n                \"complate_at\": \"2024-09-02 07:20:46\",\n                \"create_time\": \"2024-09-02 07:20:46\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2430313\",\n                \"openid\": \"ozHEa5Uq0ksYnG68nhBd3I8LX6YM\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_twrn1EZwCxzwgb4R1pADGtI\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"357430207\",\n                \"complate_at\": \"2024-09-01 20:26:40\",\n                \"create_time\": \"2024-09-01 20:26:40\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2430146\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2430146\",\n                \"openid\": \"ozHEa5RA2GO7kd5GIOwXDv9I17zY\",\n                \"nickname\": \"全寄晓雪\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t5W06JakmOrXSu7s9jL8IuM\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"357209050\",\n                \"complate_at\": \"2024-08-31 23:17:11\",\n                \"create_time\": \"2024-08-31 23:17:10\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄晓雪\"\n            },\n            {\n                \"id\": \"2424543\",\n                \"openid\": \"ozHEa5a0KWeyAWk89q6FxtXSELWk\",\n                \"nickname\": \"全寄够卑鄙\",\n                \"mobile\": \"18958810101\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/upload.kuaidihelp.com\\/package_pics\\/2024\\/08\\/15\\/38766528981518466bd66e13e57a5279182758.jpg\",\n                \"unionid\": \"oEp1_t2bfsMgAachwoz2l_7lHa2k\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"353110201\",\n                \"complate_at\": \"2024-08-15 10:17:01\",\n                \"create_time\": \"2024-08-15 10:17:00\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄够卑鄙\"\n            },\n            {\n                \"id\": \"2423503\",\n                \"openid\": \"ozHEa5UFpuh2PZvgYzW5O0ogujnY\",\n                \"nickname\": \"全寄刘梅英\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t_DMmpQ0f8kn1-eAQFeRmdc\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"352611421\",\n                \"complate_at\": \"2024-08-12 21:26:26\",\n                \"create_time\": \"2024-08-12 21:26:26\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄刘梅英\"\n            },\n            {\n                \"id\": \"2422988\",\n                \"openid\": \"ozHEa5TRd28_-VNJShBopuBC-I70\",\n                \"nickname\": \"全寄莪不是貔卡丘\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t4WnkA0UWv2Mew8n6QOiQ5E\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"352383255\",\n                \"complate_at\": \"2024-08-11 20:29:04\",\n                \"create_time\": \"2024-08-11 20:29:03\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄莪不是貔卡丘\"\n            },\n            {\n                \"id\": \"2422922\",\n                \"openid\": \"ozHEa5Z-mpyB95TJKHiPA_iQSz-k\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_tw-n0GVcRKh49l_i78h-IM8\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"352287387\",\n                \"complate_at\": \"2024-08-11 14:03:08\",\n                \"create_time\": \"2024-08-11 14:03:08\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2422889\",\n                \"openid\": \"ozHEa5a63ABhttBUp7-UBaW0OWUg\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t1vf-wa5roGsfTaD5IolACA\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"352246670\",\n                \"complate_at\": \"2024-08-11 11:02:25\",\n                \"create_time\": \"2024-08-11 11:02:24\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2418908\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2422492\",\n                \"openid\": \"ozHEa5XSNT3JRgQwkRelQCFATv7E\",\n                \"nickname\": \"全寄杨青\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t2sr9Xn7DPkk12R8TNk_5HU\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"351764973\",\n                \"complate_at\": \"2024-08-09 08:38:04\",\n                \"create_time\": \"2024-08-09 08:38:04\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄杨青\"\n            },\n            {\n                \"id\": \"2421901\",\n                \"openid\": \"ozHEa5StlVTdLywRk2T6B4Sn7-JQ\",\n                \"nickname\": \"全寄李芳\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t137sYf8YQNqJv6CA6SroDk\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"351370868\",\n                \"complate_at\": \"2024-08-06 16:34:11\",\n                \"create_time\": \"2024-08-06 16:34:11\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄李芳\"\n            },\n            {\n                \"id\": \"2421845\",\n                \"openid\": \"ozHEa5WXE6AkTU9JuBOZruBRhpz8\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t4BFKOk1U8EV2b3EHlwQLO0\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"351331625\",\n                \"complate_at\": \"2024-08-06 12:54:24\",\n                \"create_time\": \"2024-08-06 12:54:23\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2421818\",\n                \"openid\": \"ozHEa5fNf57SX5lOlePusiND0QbU\",\n                \"nickname\": \"全寄小富婆\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t3DAKRB--VpmsGbhbRcQMp4\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"351307400\",\n                \"complate_at\": \"2024-08-06 10:34:19\",\n                \"create_time\": \"2024-08-06 10:34:18\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄小富婆\"\n            },\n            {\n                \"id\": \"2421723\",\n                \"openid\": \"ozHEa5TqToO68IYVrUD705n-S-Is\",\n                \"nickname\": \"全寄魏冉\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_twGP2-eHb8RE2efLs_YDEr8\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"351215890\",\n                \"complate_at\": \"2024-08-05 16:55:44\",\n                \"create_time\": \"2024-08-05 16:55:44\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄魏冉\"\n            },\n            {\n                \"id\": \"2421250\",\n                \"openid\": \"ozHEa5RNSLtWW2vMYOpua1cVCRQg\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t3wRgpSolNWBhUlJ-UrIgXk\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350937408\",\n                \"complate_at\": \"2024-08-03 18:24:06\",\n                \"create_time\": \"2024-08-03 18:24:06\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2421249\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2421249\",\n                \"openid\": \"1zHEa5Q8f5BbE3AORaYJakmm9ZrM\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"18959832687#3886267\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"1\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350937186\",\n                \"complate_at\": \"2024-08-03 18:22:50\",\n                \"create_time\": \"2024-08-03 18:22:50\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2420464\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2421013\",\n                \"openid\": \"ozHEa5V9yvG4UXilIR35EIVISQVw\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t_DSGLuEqtCIebwz44TF6jo\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350796751\",\n                \"complate_at\": \"2024-08-02 20:31:29\",\n                \"create_time\": \"2024-08-02 19:03:26\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2420470\",\n                \"openid\": \"ozHEa5cMegSTPlB5PB1dviQAYjXE\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t7fuavd_iZCctB8eVB7LcYY\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350505724\",\n                \"complate_at\": \"2024-07-31 22:02:36\",\n                \"create_time\": \"2024-07-31 22:02:36\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2420464\",\n                \"openid\": \"1zHEa5WxFs-77u2JklT9IWCAGPfM\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"13705037800#2714174\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/upload.kuaidihelp.com\\/\\/package_pics\\/2024\\/08\\/03\\/39613927421890566ae09ce2dd9e8283215397.jpg\",\n                \"unionid\": \"1\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350504784\",\n                \"complate_at\": \"2024-07-31 21:52:25\",\n                \"create_time\": \"2024-07-31 21:52:24\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2419261\",\n                \"openid\": \"ozHEa5dKP4Ka-lgDOt_rh0Mm2Lxg\",\n                \"nickname\": \"&&&\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t7hdS_nzGi56DV54gAcpIjs\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"350095519\",\n                \"complate_at\": \"2024-07-29 12:36:53\",\n                \"create_time\": \"2024-07-29 12:36:52\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"&&&\"\n            },\n            {\n                \"id\": \"2418960\",\n                \"openid\": \"ozHEa5ZOETrlj3KSxJrIbRmSX5hg\",\n                \"nickname\": \"静待花开\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t6AAa9f5eNUduROTZowHjgs\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"349951979\",\n                \"complate_at\": \"2024-07-28 13:16:50\",\n                \"create_time\": \"2024-07-28 13:16:50\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"静待花开\"\n            },\n            {\n                \"id\": \"2418908\",\n                \"openid\": \"ozHEa5VJkFMO3iUk7nOSQl0tPVHY\",\n                \"nickname\": \"沈鑫\",\n                \"mobile\": \"18003208699\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t5qcjM3vyYrFuKD4zn8V8lQ\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"349915897\",\n                \"complate_at\": \"2024-07-28 09:23:48\",\n                \"create_time\": \"2024-07-28 09:23:47\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"沈鑫\"\n            },\n            {\n                \"id\": \"2418373\",\n                \"openid\": \"ozHEa5egcakgurJ1mcmA8-cuT1do\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_twj35HbkJaje7Cg8iiUe5ns\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"349704080\",\n                \"complate_at\": \"2024-07-26 17:37:22\",\n                \"create_time\": \"2024-07-26 17:37:21\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2418156\",\n                \"openid\": \"ozHEa5eTyDjp_eqqQagYcYlE6-78\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n                \"unionid\": \"oEp1_t-7Q_BTbTv8Dy8DmRJMohjk\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"349570269\",\n                \"complate_at\": \"2024-07-25 19:17:48\",\n                \"create_time\": \"2024-07-25 19:17:47\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            },\n            {\n                \"id\": \"2417680\",\n                \"openid\": \"1zHEa5TLqzcSsJc1C27u7ckJ0FKc\",\n                \"nickname\": \"全寄\",\n                \"mobile\": \"18003103056#7608225\",\n                \"gender\": \"未知\",\n                \"language\": \"\",\n                \"city\": \"\",\n                \"province\": \"\",\n                \"country\": \"\",\n                \"avatar_url\": \"https:\\/\\/upload.kuaidihelp.com\\/package_pics\\/2024\\/07\\/24\\/511384648253229066a0565b9d3c45259467308.jpg\",\n                \"unionid\": \"1\",\n                \"channel\": \"wechat_mini\",\n                \"is_admin\": \"0\",\n                \"regiment_id\": \"21879\",\n                \"note\": \"\",\n                \"kb_id\": \"349303934\",\n                \"complate_at\": \"2024-07-24 09:17:29\",\n                \"create_time\": \"2024-07-24 09:17:29\",\n                \"old_regiment_id\": \"0\",\n                \"service_charge_orders\": \"\",\n                \"inviter\": \"share_2417676\",\n                \"platform\": \"regiment\",\n                \"is_black\": 0,\n                \"remarks\": \"全寄\"\n            }\n        ],\n        \"date_num\": {\n            \"2024-09-02\": \"2\",\n            \"2024-09-01\": \"1\",\n            \"2024-08-31\": \"1\",\n            \"2024-08-15\": \"1\",\n            \"2024-08-12\": \"1\",\n            \"2024-08-11\": \"3\",\n            \"2024-08-09\": \"1\",\n            \"2024-08-06\": \"3\",\n            \"2024-08-05\": \"1\",\n            \"2024-08-03\": \"2\",\n            \"2024-08-02\": \"1\",\n            \"2024-07-31\": \"2\",\n            \"2024-07-29\": \"1\",\n            \"2024-07-28\": \"2\",\n            \"2024-07-26\": \"1\",\n            \"2024-07-25\": \"1\",\n            \"2024-07-24\": \"1\"\n        },\n        \"total_num\": \"25\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "fd0b3713-f484-4303-8bbc-a443c4e7183b", "ruleName": "获取张盟主定制品牌折扣", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/CustomInviteStatistic/getBrandConfigLists", "filterId": "99d39223-63eb-4372-90d0-d3bc4f4543c2", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n\t\"code\": 0,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"brand_config\": {\n\t\t\t\"partner\": {\n\t\t\t\t\"discount\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 1,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"append\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.15,\n\t\t\t\t\t\"s_fee\": 0.15\n\t\t\t\t},\n\t\t\t\t\"jd\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.675,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"dp\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.655,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"ems\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.83,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"sto\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"league\": {\n\t\t\t\t\"discount\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 1,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"append\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.25,\n\t\t\t\t\t\"s_fee\": 0.25\n\t\t\t\t},\n\t\t\t\t\"jd\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.68,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"dp\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.66,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"ems\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.86,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"sto\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.1,\n\t\t\t\t\t\"s_fee\": 0.1\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"regiment\": {\n\t\t\t\t\"discount\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 1,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"append\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.35,\n\t\t\t\t\t\"s_fee\": 0.35\n\t\t\t\t},\n\t\t\t\t\"jd\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.69,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"dp\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.68,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"ems\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.88,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"sto\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.2,\n\t\t\t\t\t\"s_fee\": 0.2\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"user\": {\n\t\t\t\t\"discount\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 1,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"append\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 1.03,\n\t\t\t\t\t\"s_fee\": 0.45\n\t\t\t\t},\n\t\t\t\t\"jd\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.75,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"dp\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.7,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"ems\": {\n\t\t\t\t\t\"type\": \"discount\",\n\t\t\t\t\t\"rate\": 0.9,\n\t\t\t\t\t\"s_fee\": 0\n\t\t\t\t},\n\t\t\t\t\"sto\": {\n\t\t\t\t\t\"type\": \"append\",\n\t\t\t\t\t\"rate\": 0.88,\n\t\t\t\t\t\"s_fee\": 0.3\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"discount_brand\": [\"dp\", \"jd\", \"sf\", \"ems\"],\n\t\t\"append_brand\": [\"yt\", \"cnsd\", \"cngg\", \"yd\", \"sto\", \"zt\", \"jt\"]\n\t}\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "3a9454fe-f9f2-4d0d-a0b4-437dd08545d4", "ruleName": "xqx-下单-德邦特殊折扣", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/checkDpOfflineOrder", "filterId": "5369d1f7-c075-40c9-b79d-4632905d6097", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"rate\": [\n            8,\n            10\n        ],\n        \"default_rate\": 10\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data: {\n      status: true,\n      rate: 8\n    }\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "cf77df90-9562-486b-8b2a-af02c3791d4e", "ruleName": "获取快递柜信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Cabinet/codeMatchCabinet", "filterId": "a046ca0e-accb-47bd-a64f-0691440f8224", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"cabinet_id\":\"75\",\n        \"version\":\"3.3.0\",\n        \"name\":\"艾信云通test\",\n        \"province\":\"上海市\",\n        \"city\":\"上海市\",\n        \"area\":\"长宁区\",\n        \"address\":\"天会广场6号楼\",\n        \"longitude\":\"121.354\",\n        \"latitude\":\"31.2209\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c591bed7-3e8b-49cc-abad-ec342c1d4ac2", "ruleName": "提交对公转账信息", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Finance/addCompanyTransfer", "filterId": "ba29020c-73bd-408c-964a-1a5b08f4083c", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 0,\n    \"data\": {},\n    \"msg\": \"success\"\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "94df44c9-e4f8-4b1b-b89a-7d74aab7acb2", "ruleName": "xqx-下单-获取公司品牌列表", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Online/getBrands", "filterId": "b1c27f11-4ed5-4753-9708-11accf20ebb6", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\":{\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n        {\n            \"brand\":\"sf\",\n            \"hot\":1,\n            \"message\":\"预约寄件 快速上门\",\n            \"type\":\"fxj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":8.5,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"offline\":[\n                    {\n                        \"label\":\"标快\",\n                        \"value\":\"offer\"\n                    },\n                    {\n                        \"label\":\"特快\",\n                        \"value\":\"express\"\n                    }\n                ],\n                \"online\":[\n                    {\n                        \"label\":\"标快\",\n                        \"value\":\"offer\"\n                    },\n                    {\n                        \"label\":\"特快\",\n                        \"value\":\"express\"\n                    }\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"sfky\",\n            \"hot\":1,\n            \"message\":\"重货包入户，大件更优惠\",\n            \"type\":\"fxj\",\n            \"pay\":\"2\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"100\",\n            \"weightLimitMin\":\"20\",\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                1,\n                2\n            ],\n            \"rate\":10,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"jd\",\n            \"hot\":1,\n            \"message\":\"优质时效\",\n            \"type\":\"fxj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":7.5,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n                    {\n                        \"label\":\"特惠送\",\n                        \"value\":\"offer\"\n                    },\n                    {\n                        \"label\":\"特快送\",\n                        \"value\":\"express\"\n                    }\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"dp\",\n            \"hot\":1,\n            \"message\":\"大件特惠超值寄\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":7.3,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"ems\",\n            \"hot\":1,\n            \"message\":\"预约寄件 优质时效\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0\n            ],\n            \"rate\":null,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"cnsd\",\n            \"hot\":1,\n            \"message\":\"预约寄件 优质时效\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0\n            ],\n            \"rate\":0.5,\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"yd\",\n            \"hot\":1,\n            \"message\":\"极速取件 贴心服务\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":0.5,\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"yt\",\n            \"hot\":1,\n            \"message\":\"一键下单 快速取件\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":0.5,\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"sto\",\n            \"hot\":1,\n            \"message\":\"安心下单 闪电送达\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":0.5,\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":1\n        },\n        {\n            \"brand\":\"zt\",\n            \"hot\":1,\n            \"message\":\"放心寄件 急速服务\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":0,\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"disable\":0\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n\"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n        {\n            \"brand\":\"sf\",\n            \"hot\":1,\n            \"message\":\"预约寄件 快速上门\",\n            \"type\":\"fxj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"8.5\",\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"offline\":[\n                    {\n                        \"label\":\"标快\",\n                        \"value\":\"offer\"\n                    },\n                    {\n                        \"label\":\"特快\",\n                        \"value\":\"express\"\n                    }\n                ],\n                \"online\":[\n\n                ]\n            },\n            \"insured\":\"offline\",\n            \"appointmentTime\":\"offline\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":5,\n                \"arrive_pay_rate_type\":\"service_charge\",\n                \"role_desc\":\"享受顺丰速运标快及<span style='color:#0C7BFF'>15%</span>特快寄件服务\"\n            },\n            \"online\":{\n                \"rate\":\"8.5\",\n                \"type\":\"rate\",\n                \"desc\":\"8.5折\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"sfky\",\n            \"hot\":1,\n            \"message\":\"重货包入户，大件更优惠\",\n            \"type\":\"fxj\",\n            \"pay\":\"2\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":100,\n            \"weightLimitMin\":20,\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":10,\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"offline\",\n            \"appointmentTime\":\"offline\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受顺丰快运寄件服务\"\n            },\n            \"online\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"10折\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"jd\",\n            \"hot\":1,\n            \"message\":\"优质时效\",\n            \"type\":\"fxj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":1,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"6.8\",\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n                    {\n                        \"label\":\"特惠送\",\n                        \"value\":\"offer\"\n                    },\n                    {\n                        \"label\":\"特快送\",\n                        \"value\":\"express\"\n                    }\n                ]\n            },\n            \"insured\":\"all\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0.4,\n                \"arrive_pay_rate_type\":\"fixed\",\n                \"role_desc\":\"平台根据活动规则对团长每笔签收订单0.4元的返佣奖励\"\n            },\n            \"online\":{\n                \"rate\":\"6.8\",\n                \"type\":\"rate\",\n                \"desc\":\"6.8折\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"dp\",\n            \"hot\":1,\n            \"message\":\"大件特惠超值寄\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"7.3\",\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"all\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0.15,\n                \"arrive_pay_rate_type\":\"rate\",\n                \"role_desc\":\"平台根据活动规则对团长进行订单总运费的15%返佣奖励\"\n            },\n            \"online\":{\n                \"rate\":\"7.3\",\n                \"type\":\"rate\",\n                \"desc\":\"7.3折\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"ems\",\n            \"hot\":1,\n            \"message\":\"预约寄件 优质时效\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"8.3\",\n            \"rate_type\":\"rate\",\n            \"sort_info\":{\n                \"key\":1,\n                \"title\":\"品质时效\",\n                \"sub_title\":\"58%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"all\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受EMS快递寄件服务\"\n            },\n            \"online\":{\n                \"rate\":\"8.3\",\n                \"type\":\"rate\",\n                \"desc\":\"8.3折\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"cnsd\",\n            \"hot\":1,\n            \"message\":\"预约寄件 优质时效\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"0.50\",\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"offline\":{\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受菜鸟速递寄件服务\"\n            },\n            \"online\":{\n                \"f_fee\":\"0.50\",\n                \"s_fee\":\"0.30\",\n                \"type\":\"append\",\n                \"desc\":\"5元起\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"yd\",\n            \"hot\":1,\n            \"message\":\"极速取件 贴心服务\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"0.50\",\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受韵达快递寄件服务\"\n            },\n            \"online\":{\n                \"f_fee\":\"0.50\",\n                \"s_fee\":\"0.30\",\n                \"type\":\"append\",\n                \"desc\":\"5元起\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"yt\",\n            \"hot\":1,\n            \"message\":\"一键下单 快速取件\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"0.50\",\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"online\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受圆通速递寄件服务\"\n            },\n            \"online\":{\n                \"f_fee\":\"0.50\",\n                \"s_fee\":\"0.30\",\n                \"type\":\"append\",\n                \"desc\":\"6元起\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"sto\",\n            \"hot\":1,\n            \"message\":\"安心下单 闪电送达\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"0.50\",\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0.4,\n                \"arrive_pay_rate_type\":\"fixed\",\n                \"role_desc\":\"平台根据活动规则对团长每笔签收订单0.4元的返佣奖励\"\n            },\n            \"online\":{\n                \"f_fee\":\"0.50\",\n                \"s_fee\":\"0.30\",\n                \"type\":\"append\",\n                \"desc\":\"5元起\"\n            },\n            \"disable\":0\n        },\n        {\n            \"brand\":\"zt\",\n            \"hot\":1,\n            \"message\":\"放心寄件 急速服务\",\n            \"type\":\"djj\",\n            \"pay\":\"3\",\n            \"cutPayDesc\":\"在快递员上门揽收后\",\n            \"weightLimitMax\":\"\",\n            \"weightLimitMin\":\"\",\n            \"isYjkd\":1,\n            \"arrivePay\":0,\n            \"payTypes\":[\n                0,\n                2\n            ],\n            \"rate\":\"0.50\",\n            \"rate_type\":\"append\",\n            \"sort_info\":{\n                \"key\":2,\n                \"title\":\"经济实惠\",\n                \"sub_title\":\"42%的人选择\"\n            },\n            \"product_types\":{\n                \"online\":[\n\n                ],\n                \"offline\":[\n\n                ]\n            },\n            \"insured\":\"\",\n            \"appointmentTime\":\"all\",\n            \"offline\":{\n                \"rate\":10,\n                \"type\":\"rate\",\n                \"desc\":\"原价\",\n                \"arrive_pay_rate\":0,\n                \"arrive_pay_rate_type\":\"\",\n                \"role_desc\":\"享受中通快递寄件服务\"\n            },\n            \"online\":{\n                \"f_fee\":\"0.50\",\n                \"s_fee\":\"0.30\",\n                \"type\":\"append\",\n                \"desc\":\"7元起\"\n            },\n            \"disable\":0\n        }\n    ]\n},\n\"statusCode\": \"\",\n\"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "2b2eaa66-86b1-45db-a29d-bc996903e9d6", "ruleName": "xqx-团员详情", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/memberDetail", "filterId": "73a9c72a-454b-41a2-8f96-66696b011cbd", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"调用成功\",\n    \"data\":{\n        \"id\":\"2264485\",\n        \"openid\":\"ozHEa5Ug7iVUHvjKIi5R-4uRxN0E\",\n        \"nickname\":\"全寄\",\n        \"mobile\":\"13810473144\",\n        \"gender\":\"未知\",\n        \"language\":\"\",\n        \"city\":\"\",\n        \"province\":\"\",\n        \"country\":\"\",\n        \"avatar_url\":\"https:\\/\\/img.kuaidihelp.com\\/qj\\/miniapp\\/logo.png\",\n        \"unionid\":\"oEp1_t0xy89tdbxXMKXVuuMXC4gM\",\n        \"channel\":\"wechat_mini\",\n        \"is_admin\":\"0\",\n        \"regiment_id\":\"12834\",\n        \"note\":\"\",\n        \"kb_id\":\"261733950\",\n        \"complate_at\":\"2023-05-16 10:42:03\",\n        \"create_time\":\"2023-05-16 10:40:56\",\n        \"old_regiment_id\":\"0\",\n        \"service_charge_orders\":\"\",\n        \"regiment_user_id\":\"2264485\",\n        \"pay_method\":\"1\",\n        \"audit_order\":\"0\",\n        \"is_black\":\"0\",\n        \"jd_service_charge\":\"0.00\",\n        \"sf_service_charge\":\"2.00\",\n        \"disable_brand\":\"\",\n        \"remarks\":\"全寄\",\n        \"update_time\":\"2023-05-16 10:42:03\",\n        \"order_num\":254474,\n        \"total_freight\":\"2457688.76\",\n        \"regiment_profit\":\"20668.50\",\n        \"wait_pay_price\":\"0.00\",\n        \"brand_freight_config\":[\n            {\n                \"id\":\"648984\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sf\",\n                \"f_fee\":\"9.50\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"discount\",\n                \"area_type\":\"0\",\n                \"create_time\":\"2023-08-24 09:44:57\",\n                \"update_time\":\"2023-08-24 09:44:57\"\n            },\n            {\n                \"id\":\"648985\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sfky\",\n                \"f_fee\":\"10.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"discount\",\n                \"area_type\":\"0\",\n                \"create_time\":\"2023-08-24 09:44:57\",\n                \"update_time\":\"2023-08-24 09:44:57\"\n            },\n            {\n                \"id\":\"648986\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"jd\",\n                \"f_fee\":\"9.50\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"discount\",\n                \"area_type\":\"0\",\n                \"create_time\":\"2023-08-24 09:44:57\",\n                \"update_time\":\"2023-08-24 09:44:57\"\n            },\n            {\n                \"id\":\"648987\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"dp\",\n                \"f_fee\":\"9.20\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"discount\",\n                \"area_type\":\"0\",\n                \"create_time\":\"2023-08-24 09:44:57\",\n                \"update_time\":\"2023-08-24 09:44:57\"\n            },\n            {\n                \"id\":\"648988\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"ems\",\n                \"f_fee\":\"8.60\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"discount\",\n                \"area_type\":\"0\",\n                \"create_time\":\"2023-08-24 09:44:57\",\n                \"update_time\":\"2023-08-24 09:44:57\"\n            },\n            {\n                \"id\":\"648989\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yd\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"1\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648990\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"1\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648991\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sto\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"1\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648992\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"zt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"1\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648993\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yd\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"2\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648994\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"2\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648995\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sto\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"2\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648996\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"zt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"2\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648997\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yd\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"3\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648998\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"3\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"648999\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sto\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"3\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"649000\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"zt\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"3\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"649001\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yd\",\n                \"f_fee\":\"1.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"4\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"649002\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"yt\",\n                \"f_fee\":\"0.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"4\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"649003\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"sto\",\n                \"f_fee\":\"0.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"4\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            },\n            {\n                \"id\":\"649004\",\n                \"regiment_id\":\"12834\",\n                \"regiment_user_id\":\"2264485\",\n                \"brand\":\"zt\",\n                \"f_fee\":\"0.00\",\n                \"s_fee\":\"0.00\",\n                \"fee_type\":\"append\",\n                \"area_type\":\"4\",\n                \"create_time\":\"2023-08-24 09:45:58\",\n                \"update_time\":\"2023-08-24 09:45:58\"\n            }\n        ],\n        \"brand_arrive_pay_config\":[\n            {\n                \"id\":\"1\",\n                \"league_id\":\"93\",\n                \"regiment_id\":\"0\",\n                \"brand\":\"jd\",\n                \"rate\":\"0.30\",\n                \"rate_type\":\"fixed\",\n                \"create_at\":\"2023-09-06 15:57:14\",\n                \"update_at\":\"2023-09-06 15:57:14\"\n            },\n            {\n                \"id\":\"2\",\n                \"league_id\":\"93\",\n                \"regiment_id\":\"0\",\n                \"brand\":\"dp\",\n                \"rate\":\"0.10\",\n                \"rate_type\":\"rate\",\n                \"create_at\":\"2023-09-06 15:57:15\",\n                \"update_at\":\"2023-09-06 15:57:15\"\n            },\n            {\n                \"id\":\"3\",\n                \"league_id\":\"93\",\n                \"regiment_id\":\"0\",\n                \"brand\":\"sto\",\n                \"rate\":\"0.20\",\n                \"rate_type\":\"fixed\",\n                \"create_at\":\"2023-09-06 15:57:15\",\n                \"update_at\":\"2023-09-06 15:57:15\"\n            },\n            {\n                \"id\":\"4\",\n                \"league_id\":\"93\",\n                \"regiment_id\":\"0\",\n                \"brand\":\"sf\",\n                \"rate\":\"0.20\",\n                \"rate_type\":\"fixed\",\n                \"create_at\":\"2023-09-06 15:57:15\",\n                \"update_at\":\"2023-09-06 15:57:15\"\n            }\n        ]\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "a700a2e2-d0d7-45a2-9ad6-0c369b02bdd9", "ruleName": "获取团员服务费", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/api/Team/getMemberServiceCharge", "filterId": "73446104-536a-4e47-935c-10ae80a44dca", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    code: 0,\n    data:1,\n    msg: 'success'\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}