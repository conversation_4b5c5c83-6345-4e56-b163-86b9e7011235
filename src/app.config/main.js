/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/index/index', // 首页

    'pages/order/index', // 订单

    'pages/cps/index', // 生活服务

    'pages/user/index', // 我的

    'pages/webview/index', // webview
  ],
  window: {
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    navigationBarTitleText: '微快递特惠寄',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    custom: process.env.PLATFORM_ENV === 'weapp',
    color: '#323232',
    selectedColor: '#1480FF',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        iconPath: 'assets/tab/home.png',
        selectedIconPath: 'assets/tab/home-active.png',
        text: '首页',
      },
      {
        pagePath: 'pages/order/index',
        iconPath: 'assets/tab/order.png',
        selectedIconPath: 'assets/tab/order-active.png',
        text: '订单',
      },
      {
        pagePath: 'pages/cps/index',
        iconPath: 'assets/tab/cps.png',
        selectedIconPath: 'assets/tab/cps-active.png',
        text: '生活服务',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
  requiredPrivateInfos: ['chooseAddress', 'getLocation'],
};

// 分包
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/index/index',
    pages: [
      'team/create/index',
      'team/invite/index',
      'order/edit/index',
      'order/edit/goods/index',
      'order/edit/brand/index',
      'order/edit/service/index',
      'order/edit/service/dh/index',
      'order/edit/service/dh/desc/index',
      'order/confirm/index',
      'address/index',
      'address/edit/index',
      'address/batch/index',
      'address/import/index',
      'cutting/index',
      'city/index', // 城市选择列表
      'order/result/index',
      // 'team/scan/index', // 证件照解析
      // 'team/face/index', // 人脸识别
      'guide/index', // 注册引导页
      'arrivePay/index', // 顺丰活动详情页
      'export/index',
      'gift/index',
      // 审核问题暂时隐藏页面
      // 'realName/index', // 实名认证页面
      // 查件
      'query/index',
      'query/match/index',
      'query/detail/index',
      'brand/index',
      // 升级团长
      'upgrade/index',
      // 获取短链
      'scheme/index',
      // 关注公众号
      'officialAccount/index',
      // 活动相关
      'activity/index',
      'activity/guide-coupon/index',
      'team/guide/index',
      'query/search/index',
      // 快递柜
      'kdg/express/index',
      //微快递特惠寄快团团张盟主特殊加盟商团队新用户首次登录承接页
      'team/guideZhang/index',
      // 拉新活动
      'activity/recruit/index',
      // 拉新活动-分销版
      'activity/recruit-fenxiao/index',
      // 推广技巧
      'recommend/index',
      // 推广小助手
      'recommend/assistant/index',
      // 客服咨询
      'consulting/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: [
      'order/detail/index',
      'order/pic/index',
      'order/detail/cancel/index',
      'order/listDetail/index',
      // 支付分引导页
      'order/credit-pay/index',
      // 底单
      'order/voucher/index',
      // 优惠券
      'order/card/index',
      // 领取自定义优惠券
      'order/custom-coupon/index',
      // 异常订单列表
      'order/abnormal/index',
      // 短信催收
      'order/remind/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: [
      'invoice/index',
      'invoice/detail/index',
      'invoice/edit/index',
      'invoice/result/index',
      'invoice/title/index',
      'invoice/extra/index',
      'invoice/history/index',

      'team/manage/index',
      'team/manage/detail/index',
      'team/manage/edit/index',
      'team/manage/introduction/index',
      'team/manage/poster/index',
      'team/manage/defaultSetting/index',
      'team/manage/brandSettings/index',
      'team/manage/showBrands/index',
      'team/manage/memberOperate/index',
      'team/manage/memberList/index',
      'team/manage/arrivePaySettings/index', // 到/现付订单价格管理

      'freight/index',
      'freight/query/index',
      'freight/rule/index',

      'closedArea/index',
      'closedArea/result/index',
      'closedArea/selectBrand/index',
      'transform/index', // 转单授权
      // 分佣管理
      'commission/index',
      'commission/list/index',
      'commission/detail/index',
      'commission/right/index',
    ],
  },
  {
    // 包含chart相关
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      'user/account/index',
      'user/account/cash-out/index',
      'user/account/detail/index',
      'user/account/recharge/index',
      'user/account/record/index',
      'user/account/record/detail/index',
      'user/account/filter/index',
      'user/account/import/index',
      'user/account/import-record/index',
      'user/setting/index',
      'user/changePhone/index',
      'user/account/manage/index',
      'user/account/rechargeHistory/index',
      'user/account/experience/index',
      'user/account/experience/guide/index',
      'user/account/experience/bill/index',
      'upgrade/league/index', // 晋升加盟商
      'user/deregister/index',
      'user/account/balanceManagement/index',
      'user/account/historyDetail/index', // 历史明细
      // 注销落地页
      'deregister/index',

      'stats/index',
      'profit/index', // 收益统计
      'profit/detail/index', // 收益统计, 详情列表

      // 加盟商管理
      'franchisee/index',
      // 推广小助手
      'franchisee/promotion/index',
      'franchisee/promotion/regiment/index',
      'franchisee/unPayList/index',

      'pcauth/index', // pc扫码授权

      // 实名
      'realname/index',
      'realname/scan/index',
      // 实名列表
      'realnameList/index',
      // 发圈素材
      'user/material/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/cps/index',
    pages: [
      'cps/order/index',
      'cps/order/listDetail/index',
      'asyncPackage/index',
      'cps/redirect/index',
    ],
    plugins: {
      jtkMovie: {
        version: '1.1.7',
        provider: 'wx89752980e795bfde',
      },
      meishi: {
        version: '1.2.2',
        provider: 'wx5c787b48e6a02a51',
      },
    },
  },
];

if (process.env.PLATFORM_ENV === 'weapp') {
  config.embeddedAppIdList = ['wx5c9beefc805e6c2f'];
}

module.exports = config;
