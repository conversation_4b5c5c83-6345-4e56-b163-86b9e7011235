

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


module.exports = (function () {
  const MODE_ENV = 'main';
  const config = require(`./${MODE_ENV}`);
  const { navigationBarBackgroundColor, backgroundColor } = config.window;
  config.plugins = config.plugins || {};
  // 微信 weapp
  config.window.backgroundColorTop = navigationBarBackgroundColor;
  config.window.backgroundColorBottom = backgroundColor;
  config.window.backgroundTextStyle = 'dark';
  config.permission = {
    'scope.userLocation': {
      desc: '快捷方便的帮助用户获取位置信息',
    },
  };
  config.plugins.speechRecognition = {
    version: '0.3.5',
    provider: 'wx069ba97219f66d99',
  };

  config.networkTimeout = {
    request: 30000,
    connectSocket: 30000,
    uploadFile: 30000,
    downloadFile: 30000,
  };
  config.resizable = false;
  config.debug = false;
  return config;
})();
