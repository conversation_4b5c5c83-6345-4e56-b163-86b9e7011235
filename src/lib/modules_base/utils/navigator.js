/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import resolve404 from '@base/utils/404/';
import Taro from '@tarojs/taro';
import qs from 'qs';
import request from '@base/utils/request';
import { transform404Path } from '@base/utils/404/_utils';
import { fixSubPackagesPath as subToolsFixSubPackagesPath } from 'taro-plugin-sub-tools';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import isFunction from 'lodash/isFunction';
import isString from 'lodash/isString';
import { getPage, reportAnalytics, getUserStateByComplete, noop } from '@base/utils/utils';
import { check } from '@base/utils/rules';
import { pagePaths, utils } from '@base/config';
import { secondaryTabs } from '~/components/_pages/_utils/tab-secondary';

// 链接开头
export const PATH_START_REG = /^\//;

/**
 *
 * @description 待优化：后期将utils下的navigator相关逻辑移入此文件模块
 */

/**
 *
 * @description 获取地址和参数
 * @param {*} url
 * @returns
 */
export function getPathAndParams(url = '') {
  const [path, query] = url.split('?');
  return {
    path,
    params: query ? qs.parse(query) : null,
  };
}

/**
 *
 * @description 获取页面启动参数
 * @returns
 */
export function getLaunchParams(page, opts) {
  const { ignore404 = false } = opts || {};
  const res = Taro.getLaunchOptionsSync() || {};
  const { path = '', query = {}, referrerInfo: { extraData = {} } = {} } = res;
  const { params: $routerParams = {}, path: $routerPath = path } = (page && page.$router) || {};

  // 兼容shortLink链接参数
  let query_obj = {};
  const _query = $routerParams.query || query.query;
  if (_query && isString(_query)) {
    const query_str = decodeURIComponent(_query);
    query_obj = qs.parse(query_str);
  }

  // 因老框架改版，启动页面有404的情况，则需要进行404页面转换
  const { url } = transform404Path({ path });
  if (!ignore404 && $routerPath !== url) {
    // 启动页非当前页时，只返回当前参数
    return $routerParams;
  }
  const { params = {} } = getPathAndParams(path);

  return {
    ...query,
    ...extraData,
    ...query_obj,
    ...params,
    ...$routerParams,
  };
}

/**
 *
 * @description 路径增加"/"
 * @param {*} path
 * @returns
 */
function addPathStart(path = '') {
  return PATH_START_REG.test(path) ? path : '/' + path;
}
/**
 * @description 创建完整路径
 */
export function createCompletePath(path, suffix = 'index') {
  // 固定链接，则不用传入suffix
  if (!suffix) {
    return path;
  }
  return check('path', path).code === 0
    ? addPathStart(path)
    : `/pages/${path}/${suffix !== 'default' ? suffix : path.split('/').slice(-1)[0]}`;
}

/**
 *
 * @description 检查是否为tab页
 * @param {*} path
 * @returns
 */
export const checkIsTabPage = (path) => {
  const tabs = ['index', 'order', 'cps', 'user'];
  return tabs.map((item) => createCompletePath(item)).includes(path);
};

/**
 *
 * @description 检查是否为二级跳转tab
 * @param {*} path
 * @returns
 */
export function checkIsSecondaryTabPage(path) {
  return secondaryTabs.map((item) => createCompletePath(item)).includes(path);
}

/**
 * 跳转，支持简称，配合utils.config.routers配置项使用
 * params.target: blank => navigateTo ,self =>redirectTo,tab=>switchTab,launch=>reLaunch
 * params.url: 无值 Taro.navigateBack
 * params.options: 参数
 * params: 参照Taro.navigateTo
 * post: 回传数据相关，触发对应页面 onPostMessage
 * report: 上报数据相关配置，针对调整小程序操作
 *  */
export function fixSubPackagesPath(url) {
  const { config: { subPackages = [] } = {} } = Taro.getApp() || {};
  return subToolsFixSubPackagesPath(url, subPackages);
}
/**
 * @description 检查页面是否已经打开
 */
function checkPageIsOpened(fixedPath, target, params) {
  if (target === 'tab') return false;
  const pages = Taro.getCurrentPages();
  const index = pages.findIndex(
    ({ $component: { $router: { path } = {} } = {} } = {}) => path === fixedPath,
  );
  if (index >= 0) {
    const { options, extraData } = params;
    postMessage(
      'routerParamsChange',
      {
        params: {
          ...options,
          ...extraData,
        },
      },
      index - pages.length,
      false,
    );
    return true;
  }
  return false;
}

// 数据回传，兼容组件中传递数据，通过Taro消息机制传递
export function postMessage(
  EV_TYPE = 'navigateBack',
  data,
  index = -2,
  unBack = false,
  { offKeys, ...navigatorOpts } = {},
) {
  const page = getPage(index);
  const delta = -1 - index;
  const triggerPost = () => {
    isFunction(page.onPostMessage) && page.onPostMessage(EV_TYPE, data);
    // 触发全局事件，主要用于自定义组件，页面内数据回传应使用onPostMessage 监听
    Taro.eventCenter.trigger(EV_TYPE, data);
    if (isArray(offKeys)) {
      // 解除绑定
      offKeys.filter((item) => item !== EV_TYPE).map((item) => Taro.eventCenter.off(item));
    }
  };
  if (delta > 0 && !unBack) {
    return Taro.navigateBack({
      delta,
    })
      .then(triggerPost)
      .catch((err) => {
        console.info(err);
        if (err.message) {
          Taro.kbToast({
            text: err.message,
          });
          return;
        }
        // 非接口错误，不执行以下逻辑
        if (!err.errMsg && !err.error) return;
        navigator({
          ...navigatorOpts,
          url: pagePaths.homePage,
          target: 'tab',
        });
      });
  }
  triggerPost();
}

const mapTarget = {
  blank: 'navigateTo',
  self: 'redirectTo',
  tab: 'switchTab',
  launch: 'reLaunch',
  half: 'navigateToMiniProgram',
};
export const navigator = (params) => {
  const opts = {
    ...params,
  };
  let {
    suffix = 'index',
    delta,
    key,
    url,
    appId: optAppId,
    target: optTarget = 'blank',
    options,
    post = {},
    report = {},
    onArrived = null, // 完成跳转
    force = false, // force===true不判断登录状态，强制跳转
    allowCurrent = false,
    ...restParams
  } = opts;

  if (delta) {
    // 配置返回页数，优先返回上一页，再触发跳转对应页面
    const { delta: optDelta, ...rest } = opts;
    Taro.navigateBack({
      delta: optDelta,
    })
      .then(() => navigator(rest))
      .catch(() => navigator(rest));
    return;
  }

  const { type, data = {}, index, unBack = false, offKeys } = post;
  if (!url) {
    return postMessage(type, data, index, unBack, { force, offKeys });
  }

  reportAnalytics({
    ...report,
    status: 'click',
  });

  let [target, webviewTarget] = optTarget.split('-');
  let [urlPath, appId = optAppId, urlTarget, verify] = `${url}`.split(',');
  // 兼容各种链接配置
  if (!verify && !mapTarget[urlTarget]) {
    verify = urlTarget;
  }
  if (mapTarget[appId]) {
    urlTarget = appId;
    appId = '';
  }
  if (urlTarget) {
    /**
     * 广告地址可以配置跳转方式 /pages/xx,appId,blank|self|half;
     * blank，self 仅对当前小程序页面有效；
     * half 半屏打开小程序，且目前仅微信小程序有效；
     */
    target = urlTarget;
  }

  const { path, params: urlParams } = getPathAndParams(urlPath);
  const query = {
    ...urlParams,
    ...options,
  };

  // 跳转插件
  if (check('pluginUrl', url).code === 0) {
    return Taro[mapTarget[target]]({ url });
  }

  const { extraData, envVersion = 'release' } = opts;
  const isShortLink = check('shortLink', urlPath).code === 0;
  if ((appId && urlPath) || isShortLink) {
    const miniProgramParams = {
      envVersion,
      extraData: {
        ...query,
        ...extraData,
      },
    };
    if (isShortLink) {
      miniProgramParams.shortLink = urlPath;
    } else {
      miniProgramParams.path = urlPath;
      miniProgramParams.appId = appId;
    }
    // 跳转其他小程序 envVersion: "trial" - 跳转体验版
    let openMiniProgram = Taro.navigateToMiniProgram;
    if (process.env.PLATFORM_ENV === 'weapp') {
      if (target === 'half' && wx.openEmbeddedMiniProgram) {
        // 不支持半屏或者已经被半屏打开的小程序不走半屏打开逻辑（微信暂时不支持半屏小程序再次半屏打开其他小程序）；
        // 跳转半屏
        const { query: launchQuery } = Taro.getLaunchOptionsSync() || {};
        const { _target_ } = launchQuery || {};
        if (_target_ !== target) {
          miniProgramParams.extraData._target_ = target;
          if (isShortLink) {
            miniProgramParams.verify = 'binding';
          } else if (verify) {
            miniProgramParams.verify = verify;
          }
          openMiniProgram = (opts_) =>
            new Promise((resolve, reject) => {
              wx.openEmbeddedMiniProgram({
                ...opts_,
                success: resolve,
                fail: reject,
              });
            });
        }
      }
    }
    return openMiniProgram(miniProgramParams)
      .then((res) => {
        reportAnalytics({
          ...report,
          status: 'success',
        });
        return res;
      })
      .catch((res) => {
        const { errMsg: message = '跳转失败' } = res || {};
        reportAnalytics({
          ...report,
          status: 'fail',
          message,
        });
        return res;
      });
  }

  if (target === 'webview') {
    // h5域名，跳转到webview页面
    if (process.env.PLATFORM_ENV === 'alipay') {
      if (/^alipays\:|render\.alipay/.test(urlPath)) {
        my.ap.navigateToAlipayPage({
          path: urlPath,
        });
        return;
      }
    }
    return navigator({
      force,
      target: webviewTarget,
      url: `webview?${qs.stringify({
        src: encodeURIComponent(urlPath),
        title: opts.title || (report && report.title) || '',
      })}`,
    });
  }

  // 获取登录信息，判断是否已登录
  if (path !== pagePaths.loginPage && !force && !getUserStateByComplete(false)) {
    Taro.kbUpdateLoginData({ status: 'modal' });
    return;
  }

  const fixedPath = fixSubPackagesPath(createCompletePath(path, suffix));
  if (
    checkPageIsOpened(fixedPath, target, {
      options: query,
      extraData,
    }) &&
    !allowCurrent
  ) {
    return;
  }

  // 检查是否为tab
  if (checkIsTabPage(fixedPath)) {
    target = 'tab';
  }

  const isSplitUrlAndQuery = key || target === 'tab'; // 是否将跳转路由与参数分开
  const hasQuery = !isEmpty(query); // 是否有参数
  const isSetGlobalData = isSplitUrlAndQuery && hasQuery; // 是否缓存全局数据

  const globalDataKey = key || path;
  // 将数据保存到全局
  isSetGlobalData && Taro.kbSetGlobalData(globalDataKey, query);
  url = !isSplitUrlAndQuery && hasQuery ? `${fixedPath}?${qs.stringify(query)}` : fixedPath;
  if (extraData && target !== 'tab') {
    // 非tab页面，此参数会带在链接上
    url = `${url}?${qs.stringify(extraData)}`;
  }
  // 如果type存在，即使有跳转页面，也触发post
  if (type) {
    postMessage(type, data, index, true, { force, offKeys });
  }

  // 是否匹配
  // tab页或者配置了key值，可不配置onArrived，自动设置为noop
  if (isSplitUrlAndQuery && !onArrived) {
    onArrived = noop;
  }
  const isMatchArrived = isFunction(onArrived);
  // 匹配是否为当前页
  const matchCurrentPage = (isCurrent = false) => {
    if (!isMatchArrived) return false;
    const page = getPage(-1);
    let { $router: { path: currentPath, params: routerParams } = {} } = page || {};
    const routerQuery = qs.stringify(routerParams);
    if (routerQuery && target !== 'tab') {
      // 非tab页面拼合参数
      currentPath = currentPath + '?' + routerQuery;
    }

    // 保证带参路径问题
    if (currentPath === url) {
      // 就在当前页
      // isCurrent = true表示就在当前页未跳转
      if (isSetGlobalData) {
        const globalParams = Taro.kbGetGlobalDataOnce(globalDataKey);
        const { type: routerType = 'routerParamsChange', offKeys: keys } =
          onArrived(page, isCurrent, globalParams) || {};
        // 触发监听器
        postMessage(routerType, { params: globalParams }, -1, true, { offKeys: keys });
      } else {
        onArrived(page, isCurrent);
      }
      return true;
    }
    return false;
  };

  // 触发循环检查是否到达指定页面
  const triggerMatchCurrentPageLoop = () => {
    if (!isMatchArrived) return;
    let count = 10;
    let timer;
    const matchCurrentPageLoop = () => {
      timer = setTimeout(() => {
        count--;
        if (matchCurrentPage() || count < 0) {
          clearTimeout(timer);
          return;
        }
        matchCurrentPageLoop();
      }, 100);
    };
    matchCurrentPageLoop();
  };

  // 跳转tab页面或者新页面时，如果是在当前页则不再重新进入
  // 此部分需要验证会造成什么后果；
  // 待优化
  const targets = ['blank', 'tab'];
  if (targets.includes(target) && matchCurrentPage(true)) {
    return;
  }
  return Taro[mapTarget[target]]({
    url,
    ...restParams,
  })
    .then(triggerMatchCurrentPageLoop)
    .catch((res) => {
      console.info(res);
      resolve404({
        home: false,
        notFound: true,
        query,
        path: fixedPath,
      });
    });
};

// 跳转帮助文档
export function navigateToDocument(id, arg2) {
  let { name = '', url, api, path } = utils.getIdsMap()[`${id}`] || {};
  let target = isString(arg2) ? arg2 : 'blank';
  if (api) {
    // 接口请求文档链接
    request({
      ...api,
      onThen: ({ code, data: url }) => {
        if (code == 0 && url && isString(url)) {
          navigator({
            target: `webview-${target}`,
            url,
            force: true,
          });
        } else {
          Taro.kbToast({
            text: `无法获取：${name}`,
          });
        }
      },
    });
    return;
  }
  if (!url) {
    // 未配置url的跳转到统一的帮助文档（微信小程序：快宝大本营）
    url =
      path ||
      `pages/document/document?${qs.stringify({
        platform: 'yz',
        name,
      })},wx1609eede4db3f582`;
  }
  console.log('target', target);
  navigator({
    target: `webview-${target}`,
    url,
    force: true,
  });
}

// 绑定方法
Taro.navigator = navigator;
Taro.navigateToDocument = navigateToDocument;
Taro.navigatorAndDocument = ({ id = -1, ...rest }) => {
  id > 0 ? navigateToDocument(id) : navigator(rest);
};
