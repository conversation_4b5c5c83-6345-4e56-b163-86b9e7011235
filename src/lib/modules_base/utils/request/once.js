import Taro from '@tarojs/taro';
import isEqual from 'lodash/isEqual';
import isNull from 'lodash/isNull';
import CallbacksPool from '~base/utils/callbacksPool';
import request from '.';
import { getPage, noop } from '../utils';

// 页面卸载，重置当前页面上的缓存
function pageUnload(res) {
  const responseCacheMap = RequestOnceManager.getResponseCache();
  const urls = Object.keys(responseCacheMap).filter((url) => {
    const catchGroup = responseCacheMap[url];
    if (catchGroup && catchGroup.some((item) => item.path === `/${res.path}`)) {
      return true;
    }
    return false;
  });
  urls.forEach((url) => {
    RequestOnceManager.reset(url);
  });
}

/**
 *
 * @description 提交数据
 * @param {{
 *    url:string;
 *    data?:any;
 *    toastLoading?:boolean;
 *    toastError?:boolean;
 *    toastSuccess?:boolean;
 * }} opts
 * @returns
 */
export function requestPost(opts) {
  return new Promise((resolve) => {
    const { onThen = noop } = opts;
    request({
      toastError: true,
      toastSuccess: true,
      quickTriggerThen: true,
      ...opts,
      onStop: (key, res = { code: 9001, msg: key }) => resolve(res),
      onThen: (res, req) => {
        onThen(res, req);
        resolve(res);
      },
    });
  });
}

/**
 *
 * @description 拉取数据，默认不展示toast信息
 * @param {{
 *    url:string;
 *    data?:any;
 *    toastLoading?:boolean;
 *    toastError?:boolean;
 *    toastSuccess?:boolean;
 * }} opts
 * @returns
 */
export function requestGet(opts) {
  return requestPost({
    toastLoading: false,
    toastError: false,
    toastSuccess: false,
    ...opts,
  });
}

const ResponseCallbacksManager = new CallbacksPool();

class RequestOnce {
  constructor() {
    this.responseCacheMap = {};
    this.responseLockMap = {};
  }

  // 锁定请求
  lockRequest(url, req) {
    this.responseLockMap[url] = req;
  }

  // 解锁请求
  unlockRequest(url) {
    this.responseLockMap[url] = null;
  }

  // 仅当参数完全相等时锁定生效
  checkIsLocked(url, req) {
    const reqCache = this.responseLockMap[url];
    return reqCache && isEqual(req, reqCache);
  }

  // 缓存响应结果
  setResponseCache(url, res, req) {
    if (isNull(res)) {
      this.responseCacheMap[url] = null;
      return;
    }
    const { $router: { path } = {} } = getPage();
    const resGroups = this.responseCacheMap[url] || [];
    const index = resGroups.findIndex((item) => isEqual(item.req, req));
    const catchItem = { res, req, path };
    if (index >= 0) {
      resGroups[index] = catchItem;
    } else {
      resGroups.push(catchItem);
    }
    this.responseCacheMap[url] = resGroups;
  }

  // 获取响应缓存
  getResponseCache(url, req) {
    if (!url) return this.responseCacheMap;
    const resGroups = this.responseCacheMap[url] || [];
    const index = resGroups.findIndex((item) => isEqual(item.req, req));
    if (index >= 0) {
      return resGroups[index].res;
    }
    return null;
  }

  // 触发回调
  onThen({ url, req, res, onThen, resolve }) {
    // 记录缓存
    if (`${res.code}` === '0') {
      this.setResponseCache(url, res, req);
    } else {
      this.unlockRequest(url);
    }
    ResponseCallbacksManager.trigger({ res, req });

    onThen(res, req);
    resolve(res);
  }

  // 请求拦截
  onIntercept({ url, req, force, onThen, onIntercept, resolve }) {
    const isLocked = this.checkIsLocked(url, req);
    const cacheRes = this.getResponseCache(url, req);

    const triggerResolve = (res, req) => {
      resolve(res);
      onThen(res, req);
    };

    if (cacheRes && !force) {
      triggerResolve(cacheRes, req);
      Taro.offBeforePageUnload(pageUnload);
      Taro.onBeforePageUnload(pageUnload);
      return true;
    }

    if (isLocked) {
      ResponseCallbacksManager.push(({ res, req }) => triggerResolve(res, req));
      return true;
    }

    // 锁定请求
    this.lockRequest(url, req);

    return onIntercept(req, onThen);
  }

  // 重置状态
  reset(url) {
    this.unlockRequest(url);
    this.setResponseCache(url, null);
  }
}

export const RequestOnceManager = new RequestOnce();

// 声明周期内，仅请求一次
export function requestGetOnce(opts, force) {
  return new Promise((resolve) => {
    const { url, data = {}, onIntercept = noop, onThen = noop, ...restOpts } = opts;
    requestGet({
      ...restOpts,
      url,
      data,
      onIntercept: (req) =>
        RequestOnceManager.onIntercept({ url, req, force, onThen, onIntercept, resolve }),
      onThen: (res, req) => RequestOnceManager.onThen({ url, req, res, onThen, resolve }),
    });
  });
}
