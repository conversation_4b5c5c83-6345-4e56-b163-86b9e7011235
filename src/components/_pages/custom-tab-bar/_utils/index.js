/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidShowCom } from '@base/hooks/page';
import { checkIsTabPage } from '@base/utils/navigator';
import { getPage, getStorage, setStorage } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import Taro, { useEffect, useMemo, useState } from '@tarojs/taro';
import { check } from '@base/utils/rules';
import { getAdConfig } from '~/components/ad-extension/_utils';
import { filterTabs } from './patch';

export function getTabBarConfig() {
  const { config: { tabBar = {} } = {} } = Taro.getApp();
  return tabBar;
}

const isCustomRemoteTabs = false;

let remoteTabs = [];
export const getRemoteTabs = () => {
  return new Promise((resolve) => {
    if (isCustomRemoteTabs) {
      if (remoteTabs && remoteTabs.length > 0) {
        resolve(remoteTabs);
        return;
      }
      getAdConfig(
        {
          fourAd: 1,
        },
        true,
      ).then((data) => {
        let list = [];
        if (data && data.length > 0) {
          list = data.map((item) => {
            const { adUrl, imgUrl, imgUrl1, title } = item || {};
            return {
              pagePath: adUrl,
              iconPath: imgUrl,
              selectedIconPath: imgUrl1,
              text: title,
            };
          });
          remoteTabs = list;
        }
        resolve(list);
      });
    } else {
      resolve([]);
    }
  });
};

let remoteTabsList = [];
export function useGetTabBarConfig() {
  const { list = [], selectedColor, color } = getTabBarConfig();
  const [customList, setCustomList] = useState([]);
  const storageTabsKey = 'storageTabsKey';

  const getStorageAsync = async () => {
    try {
      const res = await getStorage({ key: storageTabsKey });
      return res.data || {};
    } catch (error) {
      return {};
    }
  };

  useEffect(() => {
    if (remoteTabsList && remoteTabsList.length > 0) {
      setCustomList([...remoteTabsList]);
      return;
    }
    if (isCustomRemoteTabs) {
      getStorageAsync().then((res) => {
        const { data = [] } = res || {};
        if (data && data.length > 0) {
          remoteTabsList = data;
          setCustomList([...data]);
        }
        getRemoteTabs().then((res) => {
          if (res && res.length > 0) {
            // eslint-disable-next-line no-param-reassign
            res = res.map((item, index) => {
              if (!item || !item.pagePath) {
                return list[index];
              }
              return item;
            });
            remoteTabsList = res;
            setCustomList([...res]);
            setStorage({
              key: storageTabsKey,
              data: res,
            });
          }
        });
      });
    }
  }, [list]);

  const checkIsHttp = (str) => {
    return check('httpUrl', str).code == 0;
  };

  const { selectedTabIndex } = useSelector((state) => state.global);

  const tabs = useMemo(() => {
    let _list =
      customList && customList.length > 0
        ? customList
        : remoteTabsList && remoteTabsList.length > 0
        ? remoteTabsList
        : list;
    _list = _list.map(({ showText = true, ...item } = {}) => ({
      ...item,
      showText,
      pagePath: `/${item.pagePath}`,
      selectedIconPath: checkIsHttp(item.selectedIconPath)
        ? item.selectedIconPath
        : `/${item.selectedIconPath}`,
      iconPath: checkIsHttp(item.iconPath) ? item.iconPath : `/${item.iconPath}`,
      className: `kb-custom-tab__item kb-custom-tab__item--${item.className} tab-size-${
        item.text && showText ? 'normal' : 'full'
      }`,
    }));
    return filterTabs(_list);
  }, [list, customList, selectedTabIndex]);

  return [tabs, selectedColor, color];
}

/**
 *
 * @description 兼容自定义tab跳转后返回无法恢复的问题
 * @param {*} props
 * @returns
 */
export function useTabBarShow(props) {
  const { showTabBar } = useSelector((state) => state.global);
  const [show, setShow] = useState(false);

  function checkIsShow() {
    const {
      $router: { path },
    } = getPage();
    const { custom } = getTabBarConfig();
    const { isTab = checkIsTabPage(path) } = props;
    return custom && isTab && showTabBar;
  }

  useEffect(() => {
    setShow(checkIsShow());
  }, [showTabBar]);

  useDidShowCom(() => {
    setShow(checkIsShow());
  });

  return show;
}
