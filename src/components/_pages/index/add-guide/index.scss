/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$z-index-guide: 9000;

.kb-added-guide {
  // position: fixed;
  // top: 0;
  // right: 0;
  // bottom: 0;
  // left: 0;
  // z-index: $z-index-guide + 1;
  // color: #fff;

  &__wrap {
    position: fixed;
    top: 170px;
    right: $spacing-h-md;
    z-index: $z-index-guide + 1;
    width: auto;
    height: 68px;
    padding: 0 $spacing-h-md;
    color: #fff;
    font-size: $font-size-base;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 34px;
    &_step2 {
      position: fixed;
      top: 160px;
      right: 20px;
      height: fit-content;
      padding: 0;
      background-color: transparent;
      &__img {
        width: 640px;
      }
      &__btn {
        position: absolute;
        bottom: 50px;
        left: 50%;
        box-sizing: border-box;
        width: 428px;
        height: 80px;
        padding: $spacing-h-xs $spacing-h-md;
        color: #3377ff;
        font-size: $font-size-sm;
        font-size: $font-size-x;
        background-color: $color-white;
        border: 1px solid #3377ff;
        border-radius: 40px;
        transform: translateX(-50%);
      }
    }
  }

  &__mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-guide;
    transform: scale3d(1, 1, 0);
    opacity: 0;
    // transition: all 0.3s ease-in-out;
    pointer-events: none;
    &--black {
      background-color: rgba(0, 0, 0, 0.7);
      transform: scale3d(1, 1, 1);
      opacity: 1;
    }
  }
  &__btn {
    padding: $spacing-h-xs $spacing-h-md;
    color: #3377ff;
    font-size: $font-size-sm;
    background-color: $color-white;
    border-radius: 20px;
  }
  .triangle {
    &::after {
      position: absolute;
      top: -33px;
      right: 106px;
      border-top: 20px solid transparent;
      border-right: 20px solid transparent;
      border-bottom: 15px solid rgba(0, 0, 0, 0.7);
      border-left: 20px solid transparent;
      content: '';
    }
  }
}
