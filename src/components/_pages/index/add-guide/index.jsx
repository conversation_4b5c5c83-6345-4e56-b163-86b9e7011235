/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View, Fragment, Image } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import './index.scss';

const AddGuide = () => {
  const [show, setShow] = useState(false);
  const [step, setStep] = useState(1);

  useEffect(() => {
    if (wx.checkIsAddedToMyMiniProgram) {
      wx.checkIsAddedToMyMiniProgram({
        success: (res) => {
          setShow(!res.added);
        },
        fail: () => {
          setShow(true);
        },
      });
    } else {
      setShow(true);
    }
  }, []);

  return (
    show && (
      <Fragment>
        <View className='kb-added-guide'>
          {step == 1 ? (
            <View className='kb-added-guide__wrap at-row at-row__align--center'>
              <AtIcon
                prefixClass='kb-icon'
                value='close2'
                className='kb-size__base'
                onClick={() => setShow(false)}
              />
              <Text className='kb-margin-md-lr'>点添加我的小程序，使用微快递特惠寄更便利</Text>
              <View
                className='kb-added-guide__btn'
                hoverClass='kb-hover-opacity'
                onClick={() => setStep(2)}
              >
                添加
              </View>
              <View className='triangle' />
            </View>
          ) : step == 2 ? (
            <View className='kb-added-guide__wrap kb-added-guide__wrap_step2'>
              <Image
                src='https://cdn-img.kuaidihelp.com/qj/miniapp/activity/recruit/guide.png?v=2'
                mode='widthFix'
                className='kb-added-guide__wrap_step2__img'
              />
              <View
                className='kb-added-guide__wrap_step2__btn at-row at-row__align--center at-row__justify--center'
                hoverClass='kb-hover-opacity'
                onClick={() => setShow(false)}
              >
                我知道了
              </View>
            </View>
          ) : null}
        </View>
        {/* <View
          className={classNames({
            'kb-added-guide__mask': true,
            'kb-added-guide__mask--black': step == 2,
          })}
        /> */}
      </Fragment>
    )
  );
};

AddGuide.options = {
  addGlobalClass: true,
};

export default AddGuide;
