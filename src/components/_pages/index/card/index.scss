/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-home-card {
  display: flex;
  justify-content: space-around;
  padding: 0 32px 20px;
  background: linear-gradient(180deg, #518afd 56%, #f7f8fa 98%);
  &--one {
    .kb-home-card__block {
      height: 260px;
    }
    .kb-home-card__box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &--edit {
        .title {
          margin-top: 0;
        }
        .img-box {
          .img {
            display: block;
            width: 381px;
            height: 260px;
          }
        }
      }
    }
  }
  &__block {
    display: flex;
    flex: 1;
    flex-direction: column;
    box-sizing: border-box;
    height: 376px;
    margin-right: 20px;
    &:last-child {
      margin-right: 0;
    }
  }
  &__box {
    position: relative;
    flex: 1;
    flex-shrink: 0;
    box-sizing: border-box;
    margin-bottom: 18px !important;
    overflow: hidden;
    background: linear-gradient(180deg, #c5dbff 0%, #ffffff 30%);
    border-radius: 16px;
    &:last-child {
      margin-bottom: 0px !important;
    }
    &--edit {
      .title {
        margin-top: 34px;
        margin-left: 28px;
        .sub-title {
          color: #323233;
          font-weight: 500;
          font-size: 32px;
        }
        .desc {
          margin-top: 10px;
          color: #9ca1b6;
          font-size: 24px;
        }
      }
      .img-box {
        position: absolute;
        right: 0;
        bottom: 0;
      }
      .img {
        width: 290px;
        height: 198px;
      }
    }
    &--share {
      margin: 0;
      padding: 0;
      line-height: normal;
      text-align: left;
      border: none;
      &::after {
        border: none;
      }
      .title {
        margin-top: 34px;
        margin-left: 28px;
        color: #323233;
        font-weight: 500;
        font-size: 32px;
      }
      .img-box {
        position: absolute;
        right: 0;
        bottom: 0;
      }
      .img {
        width: 215px;
        height: 158px;
      }
    }
    &--recommend {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      padding: 0 24px 0 34px;
      .title {
        color: #323233;
        font-weight: 500;
        font-size: 32px;
      }
      .img {
        width: 80px;
        height: 80px;
      }
      .tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 5px 10px;
        color: #ffffff;
        font-size: 20px;
        background: #e34d59;
        border-radius: 0 0 16px 0;
        &::after {
          position: absolute;
          top: 0;
          right: -20px;
          width: 20px;
          height: 20px;
          background-image: radial-gradient(circle at 20px 20px, transparent 20px, #e34d59 20px);
          content: '';
        }
      }
    }
  }
}
