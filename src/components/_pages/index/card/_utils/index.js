/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro, { useState } from '@tarojs/taro';
import { getCurrentUser } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import { getOrderBrandList } from '@/components/_pages/order/extra-info/_utils';
import { registerLock } from '@/components/_pages/team/_utils';

const getRecommendBrand = () => {
  const recommendBrand = 'sto';
  return new Promise((resolve) => {
    getOrderBrandList().then((list) => {
      const item = list.find((i) => i.brand === recommendBrand && i.disable != 1);
      resolve(item);
    });
  });
};

// eslint-disable-next-line import/prefer-default-export
export const useHomeCard = () => {
  const [recommend, setRecommend] = useState(false);
  const isCustom = getCurrentUser('custom');
  const isAdmin = getCurrentUser('regiment');

  useUpdate(({ logined }) => {
    if (!logined) return;
    getRecommendBrand().then((res) => {
      setRecommend(!!res);
    });
  });

  const handleClick = (url) => {
    if (registerLock.check(true)) return;
    Taro.navigator({
      url,
    });
  };

  const isShowShare = !getCurrentUser('yiwu') && (isAdmin || isCustom);

  return {
    isShowShare,
    isCustom,
    recommend,
    handleClick,
  };
};
