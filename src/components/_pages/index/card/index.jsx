/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Button, Image, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import { useHomeCard } from './_utils';
import './index.scss';

const KbHomeCard = () => {
  const { isShowShare, isCustom, recommend, handleClick } = useHomeCard();
  const onlyHasEdit = !isShowShare && !recommend;

  const rootCls = classNames('kb-home-card', {
    'kb-home-card--one': onlyHasEdit,
  });
  return (
    <View className={rootCls}>
      <View className='kb-home-card__block'>
        <View
          className='kb-home-card__box kb-home-card__box--edit'
          onClick={handleClick.bind(this, 'order/edit')}
          hoverClass='kb-hover-opacity'
        >
          <View className='title'>
            <View className='sub-title'>立即寄件</View>
            <View className='desc'>上门取件 优惠寄</View>
          </View>
          <View className='img-box'>
            <Image
              className='img'
              src='https://cdn-img.kuaidihelp.com/qj/miniapp/home2.png?v=1'
              mode='widthFix'
            />
          </View>
        </View>
      </View>
      {!onlyHasEdit && (
        <View className='kb-home-card__block'>
          {isShowShare && (
            <Button className='kb-home-card__box kb-home-card__box--share' openType='share'>
              <View className='title'>{isCustom ? '邀请好友' : '邀请团员'}</View>
              <View className='img-box'>
                <Image
                  className='img'
                  src='https://cdn-img.kuaidihelp.com/qj/miniapp/home1.png?v=1'
                  mode='widthFix'
                />
              </View>
            </Button>
          )}
          {recommend && (
            <View
              className='kb-home-card__box kb-home-card__box--recommend'
              onClick={handleClick.bind(this, 'order/edit?brand=sto')}
              hoverClass='kb-hover-opacity'
            >
              <View className='tag'>
                <AtIcon
                  className='kb-icon2'
                  prefixClass='kb-icon'
                  value='recommend'
                  color='#fff'
                  size='12'
                />
                <Text className='kb-margin-xs-l'>快宝推荐</Text>
              </View>
              <View className='title'>申通快递</View>
              <View className='img-box'>
                <Image
                  className='img'
                  src='https://cdn-img.kuaidihelp.com/qj/miniapp/jjhy/sto.png?v=1'
                  mode='widthFix'
                />
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

KbHomeCard.options = {
  addGlobalClass: true,
};

export default KbHomeCard;
