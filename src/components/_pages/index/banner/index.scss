

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-ads {
  height: 184px;
  overflow: hidden;
  background-color: $color-white;

  &__swiper {
    width: 100%;
    height: 184px;

    &--item {
      height: 100%;
      background: linear-gradient(270deg, #2b69e5 0%, #2b69e5 0%, #4f8fee 99%, #4f8fee 100%);
      border-radius: $border-radius-xxl;
    }
    &--image {
      width: 100%;
      height: 100%;
    }
  }
}

.kb-brand {
  position: relative;
  &::after {
    position: absolute;
    top: -25px;
    right: -74px;
    padding: 4px $spacing-h-sm;
    color: $color-white;
    font-size: $font-size-sm;
    background-color: $color-red;
    border-top-left-radius: $border-radius-xl;
    border-bottom-right-radius: $border-radius-xl;
    content: 'HOT';
  }
}
