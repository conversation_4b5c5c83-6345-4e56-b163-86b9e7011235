/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Swiper, SwiperItem, Image } from '@tarojs/components';
import { AtAvatar } from 'taro-ui';
import { openCustomerService } from '@base/components/login/_utils/index';
import KbLoader from '@base/components/loader';
import './index.scss';

const ads = [
  {
    id: 1,
    imgUrl: 'https://cdn-img.kuaidihelp.com/qj/miniapp/banner1.jpg?v=1',
  },
  {
    id: 2,
    imgUrl: 'https://cdn-img.kuaidihelp.com/qj/miniapp/banner2.jpg?v=1',
  },
];

const Index = ({ admin }) => {
  // 打开客服
  const handleClick = (id) => {
    if (id) {
      id == 2 && openCustomerService();
      return;
    }
    Taro.navigator({
      url: 'arrivePay',
    });
  };

  return (
    <View className='kb-ads'>
      {admin ? (
        <Swiper
          className='kb-ads__swiper'
          autoplay
          disableTouch
          circular
          indicatorDots={admin != 1}
          indicatorColor='rgba(255,255,255,0.5)'
          indicatorActiveColor='#CCCCCC'
        >
          {admin == 1 ? (
            <SwiperItem>
              <View
                className='kb-ads__swiper--item'
                hoverClass='kb-hover-opacity'
                onClick={handleClick.bind(null, '')}
              >
                <View className='at-row'>
                  <View className='kb-spacing-xl at-row at-row__justify--between at-row__align--center'>
                    <View>
                      <View className='at-row at-row__align--center kb-margin-sm-b'>
                        <AtAvatar
                          className='kb-avatar__middle'
                          circle
                          size='normal'
                          image='https://cdn-img.kuaidihelp.com/brand_logo/icon_sf.png?t=20230316'
                        />
                        <View className='kb-color__white kb-size__17 kb-margin-md-l kb-brand'>
                          顺丰快递
                        </View>
                      </View>
                      <View className='kb-color__white kb-size__sm'>
                        百万返佣活动进行中，寄顺丰，享返佣
                      </View>
                    </View>
                    <View className='kb-color__grey-3 kb-size__sm'>了解详情 {'>'}</View>
                  </View>
                </View>
              </View>
            </SwiperItem>
          ) : (
            <Fragment>
              {ads.map((item) => {
                return (
                  <SwiperItem key={item.id}>
                    <View
                      className='kb-ads__swiper--item'
                      hoverClass='kb-hover-opacity'
                      onClick={handleClick.bind(null, item.id)}
                    >
                      <Image className='kb-ads__swiper--image' src={item.imgUrl} />
                    </View>
                  </SwiperItem>
                );
              })}
            </Fragment>
          )}
        </Swiper>
      ) : (
        <KbLoader centered loadingText={false} size='small' />
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
