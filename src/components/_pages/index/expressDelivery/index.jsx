

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import './index.scss';

/**
 * 首页寄快递组件
 *  */
const Index = () => {
  const onJump = () => {
    Taro.navigator({
      url: 'pages/order/edit/index',
    });
  };

  return (
    <View className='kb-card kb-spacing-md' hoverClass='kb-hover-opacity' onClick={onJump}>
      <View className='kb-size__17 kb-color__white'>立即寄件</View>
      <View className='kb-size__base kb-color__white'>全网比价 实惠寄件</View>
      <View className='kb-size__base kb-color__white'>上门取件 品质保障</View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
