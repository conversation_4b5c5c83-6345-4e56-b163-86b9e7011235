

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(180deg, #0075ff 0%, #0075ff 0%, #0055ff 100%, #0055ff 100%);
  border-radius: $border-radius-xxl;
  opacity: 0.95;
  backdrop-filter: blur(10px);
  &__brand {
    padding: 0 4px 0 4px;
    color: $color-white;
    border: $border-lighter;
    border-radius: $border-radius-md;
  }
}
