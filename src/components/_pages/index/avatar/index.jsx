/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { View, Image, Button } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { defaultAvatarUrl, getCurrentUser } from '@base/utils/utils';
import { updateUserInfo, uploadImage } from '@/utils/qy';
import { getUserTittleAndTagImg } from '../_utils';
import './index.scss';

const KbPageAvatar = (props) => {
  const { action = '', src, level } = props;
  const [tagImg, setTagImg] = useState('');
  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo, logined } = loginData;
  const { avatar_url } = userInfo || {};
  const isMember = level ? level == 0 : getCurrentUser('member');
  const isCustom = getCurrentUser('custom');

  const onBindChooseAvatar = (res) => {
    const { avatarUrl } = res.detail;
    uploadImage({ filePath: avatarUrl }).then((res) => {
      if (res.code == 0) {
        const avatar_url = `https://upload.kuaidihelp.com${res.data.url}`;
        updateUserInfo({ avatar_url }).then(() => {
          Taro.kbUpdateUserInfo({
            avatar_url,
          });
        });
      } else {
        Taro.kbToast({
          text: res.msg,
        });
      }
    });
  };

  const url = src || avatar_url || defaultAvatarUrl;

  const getTag = async (l) => {
    const { img: tagImg } = (await getUserTittleAndTagImg({ level: l })) || {};
    setTagImg(tagImg);
  };

  useEffect(() => {
    if (logined) {
      getTag(level);
    }
  }, [logined, level]);

  return (
    <View className='kb-page-avatar' hoverClass={action ? 'kb-hover-opacity' : ''}>
      {action == 'chooseAvatar' && (
        <Button
          className='kb-page-avatar__btn'
          openType='chooseAvatar'
          onChooseAvatar={onBindChooseAvatar}
        />
      )}
      <Image className='kb-page-avatar__img' src={url} />
      {tagImg && (
        <Image
          className={`kb-page-avatar__tag kb-page-avatar__tag--${
            !isCustom || isMember ? 'sm' : 'md'
          }`}
          src={tagImg}
          mode='widthFix'
        />
      )}
    </View>
  );
};

KbPageAvatar.options = {
  addGlobalClass: true,
};

export default KbPageAvatar;
