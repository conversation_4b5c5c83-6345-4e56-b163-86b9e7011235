/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-page-avatar {
  position: relative;
  box-sizing: border-box;
  &__btn {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    opacity: 0;
  }
  &__img {
    display: block;
    width: 96px;
    height: 96px;
    overflow: hidden;
    border-radius: $border-radius-circle;
  }
  &__tag {
    position: absolute;
    right: 0;
    bottom: 0;
    &--sm {
      width: 28px;
      height: 28px;
    }
    &--md {
      width: 43px;
      height: 28px;
    }
  }
}
