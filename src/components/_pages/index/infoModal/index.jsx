/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbErrorModal from '@/components/errorModal';
import { getStorageSync, setStorage } from '@base/utils/utils';
import dayjs from 'dayjs';
import './index.scss';

const sKey = 'infoModal';

const Index = () => {
  const [isOpened, setIsOpened] = useState(false);

  const handleClose = () => {
    setIsOpened(false);
  };

  useEffect(() => {
    const currentDay = dayjs().format('YYYY-MM-DD');
    const { data } = getStorageSync(sKey) || {};

    if (data != currentDay) {
      setTimeout(() => {
        setIsOpened(true);
        setStorage({
          key: sKey,
          data: currentDay,
        });
      }, 500);
    }
  }, []);

  return (
    <KbErrorModal
      className='kb-error-modal'
      isOpened={isOpened}
      title={false}
      onClose={handleClose}
      buttons={[]}
      renderFooter={
        <View
          onClick={handleClose}
          className='kb-color__grey-3 kb-size__lg kb-text__center kb-border-t kb-spacing-md-tb'
          hoverClass='kb-hover'
        >
          我已知晓
        </View>
      }
    >
      <View className='kb-size__17 kb-text__center kb-spacing-lg-tb'>告微快递特惠寄用户书</View>
      <View className='content'>
        <View>
          因部分微快递特惠寄团长出现恶意利用折扣引导，微快递特惠寄平台内部需要集中清理这些恶意引导用户，因此暂停京东现付渠道，请大家选择其他寄件方式，请给位用户知悉！
        </View>
        <View className='kb-text__right'>微快递特惠寄官方</View>
        <View className='kb-text__right'>2022年9月5日</View>
      </View>
    </KbErrorModal>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
