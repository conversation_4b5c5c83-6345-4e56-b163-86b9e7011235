/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '~base/utils/request';

/**
 * 获取地推空码信息
 * @returns
 */
export const getDituiCodeInfo = (opt) => {
  const { scene } = opt;
  return new Promise((resolve) => {
    request({
      url: '/api/BindWxQrcode/getWxQrcode',
      toastLoading: false,
      toastError: true,
      data: {
        scene,
      },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};

/**
 * 获取地推空码信息
 * @returns
 */
export const bindDituiCode = (opt) => {
  const { scene, phone } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/api/BindWxQrcode/bindWxQrcode',
      data: {
        scene,
        phone,
      },
      toastError: true,
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};
