/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtInput } from 'taro-ui';
import { useUpdate } from '~base/hooks/page';
import KbErrorModal from '@/components/errorModal';
import { getEditPageLunchParams } from '../../order/_utils/order.edit';
import { bindDituiCode, getDituiCodeInfo } from './_utils';
import './index.scss';

const KbDituiCode = (props) => {
  const { data } = props;
  const [isOpened, setIsOpened] = useState(false);
  const [isOpenedSuccess, setIsOpenedSuccess] = useState(false);
  const [isOpenedError, setIsOpenedError] = useState(false);
  const [error, setError] = useState('');
  const [codeInfo, setCodeInfo] = useState();
  const [value, setValue] = useState();

  useUpdate(
    async (loginData) => {
      const { logined, userInfo } = loginData || {};
      if (!logined) return;
      const { user_id } = userInfo || {};
      const lunchRes = data || (await getEditPageLunchParams(this));
      const { dt } = lunchRes || {};
      console.log('KbDituiCode===>res', lunchRes);
      if (dt) {
        getDituiCodeInfo({ scene: dt }).then((res) => {
          console.log('getDituiCodeInfo===>res', res);
          if (res.code == 0 && res.data) {
            const { bindId, user_type, invite_type, bind_type } = res.data || {};
            res.data.dt = dt;
            setCodeInfo(res.data);
            if (bind_type === 'user') return;
            if (bindId) {
              //已激活
              Taro.navigator({
                url: 'order/edit',
                options: {
                  bindId,
                  user_type,
                  invite_type,
                },
              });
            } else {
              //未激活
              setValue(user_id);
              setIsOpened(true);
            }
          }
        });
      }
    },
    [data],
  );

  const handleClose = () => {
    setIsOpened(false);
  };

  const handleSuccessClose = () => {
    setIsOpenedSuccess(false);
  };

  const handleErrorClose = () => {
    setIsOpenedError(false);
  };

  const handleTry = () => {
    handleErrorClose();
    setIsOpened(true);
  };

  const handleConfirm = () => {
    if (!value) {
      Taro.kbToast({
        text: '请输入被激活用户ID/手机号',
      });
      return;
    }
    bindDituiCode({
      scene: codeInfo.dt,
      phone: value,
    }).then((res) => {
      if (res.code == 0) {
        setValue('');
        handleClose();
        setIsOpenedSuccess(true);
      } else {
        setError(res.msg);
        setIsOpenedError(true);
      }
    });
  };

  return (
    <View>
      <KbErrorModal
        onClose={handleClose}
        title={false}
        isOpened={isOpened}
        buttons={[]}
        closeOnClickOverlay={false}
        renderFooter={
          <View className='kb-dituiModal-footer'>
            <View
              className='kb-dituiModal-footer__btn kb-dituiModal-footer__btn--cancel'
              hoverClass='kb-hover'
              onClick={handleClose}
            >
              取消
            </View>
            <View
              className='kb-dituiModal-footer__btn kb-dituiModal-footer__btn--confirm'
              hoverClass='kb-hover'
              onClick={handleConfirm}
            >
              立即激活
            </View>
          </View>
        }
      >
        {isOpened ? (
          <View className='kb-dituiModal-container'>
            <View>
              <View className='kb-dituiModal-title'>激活线下太阳码</View>
              <View className='kb-dituiModal-input'>
                <AtInput
                  value={value}
                  placeholder='请输入被激活用户ID/手机号'
                  onChange={setValue}
                />
              </View>
              <View className='kb-dituiModal-list'>
                <View className='kb-dituiModal-list__item'>激活方式：</View>
                <View className='kb-dituiModal-list__item'>
                  1. 输入特惠寄小程序用户ID，可激活小程序太阳码；{' '}
                </View>
                <View className='kb-dituiModal-list__item'>
                  2. 输入特惠寄小程序注册手机号，可激活小程序太阳码；
                </View>
              </View>
            </View>
          </View>
        ) : null}
      </KbErrorModal>
      <KbErrorModal
        onClose={handleSuccessClose}
        title={false}
        isOpened={isOpenedSuccess}
        buttons={[]}
        renderFooter={
          <View className='kb-dituiModal-footer'>
            <View
              className='kb-dituiModal-footer__btn kb-dituiModal-footer__btn--cancel'
              hoverClass='kb-hover'
              onClick={handleSuccessClose}
            >
              我已知晓
            </View>
          </View>
        }
      >
        <View className='kb-dituiModal-container'>
          <View className='kb-dituiModal-success'>
            <View className='kb-icon kb-icon-chenggong' />
            <View className='kb-dituiModal-title'>激活绑定成功</View>
            <View className='kb-dituiModal-desc'>恭喜您，此码已经绑定成为您的专属推广二维码！</View>
          </View>
        </View>
      </KbErrorModal>
      <KbErrorModal
        onClose={handleErrorClose}
        title={false}
        isOpened={isOpenedError}
        buttons={[]}
        renderFooter={
          <View className='kb-dituiModal-footer'>
            <View
              className='kb-dituiModal-footer__btn kb-dituiModal-footer__btn--cancel'
              hoverClass='kb-hover'
              onClick={handleErrorClose}
            >
              取消
            </View>
            <View
              className='kb-dituiModal-footer__btn kb-dituiModal-footer__btn--confirm'
              hoverClass='kb-hover'
              onClick={handleTry}
            >
              再试一次
            </View>
          </View>
        }
      >
        <View className='kb-dituiModal-container'>
          <View className='kb-dituiModal-error'>
            <View className='kb-icon kb-icon-close' />
            <View className='kb-dituiModal-title'>激活绑定失败</View>
            <View className='kb-dituiModal-desc'>失败原因：{error}</View>
          </View>
        </View>
      </KbErrorModal>
    </View>
  );
};

KbDituiCode.options = {
  addGlobalClass: true,
};
export default KbDituiCode;
