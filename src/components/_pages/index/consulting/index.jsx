/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import classNames from 'classnames';
import { openCustomerService } from '~base/components/login/_utils';
import './index.scss';

const Consulting = ({ scrolling }) => {
  return (
    <View
      className={classNames('kb-consulting', {
        'kb-consulting--scrolling': scrolling,
      })}
      hoverClass='kb-hover-opacity'
      onClick={openCustomerService}
    >
      <Image
        className='kb-consulting__icon'
        src='https://cdn-img.kuaidihelp.com/qj/miniapp/cps/consulting.png'
      />
    </View>
  );
};

Consulting.options = {
  addGlobalClass: true,
};

export default Consulting;
