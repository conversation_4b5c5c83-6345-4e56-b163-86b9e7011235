/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useState } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { useUpdate } from '~base/hooks/page';
import { isFitFXLXActivity } from './_utils';
import './index.scss';

// 全寄-全民分销版-首单寄件,福利补贴活动(https://tower.im/teams/258300/todos/108987/#subtodo-108152686)
const KbFXLXActivity = () => {
  const [openAddReward, setOpenAddReward] = useState(false);

  const handleCurtainClose = () => {
    setOpenAddReward(false);
  };

  useUpdate((loginRes) => {
    const { logined } = loginRes || {};
    if (!logined) return;
    isFitFXLXActivity().then((res) => {
      setOpenAddReward(res);
    });
  }, []);

  return (
    <Fragment>
      <AtCurtain isOpened={openAddReward} onClose={handleCurtainClose}>
        <View className='KbFXLXActivity-reward' onClick={handleCurtainClose}>
          <Image
            onClick={handleCurtainClose}
            className='KbFXLXActivity-reward--img'
            src='https://cdn-img.kuaidihelp.com/qj/miniapp/invite/hb.png'
          />
        </View>
      </AtCurtain>
    </Fragment>
  );
};

KbFXLXActivity.defaultProps = {};

KbFXLXActivity.options = {
  addGlobalClass: true,
};

export default KbFXLXActivity;
