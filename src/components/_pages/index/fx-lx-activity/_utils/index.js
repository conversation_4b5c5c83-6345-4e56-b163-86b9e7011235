/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { checkIsParticipateActivity } from '~/components/_pages/activity/_utils';
import dayjs from 'dayjs';

/**
 * 是否分销版红包活动期间
 * @returns
 */
export const checkIsFXLXActivityTime = () => {
  return new Promise((resolve) => {
    const now = new Date();
    const start = '2025-01-15 11:00:00';
    const end = '2025-02-28 23:59:59';
    if (dayjs(start).isBefore(now) && dayjs(end).isAfter(now)) {
      resolve(true);
    }
    resolve(false);
  });
};

/**
 * 是否发放分销版红包活动
 * @returns
 */
export const isFitFXLXActivity = async () => {
  return new Promise((resolve) => {
    checkIsParticipateActivity({ activity: 'first_order_subsidy' }).then((res) => {
      resolve(!!res);
    });
  });
};
