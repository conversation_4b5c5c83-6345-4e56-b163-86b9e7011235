/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import { noop } from '@base/utils/utils';
import KbCurtainModal from '@/components/_pages/order/curtain-modal';
import './index.scss';

const headConfig = {
  headColor: 'blur-linear',
  title: '钱包余额不足',
  desc: '余额不足会影响您的寄件需求，请尽快充值!',
  icon: 'gold',
};

const Index = (props) => {
  const { onConfirm, onClose, billInfo, isOpened } = props;
  const handleClose = (e) => {
    onClose(e);
    onConfirm(e);
  };
  return (
    <KbCurtainModal
      isOpened={isOpened}
      onClose={handleClose}
      onConfirm={onConfirm}
      confirmText='立即充值'
      headColor='blur-linear'
      head={headConfig}
    >
      <View className='kb-admin-modal kb-margin-md kb-spacing-md kb-background__grey'>
        <View className='at-row at-row__justify--around'>
          <View className='kb-text__center'>
            <View className='kb-color__red kb-admin-modal__num'>{billInfo.arrears_money}</View>
            <View className='kb-size__sm'>待补金额（元）</View>
          </View>
        </View>
      </View>
    </KbCurtainModal>
  );
};

Index.defaultProps = {
  onConfirm: noop,
  onClose: noop,
  billInfo: { arrears_money: 0, order_num: 0 },
  isOpened: false,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
