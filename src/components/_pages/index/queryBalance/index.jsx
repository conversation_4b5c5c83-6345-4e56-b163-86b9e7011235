/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useRef, useState, useEffect } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { getPage } from '@base/utils/utils';
import { useDidShowCom, useUpdate } from '@base/hooks/page';
import KbModal from '@base/components/modal';
import isUndefined from 'lodash/isUndefined';
import checkBalance from '../_utils/queryBalance';
import KbAdminModal from './adminModal';
import './index.scss';

/**
 * 钱包余额查询弹窗
 *
 * 寄件页不展示NoticeBar，直接弹出去充值的弹窗
 *  */
const Index = (props) => {
  const { actionRef } = props;
  const compRef = useRef({ data: null });
  const [showTips, setShowTips] = useState(false);
  const [showMember, setShowMember] = useState(false);
  const [showAdmin, setShowAdmin] = useState(false);
  const [billInfo, setBillInfo] = useState({});
  const { loginData = {} } = useSelector((state) => state.global);
  const { logined, userInfo = {} } = loginData;
  const { is_admin = 0, regiment_id = 0 } = userInfo;
  const isAdmin = is_admin == 1;

  const isNeedChargeMoney = (arrears_money) => !isUndefined(arrears_money);

  const onJump = () => {
    const { order_num, order_number, arrears_money, money } = billInfo;
    if (!isAdmin) {
      Taro.navigator({
        url: 'order',
        target: 'tab',
        key: 'routerParamsChange',
        options: {
          tabKey: 'waitPay',
        },
      });
    }
    if (isNeedChargeMoney(arrears_money) || money < 0) {
      Taro.navigator({
        url: 'user/account/recharge',
      });
    } else {
      if (order_num == 1) {
        Taro.navigator({
          url: 'order/detail',
          options: {
            order_number,
            isAdmin,
          },
        });
      } else {
        Taro.navigator({
          url: 'order',
          target: 'tab',
          key: 'routerParamsChange',
          options: {
            tabKey: 'waitPay',
          },
        });
      }
    }
  };

  const onCancel = () => {
    setShowMember(false);
    setShowAdmin(false);
  };

  const triggerSet = (res) => {
    const page = getPage(-1);
    const { $router = {} } = page || {};
    const { path: currentPath } = $router;
    const isHomePage = currentPath === '/pages/index/index';
    const { arrears_money, money, order_num, is_arrears } = res;
    if ((arrears_money && arrears_money != 0) || order_num > 0 || is_arrears) {
      if (isHomePage) {
        setShowTips(true);
      } else {
        if (isNeedChargeMoney(arrears_money) || money < 0) {
          setShowAdmin(true);
        } else {
          setShowMember(true);
        }
      }
    } else {
      setShowTips(false);
      setShowMember(false);
      setShowAdmin(false);
    }
    setBillInfo(res);
  };

  const getBalance = () => {
    if (logined && (regiment_id != 0 || is_admin != 0)) {
      checkBalance(isAdmin).then((res = {}) => {
        compRef.current.data = res;
        triggerSet(res);
      });
    }
  };

  useEffect(() => {
    if (actionRef) {
      actionRef.current = {
        submitCheck: () => {
          // 提交订单时检查
          const res = (compRef && compRef.current && compRef.current.data) || {};
          if (res && (res.order_num > 0 || res.is_arrears)) {
            triggerSet(res);
            return true;
          }
        },
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 登录状态变更
  useUpdate((loginData) => {
    if (!loginData.logined) return;
    getBalance();
  }, []);

  useDidShowCom(() => {
    logined && getBalance();
  });

  const dealText = ({ arrears_money, money, order_num }) => {
    return isAdmin && (money < 0 || isNeedChargeMoney(arrears_money))
      ? '您的钱包余额不足，已影响团队寄件需求，请尽快充值！'
      : `您有${order_num ? order_num + '笔' : ''}待补款的订单，请尽快完成支付`;
  };

  return (
    <View>
      {showTips && (
        <View className='kb-notice-bar kb-margin-md'>
          <View
            className='kb-size__base kb-spacing-sm at-row at-row__align--center at-row__justify--between'
            onClick={onJump}
            hoverClass='kb-hover'
          >
            <Text>{dealText(billInfo)}</Text>
            <AtIcon
              color='#b27700'
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon-size__sm'
            />
          </View>
        </View>
      )}
      <KbAdminModal
        isOpened={showAdmin}
        onConfirm={onJump}
        onClose={onCancel}
        billInfo={billInfo}
      />
      <KbModal
        title='温馨提示'
        isOpened={showMember}
        top={false}
        closable={false}
        onClose={onCancel}
        confirmText=''
        bars={[{ label: '查看异常订单', key: 'confirm' }]}
        onConfirm={onJump}
        barOne
      >
        <View className='kb-spacing-md'>
          您当前存在未完成待补款的异常订单，请处理完成后继续下单！
        </View>
      </KbModal>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
