/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { adNavigator } from '@/components/ad-extension/sdk';
import { getAdConfig } from '@/components/ad-extension/_utils';
import { useUpdate } from '@base/hooks/page';
import { frequencyLimit, noop, reportAnalytics } from '@base/utils/utils';
import { Image, View } from '@tarojs/components';
import Taro, { Fragment, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtCurtain } from 'taro-ui';
import { isFitFXLXActivity } from '../fx-lx-activity/_utils';
import './index.scss';

const KbAdModal = (props) => {
  const { data, hasRole, closeBtnPosition = 'top-right', onClose = noop } = props;
  const [isOpened, updateIsOpened] = useState(false);
  const [popAds, upPopAds] = useState(null);

  const handleClose = () => {
    onClose();
    updateIsOpened(false);
  };

  // 登录状态变更
  useUpdate(
    (loginData) => {
      if (!loginData.logined || !hasRole) return;
      showActivity();
    },
    [hasRole],
  );

  const showActivity = async () => {
    const isLimit = frequencyLimit('check', 'home-modal');
    if (isLimit) return;
    // 全寄-全民分销版-首单寄件,福利补贴活动,活动期间做屏蔽处理；
    let fitFXLXActivity = false;
    try {
      fitFXLXActivity = await isFitFXLXActivity();
    } catch (error) {
      console.log('error', error);
    }
    if (fitFXLXActivity) return;
    getAdConfig(data).then((data) => {
      if (isArray(data) && data.length > 0) {
        upPopAds(data[0]);
        frequencyLimit('limit', 'home-modal');
        data[0] && updateIsOpened(true);
        reportAnalytics({
          key: 'home-modal',
          options: '首页弹窗',
        });
      }
    });
  };

  const handleClick = () => {
    adNavigator({
      ...popAds,
    });
    updateIsOpened(false);
    reportAnalytics({
      key: 'home-modal',
      options: '首页弹窗-点击跳转',
    });
  };

  return (
    <Fragment>
      <AtCurtain
        className='kb-curtain__large'
        isOpened={isOpened}
        onClose={handleClose}
        closeBtnPosition={closeBtnPosition}
      >
        <View className='kb-activity' onClick={handleClick}>
          <Image className='kb-activity--img' mode='widthFix' src={popAds.imgUrl} />
        </View>
      </AtCurtain>
    </Fragment>
  );
};

KbAdModal.options = {
  addGlobalClass: true,
};
export default KbAdModal;
