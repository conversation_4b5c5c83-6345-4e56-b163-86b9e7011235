/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import KbModal from '@/components/errorModal';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { importFieldHide } from '@base/utils/utils';
import './index.scss';
import { typeMapCn } from '../_utils';

const Index = ({ isOpened, onClose, onConfirm, data = {}, loading }) => {
  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo = {} } = loginData;
  const { mobile } = userInfo;

  const onClick = () => {
    onConfirm && onConfirm(data);
  };

  return (
    <KbModal
      title='授权页面'
      onClick={onClick}
      buttons={[
        {
          label: '一键授权关联',
          key: 'auth',
          type: 'primary',
          loading,
        },
      ]}
      isOpened={isOpened}
      onClose={onClose}
    >
      <View className='kb-text__center'>
        <View>尊敬的微快递特惠寄用户：</View>
        <View>{importFieldHide(mobile, 3, 7)}</View>
        <View>是否授权绑定至{typeMapCn[data.type]}账号：</View>
        <View>
          {data.mobile}（{data.name}）
        </View>
      </View>
    </KbModal>
  );
};

Index.options = {
  addGlobalClass: true,
};
