/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import KbModal from '@base/components/modal/index';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import request from '@base/utils/request';
import './index.scss';

const LinkModal = (props) => {
  const { isOpened, info = {}, onClose } = props;

  const encryptMobile = (phone) => {
    const mobile = '' + phone;
    return `${mobile.substring(0, 3)}****${mobile.substring(7, 11)}`;
  };

  const handleLink = () => {
    request({
      url: '/api/BindOpen/bindOpenAccount',
      data: {
        token: Taro.kbGetGlobalData('linkToken'),
      },
    }).then(({ code, msg }) => {
      if (code == 0) {
        Taro.kbToast({
          text: '绑定成功',
        });
        onClose();
      } else {
        Taro.kbToast({
          text: msg,
        });
      }
    });
  };

  return (
    <KbModal
      closable={false}
      top={false}
      title='授权页面'
      cancelText='暂不授权'
      confirmText='一键授权关联'
      isOpened={isOpened}
      onClose={() => onClose()}
      onCancel={() => onClose()}
      onConfirm={() => handleLink()}
    >
      <View className='kb-desc__title'>
        <View className='kb-size__xxl'>
          {info.regiment_mobile ? encryptMobile(info.regiment_mobile) : '--'}
        </View>
        <View className='kb-size__base'>是否授权绑定至“快宝开放平台账号”</View>
        <View className='kb-size__base'>
          {info.open_mobile || '--'}（{info.company || '--'}）
        </View>
      </View>
    </KbModal>
  );
};

LinkModal.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({
  loginData: global.loginData,
}))(LinkModal);
