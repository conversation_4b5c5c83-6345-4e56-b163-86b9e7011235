/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import icon_regiment from '@/assets/icon/icon_leader.png';
import icon_proxy_second from '@/assets/icon/icon_proxy_second.jpg';
import icon_proxy_first from '@/assets/icon/icon_proxy_first.jpg';
import { getCurrentUser } from '@base/utils/utils';
import dayjs from 'dayjs';
import { getCustomName } from '../../commission/utils';

const typeMap = {
  kdy: 'courier',
  yz: 'inn',
};
export const typeMapCn = {
  kdy: '快递员',
  yz: '驿站',
};

/**
 * 检查账号是否已经授权
 * @param {string} type 账号类型（courier快递员 inn驿站）
 * @param {string} c_id 快递员id/驿站id
 *  */
export const checkIsAuth = ({ c_id, type }) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/courier/Account/checkIsBind',
      toastLoading: false,
      directTriggerThen: true,
      data: {
        type: typeMap[type],
        c_id,
      },
    })
      .then((res = {}) => {
        if (res.code == 0 && !res.data) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};
/**
 * 授权驿站账号
 * @param {string} type 账号类型（courier快递员 inn驿站）
 * @param {string} c_id 快递员id/驿站id
 *  */
export function yzAuth({ c_id, type }, _this) {
  return new Promise((resolve, reject) => {
    request(
      {
        url: '/api/courier/Account/accountBind',
        loadingStatusKey: 'authing',
        toastError: true,
        toastSuccess: '授权成功',
        data: {
          type: typeMap[type],
          c_id,
        },
      },
      _this,
    )
      .then((res = {}) => {
        if (res.code == 0 && res.data) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(reject);
  });
}

/**
 * 获取当前用户的标题及等级图标
 * @param {string} level 外部传入的层级（0，1，2）
 *  */
export const getUserTittleAndTagImg = async ({ level } = {}) => {
  const isAdmin = getCurrentUser('regiment');
  const isLeague = getCurrentUser('league');
  const isMember = getCurrentUser('member');
  const isCustom = getCurrentUser('custom');
  const isRole = getCurrentUser('role');

  let obj = {
    tag: isLeague ? '尊贵合伙人' : isAdmin ? '荣耀团长' : isRole ? '高级寄件会员' : '',
    img: isAdmin ? icon_regiment : '',
  };

  if (isCustom) {
    const names = await getCustomName();
    console.log('names', names);
    const findName = (l) => {
      return names.find((v) => v.key == l) || {};
    };

    obj.tag =
      isAdmin && !isLeague
        ? findName(1).title
        : isLeague
        ? findName(2).title
        : isMember
        ? findName(0).title
        : '';
    obj.img = isMember
      ? icon_regiment
      : isAdmin && !isLeague
      ? icon_proxy_second
      : isLeague
      ? icon_proxy_first
      : '';

    if (level) {
      switch (level * 1) {
        case 3: // 合伙人
        case 2:
          obj.tag = findName(2).title;
          obj.img = icon_proxy_first;
          break;
        case 1:
          obj.tag = findName(1).title;
          obj.img = icon_proxy_second;
          break;
        case 0:
          obj.tag = findName(0).title;
          obj.img = icon_regiment;
          break;
        default:
          break;
      }
    }
  }

  return obj;
};

export const getUserLevel = () => {
  const isAdmin = getCurrentUser('regiment');
  const isLeague = getCurrentUser('league');
  const isPartner = getCurrentUser('partner');
  return isPartner ? 3 : isLeague ? 2 : isAdmin ? 1 : 0;
};

export const getScanInfo = (token) => {
  return request({
    url: '/api/BindOpen/getOpenInfo',
    toastLoading: false,
    data: {
      token,
    },
  });
};

// 拉新活动时间
const recruitActivityDate = '2024-12-31';
export const checkIsRecruitActivityStatus = () => {
  return new Promise((resolve) => {
    const now = new Date();
    if (dayjs(recruitActivityDate).isAfter(now)) {
      resolve(true);
    } else {
      resolve(false);
    }
  });
};

export const checkClipboard = () => {
  Taro.getClipboardData().then((data) => {
    const { data: keywords } = data || {};
    keywords &&
      request({
        url: '/api/Online/extractTrackingNumber',
        data: { keywords },
        toastLoading: false,
        onThen: (res) => {
          const { code, data: waybill } = res || {};
          if (code == 0 && waybill) {
            Taro.kbModal({
              top: false,
              closable: false,
              title: '友情提示',
              content: [
                { className: 'kb-text__center', text: '您是否想查询以下快递单号：' },
                { className: 'kb-text__center', text: waybill },
              ],
              confirmText: '马上查询',
              cancelText: '我不需要',
              onConfirm: () => {
                Taro.navigator({
                  url: 'query/match',
                  options: {
                    waybill: waybill,
                  },
                });
              },
            });
          }
        },
      });
  });
};
