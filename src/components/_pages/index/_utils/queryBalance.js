/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import apis from '@/utils/apis';

/**
 * 检查用户余额
 * @param {boolean} isAdmin 是否是团长
 *  */
const checkBalance = (isAdmin) => {
  return new Promise((resolve, reject) => {
    const url = apis[`order.${isAdmin ? 'leader' : 'member'}.getUnPayList`];
    request({
      url,
      toastLoading: false,
      directTriggerThen: true,
    })
      .then((res = {}) => {
        if (res.code == 0) {
          resolve(res.data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

export default checkBalance;
