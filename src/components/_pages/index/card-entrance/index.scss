/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-card-entrance {
  position: relative;
  margin: 0 32px 20px;
  &__bottom {
    padding: 0 25px;
    background: #fff;
    border-radius: $border-radius-xxl;
    .scrollview {
      position: relative;
      width: 100%;
      height: 120px;
      padding-bottom: 25px;
      overflow: hidden;
      ::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
      }
    }
    &--list {
      position: absolute;
      display: flex;
    }
    &--item {
      position: relative;
      width: 130px;
      margin-right: 20px;
      padding-top: 25px;
      &__img {
        width: 56px !important;
        height: 56px !important;
      }
      &-tag {
        position: absolute;
        top: 0px;
        right: 0px;
        z-index: 10;
        padding: 5px 10px;
        color: $color-white;
        font-size: 22px;
        white-space: nowrap;
        background: $color-red;
        border: $border-lighter;
        border-radius: $border-radius-md;
        transform: translateY(-100%);
      }
      &:last-child {
        margin-right: 0;
      }
      .shareBtn {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
    }
    &--notice {
      overflow: hidden;
      border-top: $border-lighter;
      &__title {
        min-width: 140px;
      }
      &__bar {
        flex: 1;
      }
    }
  }
}
