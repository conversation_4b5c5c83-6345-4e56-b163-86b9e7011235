/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useRef } from '@tarojs/taro';
import { View, Image, ScrollView, Button } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { getCurrentUser, getSessionIDFromLoginData } from '@base/utils/utils';
import { createBars } from '@/components/_pages/user/_utils';
import KbNoticeBar from '@base/components/notice-bar';
import KbModal from '@/components/errorModal';
import { AtBadge, AtButton, AtIcon } from 'taro-ui';
import { getAdConfig } from '@/components/ad-extension/_utils';
import { CreateSubscribeMessage } from '@base/components/subscribe/sdk';
import { useUpdate } from '@base/hooks/page';
import { getleaderInfo, registerLock } from '../../team/_utils';
import './index.scss';

const KbCardEntrance = (props) => {
  const { data, mastLogin, hasRole } = props;
  const actionRef = useRef({});
  const [isOpened, setIsOpened] = useState(false);
  const [notice, setNotice] = useState('');
  const [leaderInfo, setLeaderInfo] = useState({});
  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo } = loginData;
  const { is_admin, auth_login_user_id } = userInfo || {};
  const { finance_display_status = '0' } = leaderInfo || {};
  const isAdmin = getCurrentUser('regiment');

  let memberConfig = ['fee', 'closedArea', 'share'];
  let leaderConfig = ['fare', 'team', 'poster', 'profit', 'closedArea'];

  if (getCurrentUser('custom')) {
    memberConfig = ['poster', 'commission', 'fee', 'closedArea', 'share'];
    leaderConfig = ['poster', 'commission', 'fare', 'profit', 'closedArea'];
  }

  const bars = createBars({
    params: is_admin,
    isOldLeader: true,
    is_league: getCurrentUser('league'),
  });

  const arr = isAdmin ? leaderConfig : memberConfig;

  let barsConfig = [];
  if (arr && arr.length > 0) {
    arr.map((v) => {
      const item = bars.find((i) => i.key == v);
      if (item) {
        barsConfig.push(item);
      }
    });
  }

  // 被授权账号被关闭资金权限后，不展示某些资金相关入口
  barsConfig =
    is_admin > 0 && auth_login_user_id > 0 && finance_display_status >= 1
      ? barsConfig.filter((i) => !['fare', 'team', 'profit', 'export'].includes(i.key))
      : barsConfig;

  useUpdate((loginRes) => {
    const { logined, userInfo } = loginRes || {};
    if (!logined) return;
    if (userInfo.is_admin == 1 && userInfo.auth_login_user_id > 0) {
      getleaderInfo({
        regiment_id: userInfo.regiment_id,
      }).then((res) => {
        if (res.data) {
          setLeaderInfo(res.data);
        }
      });
    }
  });

  const onClick = (url) => {
    if (registerLock.check(true)) return;
    if (url === 'profit' || url === 'team/manage/poster') {
      // 查看【收益统计详细】通知订阅(https://tower.im/teams/258300/todos/106406/)
      const subIns = new CreateSubscribeMessage();
      subIns.trigger('profit');
    }
    Taro.navigator({
      url,
    });
  };

  const onOpenNotice = () => {
    setIsOpened(true);
  };

  const getAdList = () => {
    actionRef.current.loaded = true;
    getAdConfig(data).then((res) => {
      const { remark } = res[0] || {};
      setNotice(remark);
    });
  };

  useEffect(() => {
    const { logined } = loginData || Taro.kbLoginData || {};
    if ((!logined && mastLogin) || !hasRole || actionRef.current.loaded) return;
    getAdList();
  }, [getSessionIDFromLoginData(loginData), data, hasRole]);

  return (
    <View className='kb-card-entrance'>
      <View className='kb-card-entrance__bottom'>
        <ScrollView scrollX className='scrollview'>
          <View className='kb-card-entrance__bottom--list'>
            {barsConfig.map((item) => {
              return (
                <View
                  onClick={onClick.bind(null, item.url)}
                  hoverClass='kb-hover-opacity'
                  className='kb-text__center kb-card-entrance__bottom--item'
                  key={item.key}
                >
                  <AtBadge value={item.tag}>
                    {item.key == 'share' && (
                      <Button className='shareBtn at-button' openType='share' />
                    )}
                    <Image
                      className='kb-card-entrance__bottom--item__img'
                      src={item.image}
                      mode='widthFix'
                      lazyLoad
                    />
                    <View className='kb-size__base'>{item.value}</View>
                  </AtBadge>
                </View>
              );
            })}
          </View>
        </ScrollView>
        {notice && (
          <View className='kb-card-entrance__bottom--notice'>
            <View className='at-row at-row__align--center'>
              <View className='kb-card-entrance__bottom--notice__title kb-size__sm'>
                <AtIcon
                  prefixClass='kb-icon'
                  value='volume1'
                  className='kb-icon-size__base kb-color__brand kb-margin-sm-r'
                />
                公告消息
              </View>
              <View
                className='kb-card-entrance__bottom--notice__bar'
                hoverClass='kb-hover-opacity'
                onClick={onOpenNotice}
              >
                <KbNoticeBar type='transparent' marquee>
                  {notice}
                </KbNoticeBar>
              </View>
            </View>
          </View>
        )}
        <KbModal
          renderFooter={
            <View className='kb-margin-md'>
              <AtButton circle type='primary' onClick={() => setIsOpened(false)}>
                我已知晓
              </AtButton>
            </View>
          }
          title='公告通知'
          buttons={[]}
          isOpened={isOpened}
          onClose={() => setIsOpened(false)}
        >
          <View className='kb-rules__title kb-size__17 kb-text__center kb-margin-xl-b'>
            {notice}
          </View>
        </KbModal>
      </View>
    </View>
  );
};

KbCardEntrance.options = {
  addGlobalClass: true,
};

export default KbCardEntrance;
