/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState, useEffect, useRef } from '@tarojs/taro';

// eslint-disable-next-line import/prefer-default-export
export const useHomeTop = () => {
  const allBrands = ['sto', 'dp', 'zt', 'sf', 'ky'];
  const oldClass = ['left', 'active', 'right', 'normal', 'normal'];
  const [swiper, setSwiper] = useState(0);
  const ref = useRef({ oldClass });
  const [cList, setCList] = useState(oldClass);

  const move = () => {
    const _cList = [...ref.current.oldClass];
    _cList.unshift(_cList.pop());
    ref.current.oldClass = _cList;
    setCList(_cList);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      move();
    }, 2000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  const handleSwipeChange = (ev) => {
    setSwiper(ev.detail.current);
  };

  return {
    cList,
    move,
    list: allBrands,
    swiper,
    handleSwipeChange,
  };
};
