/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import KbButton from '@base/components/button';
import classNames from 'classnames';
import { getBrandItem } from '../../_utils';
import { useHomeTop } from './_utils';
import './index.scss';

const KbHomeTop = () => {
  const { brands: oBrands } = useSelector((state) => state.global);
  const { cList, list = [], move } = useHomeTop();

  return (
    <View className='kb-home-top' onClick={move}>
      <View className='kb-home-top__brands'>
        <View className='swipe'>
          {list.map((item, itemIndex) => {
            const oBrandItem = getBrandItem(item, oBrands);
            const swipeItemCls = classNames('swipe-item', `swipe-item--${cList[itemIndex]}`);
            return (
              <View className={swipeItemCls} key={item}>
                <Image className='swipe-item-img' mode='widthFix' src={oBrandItem.logo_link} />
              </View>
            );
          })}
        </View>
      </View>
      <View className='kb-home-top__desc at-row at-row__align--center'>
        <Text>全网品牌比价！</Text>
        <KbButton className='kb-cps_navLeft' openType='share'>
          <View className='at-row at-row__align--center'>
            <Image
              className='kb-cps_navLeft-icon'
              src='https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_wx.png'
            />
            <Text className='kb-cps_navLeft-txt'>分享</Text>
          </View>
        </KbButton>
      </View>
    </View>
  );
};

KbHomeTop.options = {
  addGlobalClass: true,
};

export default KbHomeTop;
