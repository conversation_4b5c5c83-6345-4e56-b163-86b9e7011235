

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import dayjs from 'dayjs';

export const getCurrentMonthCN = (month) => {
  const cn = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
  return cn[dayjs(month).month()];
};

export const calculateRestDay = (end) => {
  if (end == 0) {
    return {
      day: 0,
      hour: 0,
      mint: 0,
    };
  }
  const startDate = dayjs();
  const endDate = dayjs(end);
  const duration = dayjs.duration(endDate.diff(startDate), 'milliseconds');
  const day = parseInt(duration.asDays());
  const hour = duration.hours();
  const mint = duration.minutes();
  return {
    day: day <= 0 ? 0 : day,
    hour: hour <= 0 ? 0 : hour,
    mint: mint <= 0 ? 0 : mint,
  };
};

export const stageConfig = {
  sf: {
    num: [10, 30],
    percent: [0.05, 0.1],
    eg: '例如：活动月内完成12单签收，累计总运费500元，您可获得25元返现金额；30单，累计总运费1000元，可获得100元返佣；',
  },
  dp: {
    num: [10, 30],
    percent: [0.1, 0.27],
    eg: '例如：活动月内完成12单签收，累计总运费1000元，您可获得100元返现金额；30单，累计总运费5000元，可获得1350元返佣；',
  },
};

export const formatPercent = (percent) => {
  return `${percent * 100}%`;
};
