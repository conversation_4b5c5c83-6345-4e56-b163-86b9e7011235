

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { useState, useEffect, useRef } from '@tarojs/taro';
import { View } from '@tarojs/components';
import numeral from 'numeral';
import KbLongList from '@base/components/long-list';
import isArray from 'lodash/isArray';
import dayjs from 'dayjs';
import { getCurrentMonthCN, stageConfig } from '../_utils';
import './index.scss';

const Index = ({ brand, config }) => {
  const [list, setList] = useState([]);
  const [active, setActive] = useState(false);
  const listIns = useRef();

  const { num = [] } = config || stageConfig[brand] || {};

  const formatNum = (num = 0) => numeral(num).format('0.00');

  const listData = {
    api: {
      url: '/api/ArrivePay/lists',
      data: {
        brand,
      },
      formatResponse: (res) => {
        const { data: list } = res;
        if (isArray(list) && list.length > 0) {
          return {
            data: { list },
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (list) => {
        setList(list);
      },
    },
  };

  const onReady = (ins) => {
    listIns.current = ins;
  };

  useEffect(() => {
    if (brand) {
      setActive(true);
      listIns.current.loader({ brand });
    }
  }, [brand]);

  const year = list[0] ? list[0].year : dayjs().get('year');

  return (
    <View className='kb-duration kb-margin-xl-t'>
      <View className='kb-margin-xl-lr kb-spacing-xl kb-background__white kb-border-radius__xxl'>
        <View className='kb-size__lg'>
          <View>历史佣金记录【{year}年度】</View>
        </View>
        <KbLongList active={active} onReady={onReady} data={listData} height='auto'>
          <View className='kb-list'>
            <View className='kb-margin-xl-t'>
              <View className='at-row at-row__align--center at-row__justify--between kb-margin-md-b'>
                <View className='at-col at-col-3'>活动时间</View>
                <View className='at-col at-col-3 kb-text__center'>阶段一</View>
                <View className='at-col at-col-3 kb-text__center'>阶段二</View>
                <View className='at-col at-col-3 kb-text__right'>总返现</View>
              </View>
              {list.map((val) => {
                const { rebate, order_num } = val || {};
                return (
                  <View
                    key={val.month}
                    className='at-row at-row__align--center at-row__justify--between kb-margin-md-b kb-color__grey-3'
                  >
                    <View className='at-col at-col-3'>第{getCurrentMonthCN(val.date)}月</View>
                    <View className='at-col at-col-3 kb-text__center'>
                      {order_num >= num[0] ? '已达标' : '未达标'}
                    </View>
                    <View className='at-col at-col-3 kb-text__center'>
                      {order_num >= num[1] ? '已达标' : '未达标'}
                    </View>
                    <View
                      className={`${
                        rebate > 0 ? 'kb-color__green' : ''
                      } at-col at-col-3 kb-text__right`}
                    >
                      {rebate > 0 ? `+${formatNum(val.rebate)}元` : '未达标'}
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </KbLongList>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
