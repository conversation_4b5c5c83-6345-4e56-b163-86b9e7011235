

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { useState, useEffect, useRef } from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import dayjs from 'dayjs';
import { getCurrentMonthCN, calculateRestDay } from '../_utils';
import './index.scss';

const Index = ({ end }) => {
  const timer = useRef();

  const [time, setTime] = useState(calculateRestDay(end));

  useEffect(() => {
    setTime(calculateRestDay(end));
    timer.current = setInterval(() => {
      const times = calculateRestDay(end);
      setTime(times);
    }, 1000 * 5);
    return () => {
      clearInterval(timer.current);
    };
  }, [end]);

  return (
    <View className='kb-countDown'>
      <View className='kb-margin-xl-lr kb-spacing-xl kb-background__white kb-border-radius__xxl'>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='kb-size__lg'>活动时间</View>
          <View>
            <View className='kb-size__sm at-row at-row__align--center'>
              <View className='kb-countDown__fire kb-margin-xs'>
                <Image
                  src='https://cdn-img.kuaidihelp.com/qj/miniapp/arrivePay-hot.png'
                  mode='widthFix'
                />
              </View>
              <View className='kb-margin-sm-l'>
                <Text>第{getCurrentMonthCN(dayjs())}月</Text>
                <Text className='kb-color__grey'>火热进行中</Text>
              </View>
            </View>
          </View>
        </View>

        <View className='at-row at-row__align--center at-row__justify--between kb-margin-xl-t'>
          <View className='at-col'>
            <Text className='kb-countDown__text kb-color__grey-3 kb-size__md'>剩余</Text>
            <Text className='kb-countDown__num kb-color__white kb-size__xxl kb-text__center kb-margin-xl-l'>
              {time.day}
            </Text>
          </View>
          <View className='at-col'>
            <Text className='kb-countDown__text kb-color__grey-3 kb-size__md kb-margin-md-l'>
              天
            </Text>
            <Text className='kb-countDown__num kb-color__white kb-size__xxl kb-text__center kb-margin-xl-l'>
              {time.hour}
            </Text>
          </View>
          <View className='at-col'>
            <Text className='kb-countDown__text kb-color__grey-3 kb-size__md'>小时</Text>
            <Text className='kb-countDown__num kb-color__white kb-size__xxl kb-text__center kb-margin-xl-l'>
              {time.mint}
            </Text>
          </View>
          <Text className='kb-countDown__text kb-color__grey-3 kb-size__md'>分钟</Text>
        </View>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
