

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-countDown {
  &__fire {
    width: 24px;
    height: 24px;
    Image {
      width: 100%;
      height: 100%;
    }
  }
  &__num {
    display: inline-block;
    width: 72px;
    height: 72px;
    font-weight: bold;
    line-height: 72px;
    background: linear-gradient(0deg, #1480ff 0%, #1480ff 0%, #30b9f1 100%, #30b9f1 100%);
    border-radius: 16px;
  }
}
.kb-border-radius__xxl {
  border-radius: $border-radius-xxl;
}
