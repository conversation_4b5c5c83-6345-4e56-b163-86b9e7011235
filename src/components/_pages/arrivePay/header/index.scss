

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-header {
  width: 100%;
  background: url('https://cdn-img.kuaidihelp.com/qj/miniapp/arrivePay-bg.png') no-repeat;
  background-size: 100% auto;
  &__wrapper {
    &--title {
      position: relative;
      &-sub {
        position: absolute;
        top: 50px;
        right: 20px;
        padding: $spacing-v-sm $spacing-v-md;
        color: $color-brand;
        background-color: $color-white;
        border-radius: $border-radius-md;
        &::before {
          position: absolute;
          bottom: -10px;
          left: 15px;
          border: 15px solid $color-white;
          border-right-color: transparent;
          border-bottom: 0;
          border-left-color: transparent;
          content: '';
        }
      }
      &-big {
        padding-top: 115px;
        padding-bottom: 50px;
        font-size: 60px;
        letter-spacing: 10px;
      }
    }
    &--info {
      margin-top: $spacing-h-xxl;
      background: linear-gradient(180deg, #cafbff 0%, #cafbff 0%, #ffffff 51%, #ffffff 100%);
      border-radius: $border-radius-xxl;
      &--background {
        padding: 0 5px;
        border: $border-lighter;
        border-color: $color-brand;
        border-radius: $border-radius-md;
      }
    }
    &--progress-bar {
      height: 22px;
      margin-top: $spacing-h-md;
      &__left {
        height: 100%;
        margin-right: $spacing-h-sm;
        background-color: $color-blue-0;
        border-top-left-radius: $border-radius-arc;
        border-bottom-left-radius: $border-radius-arc;
      }
      &__right {
        height: 100%;
        margin-left: $spacing-h-sm;
        background-color: $color-bluer;
        border-top-right-radius: $border-radius-arc;
        border-bottom-right-radius: $border-radius-arc;
      }
    }
  }
}
