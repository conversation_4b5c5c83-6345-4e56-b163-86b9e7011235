/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { stageConfig, formatPercent } from '@/components/_pages/arrivePay/_utils';
import './index.scss';

const Index = ({ brand, config }) => {
  const { num, percent = [], eg = '' } = config || stageConfig[brand] || {};
  return (
    <View className='kb-header'>
      <View className='kb-header__wrapper kb-spacing-xl'>
        <View className='kb-header__wrapper--title kb-color__white kb-text__center'>
          <View className='kb-header__wrapper--title-sub kb-size__lg'>“微快递特惠寄”团长专享</View>
          <View className='kb-header__wrapper--title-big'>下单即享平台返佣补贴</View>
        </View>
        <View className='kb-header__wrapper--info kb-spacing-xl'>
          <View className='kb-size__lg'>活动说明</View>
          <View className='kb-size__sm at-row at-row__align--center kb-margin-sm-t'>
            <View>
              活动月内<Text className='kb-color__brand'>团长+所属团员</Text>
              累计完成票【现付/到付】订单目标即可返佣
            </View>
          </View>
          <View className='kb-header__wrapper--progress kb-margin-lg-t'>
            <View className='at-row at-row__align--center'>
              <View className='at-col at-col-6'>
                <View className='kb-margin-lg-l'>
                  <View className='kb-margin-sm-b kb-size__sm kb-color__blue'>阶段1</View>
                  <View className='kb-margin-sm-b kb-size__xs'>
                    完成<Text className='kb-color__blue'>{num[0] || 10}单</Text>可获得
                    <Text className='kb-color__blue'>{formatPercent(percent[0])}</Text>的佣金返利
                  </View>
                  <View className='kb-header__wrapper--progress-bar'>
                    <View className='kb-header__wrapper--progress-bar__left'></View>
                  </View>
                </View>
              </View>
              <View className='at-col at-col-6'>
                <View className='kb-margin-lg-r'>
                  <View className='kb-margin-sm-b kb-margin-sm-l kb-size__sm kb-color__bluer'>
                    阶段2
                  </View>
                  <View className='kb-margin-sm-b kb-margin-sm-l kb-size__xs'>
                    完成<Text className='kb-color__bluer'>{num[1] || 30}单</Text>可获得
                    <Text className='kb-color__bluer'>{formatPercent(percent[1])}</Text>的佣金返利
                  </View>
                  <View className='kb-header__wrapper--progress-bar'>
                    <View className='kb-header__wrapper--progress-bar__right'></View>
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View className='kb-margin-lg-b kb-margin-xl-t'>
            多下多得，无上限，后续最高返佣可高至{brand == 'sf' ? 20 : 30}%，敬请期待！
          </View>
          <View className='kb-color__grey-3'>{eg || stageConfig[brand].eg}</View>
        </View>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
