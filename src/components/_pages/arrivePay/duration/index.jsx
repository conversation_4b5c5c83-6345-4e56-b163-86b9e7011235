

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { Fragment } from '@tarojs/taro';
import { View, Image, Text, Button } from '@tarojs/components';
import { AtIcon, AtProgress } from 'taro-ui';
import { stageConfig, formatPercent } from '@/components/_pages/arrivePay/_utils';
import classNames from 'classnames';
import './index.scss';

const Index = ({ currentNum, status, brand, config }) => {
  const { num = [], percent: configPercent = [] } = config || stageConfig[brand] || {};

  const [stageOne = 10, stageTow = 30] = num;

  const stage = currentNum >= stageOne ? 1 : 0;

  const percent = (Number(currentNum) / stageTow) * 100;

  const finished = status != 0 || currentNum >= num[1];

  return (
    <View className='kb-duration kb-margin-xl-t'>
      <View className='kb-margin-xl-lr kb-spacing-xl kb-background__white kb-border-radius__xxl'>
        <View className='kb-size__lg at-row at-row__align--center at-row__justify--between'>
          <View>目标进度</View>
          {!finished && (
            <View className='kb-duration__invite kb-size__md' hoverClass='kb-hover-opacity'>
              <Button className='at-button' openType='share' />
              <Image
                src='https://cdn-img.kuaidihelp.com/qj/miniapp/arrivePay-invite.png'
                mode='widthFix'
              />
              <Text className='kb-margin-sm-lr'>邀请团员下单</Text>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className='kb-icon-size__base kb-color__grey'
              />
            </View>
          )}
        </View>
        <View className='kb-duration__progress kb-margin-xl-t'>
          <View
            className={classNames(
              'kb-duration__progress-inside',
              `kb-color__${currentNum == 0 ? 'black' : 'white'}`,
            )}
          >
            {stage == 1
              ? `${formatPercent(configPercent[finished ? 1 : 0])}返佣中`
              : `已完成${currentNum}单`}
          </View>
          <View
            className='kb-duration__progress-flag'
            style={{ left: `calc(${(stageOne / stageTow) * (100).toFixed(2)}% - 15px)` }}
          >
            {stageOne}单
          </View>
          <View className='kb-duration__progress-after'>{stageTow}单</View>
          <AtProgress percent={status == 0 ? percent : 100} strokeWidth={20} isHidePercent />
        </View>
        <View className='kb-text__center kb-size__xl kb-margin-xl-t'>
          {!finished ? (
            <Fragment>
              <Fragment>
                还差 <Text className='kb-color__brand'>{num[stage] - Number(currentNum)}</Text> 单
                完成阶段{stage == 0 ? '一' : '二'}目标 返佣{' '}
                <Text className='kb-color__brand'>{formatPercent(configPercent[stage])}</Text>
              </Fragment>
            </Fragment>
          ) : (
            <Fragment>
              <Text className='kb-color__brand'>已达标</Text> 佣金持续发放中
            </Fragment>
          )}
        </View>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
