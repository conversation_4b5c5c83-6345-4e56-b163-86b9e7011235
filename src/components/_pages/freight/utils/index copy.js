/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import { defaultDiscount } from '@/reducers/global';
import { sortBrands } from '../../order/extra-info/_utils';

function setDiscount(discount) {
  return Array.from({ length: 99 }, (v, k) => (k + 1) / 10)
    .slice(discount * 10 - 1)
    .reverse(); // eslint-disable-line
}

export function setFreight(params) {
  return request({
    url: '/api/Freight/set',
    data: params,
    toastError: true,
  });
}

/**
 * 获取所有下单品牌
 *  */
export function getBrands(force = false, opts) {
  const { source } = opts || {};
  return new Promise((resolve, reject) => {
    if (Taro.allUserBrands && !force) {
      resolve(Taro.allUserBrands);
    } else {
      request({
        url: '/api/Online/getBrands',
        toastLoading: false,
        toastError: false,
        toastSuccess: false,
        data: {
          source,
        },
      })
        .then((res) => {
          const { code, data } = res;
          if (code == 0 && isArray(data)) {
            const availableBrands = data.filter((v) => v.disable == 0);
            // const dpBrand = availableBrands.find((v) => v.brand == 'dp');
            // const dpBrands = []
            // if (dpBrand && dpBrand.channel_types && Array.isArray(dpBrand.channel_types.offline)) {
            //   dpBrand.channel_types.offline.forEach((v) => {
            //     dpBrands.push({
            //       ...dpBrand,
            //       sub: v.label,
            //       brand: v.value
            //     });
            //   })
            //   availableBrands.splice(
            //     availableBrands.findIndex((v) => v.brand == 'dp'),
            //     1,
            //     ...dpBrands,
            //   );
            // }
            // console.log('availableBrands',availableBrands)
            Taro.allUserBrands = availableBrands;
            resolve(availableBrands);
          } else {
            reject();
          }
        })
        .catch(reject);
    }
  });
}

/**
 * 返回品牌费用设置列表或折扣设置列表
 * @param {string} type fee | discount
 * @param {Array} allUserBrands 可用的下单品牌
 *  */
export const formatBrandConfig = ({ allUserBrands = [], type }, isMember, isSetting) => {
  const sort_brands = sortBrands(allUserBrands) || [];
  const keyMap = {
    discount: {
      key: 1,
      placeholder: '请设置折扣值',
    },
    fee: {
      key: 2,
      placeholder: '请输入加收费',
    },
  };
  const oTypeConfig = keyMap[type] || {};
  const oSortBrands = sort_brands.find((i) => i.key == oTypeConfig.key);
  console.log('oSortBrands', oSortBrands);
  const configBrands = oSortBrands.brands.map((item) => {
    return {
      brand: item,
      placeholder: oTypeConfig.placeholder,
    };
  });
  console.log('configBrands', configBrands);

  // 德邦八折， 接口增加 brand=dp -> [channel_types.offline]
  // 变更为加盟商也显示.............
  if (isMember) {
    if (oSortBrands.items) {
      const dpBrand = oSortBrands.items.find((item) => item.brand == 'dp');
      const dpBrands = [];
      if (dpBrand && dpBrand.channel_types && Array.isArray(dpBrand.channel_types.offline)) {
        dpBrand.channel_types.offline.forEach((v) => {
          dpBrands.push({
            brand: dpBrand.brand,
            placeholder: oTypeConfig.placeholder,
            sub: v.value == 'dp' ? '月结' : '8折',
            value: v.value,
          });
        });
        if (isSetting) {
          dpBrands.push({
            brand: dpBrand.brand,
            placeholder: oTypeConfig.placeholder,
            sub: '原价',
            value: 'dp_original',
          });
        }
        //改顺序
        const dpRate = dpBrands.find((v) => v.value == 'dp_rate');
        if (dpRate) {
          dpBrands.splice(
            dpBrands.findIndex((v) => v.value == 'dp_rate'),
            1,
          );
          dpBrands.push(dpRate);
        }
        //添加到配置列表
        configBrands.splice(
          configBrands.findIndex((v) => v.brand == 'dp'),
          1,
          ...dpBrands,
        );
      }
    }
  }

  return configBrands;
};

/**
 * 获取团长折扣设置范围
 *  */
export function getDiscountThreshold(params) {
  return new Promise((resolve) => {
    getBrands(true, params)
      .then((allUserBrands) => {
        const obj = {};
        allUserBrands.forEach((val) => {
          obj[val.brand] = {
            ...val,
          };
        });
        resolve(obj);
      })
      .catch(() => {
        resolve(defaultDiscount);
      });
  });
}

/**
 * @description 获取折扣选项
 * @param current 品牌
 * @param {boolean} toSting 是否将数据转换为string类型
 * @param {string} source batch | detail 来源
 *  */
export function getDiscountOptions({
  current,
  toSting = false,
  source = 'detail',
  discount,
  showIncome = false,
}) {
  return new Promise((reslove) => {
    const { rate } = discount[current] || {};
    const calculateIncome = (item) => {
      const num = Math.abs((rate * 1 || 0) - (item * 1 || 0));
      return Math.round(num * 10);
    };
    let options = setDiscount(rate).map((item) => {
      const income = calculateIncome(item);
      return {
        label: `${item}折`,
        value: toSting ? item.toString() : item,
        subTitle: showIncome && {
          className: 'kb-color__brand',
          text: `${income}%利润`,
        },
      };
    });
    options.unshift({
      label: `原价`,
      value: toSting ? '10' : 10,
      subTitle: showIncome && {
        className: 'kb-color__brand',
        text: `${calculateIncome(10)}%利润`,
      },
    });
    if (source == 'batch') {
      options.unshift({
        label: `原始折扣`,
        value: toSting ? '0' : 0,
        subTitle: showIncome && {
          className: 'kb-color__brand',
          text: `${calculateIncome(10)}%利润`,
        },
      });
    }
    reslove(options);
    return options;
  });
}

/**
 * 获取德邦返佣利率
 *  */
export function getDpRate() {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/regimentDpProfitRate',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        const { code, data } = res;
        if (code == 0) {
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
}

export const priceShowConfig = [
  {
    label: '仅展示原价',
    key: 1,
  },
  {
    label: '仅显示成本价',
    key: 2,
  },
  {
    label: '同时展示成本价和原价',
    key: 3,
  },
];

/**
 * 获取下单时，价格展示的策略
 * @returns {string} 1：仅展示原价，2：仅显示成本价，3：同时展示成本价和原价
 *  */
export const getPriceShowConfig = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/getShowQuotationType',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        const { code, data } = res;
        const { quotation_type = 2 } = data || {};
        if (code == 0) {
          resolve(Number(quotation_type));
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};
/**
 * 设置价格展示的策略
 * @returns {string} 1：仅展示原价，2：仅显示成本价，3：同时展示成本价和原价
 *  */
export const setPriceShowConfig = (quotation_type) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/changeShowQuotationType',
      toastLoading: true,
      toastError: true,
      toastSuccess: true,
      data: {
        quotation_type,
      },
    })
      .then((res) => {
        const { code, data } = res;
        if (code == 0) {
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};
