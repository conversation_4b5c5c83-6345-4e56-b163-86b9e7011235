/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import { defaultDiscount } from '@/reducers/global';
import { sortBrands } from '../../order/extra-info/_utils';

function setDiscount(discount) {
  return Array.from({ length: 99 }, (v, k) => (k + 1) / 10)
    .slice(discount * 10 - 1)
    .reverse(); // eslint-disable-line
}

export function setFreight(params) {
  return request({
    url: '/api/Freight/set',
    data: params,
    toastError: true,
  });
}

/**
 * 获取所有下单品牌
 *  */
export function getBrands(force = false, opts) {
  const { source } = opts || {};
  return new Promise((resolve, reject) => {
    if (Taro.allUserBrands && !force) {
      resolve(Taro.allUserBrands);
    } else {
      request({
        url: '/api/Online/getBrands',
        toastLoading: false,
        toastError: false,
        toastSuccess: false,
        data: {
          source,
        },
      })
        .then((res) => {
          const { code, data } = res;
          if (code == 0 && isArray(data)) {
            const availableBrands = data.filter((v) => v.disable == 0);
            // const dpBrand = availableBrands.find((v) => v.brand == 'dp');
            // const dpBrands = []
            // if (dpBrand && dpBrand.channel_types && Array.isArray(dpBrand.channel_types.offline)) {
            //   dpBrand.channel_types.offline.forEach((v) => {
            //     dpBrands.push({
            //       ...dpBrand,
            //       sub: v.label,
            //       brand: v.value
            //     });
            //   })
            //   availableBrands.splice(
            //     availableBrands.findIndex((v) => v.brand == 'dp'),
            //     1,
            //     ...dpBrands,
            //   );
            // }
            // console.log('availableBrands',availableBrands)
            Taro.allUserBrands = availableBrands;
            resolve(availableBrands);
          } else {
            reject();
          }
        })
        .catch(reject);
    }
  });
}

/**
 * 替换多重线下渠道原价value值
 * @param {*} k
 * @param {*} b
 */
export const replaceOriginalKey = (k, b) => {
  return k.value === b.brand ? `${k.value}_original` : k.value;
};

/**
 * 检查品牌是否存在多重线下渠道
 * @param {*} data
 */
export const checkIsDivideBrand = (data) => {
  const { channel_types } = data || {};
  return channel_types && Array.isArray(channel_types.offline);
};

/**
 * 生成包含多重价格渠道的品牌列表
 * @returns ['sto','yt','dp','dp_rate','_original',...]
 * @param {*} brandList
 */
export const createDivideBrands = (brandList = []) => {
  const divide_arr = [];
  if (brandList && brandList.length > 0) {
    brandList.map((item) => {
      const { channel_types } = item;
      if (checkIsDivideBrand(item)) {
        channel_types.offline.map((k) => {
          divide_arr.push(replaceOriginalKey(k, item));
        });
      }
      divide_arr.push(item.brand);
    });
  }
  return divide_arr;
};

const keyMap = {
  discount: {
    key: 1,
    placeholder: '请设置折扣值',
  },
  fee: {
    key: 2,
    placeholder: '请输入加收费',
  },
};

/**
 * 格式化品牌配置列表
 * @param {object} opts
 * @param {Array} opts.list 品牌配置列表
 * @param {boolean} opts.isMember 这里沿用的老逻辑，但词不达意，个人理解为是否处理特殊折扣渠道，默认不处理
 * @param {boolean} opts.isSetting 是否去掉原价渠道，默认去掉
 */
export const createFormatBrandConfigList = (opts = {}) => {
  const { brands = [], placeholder, isMember, isSetting } = opts || {};
  // 最终格式化品牌列表
  const list = brands.map((item) => {
    return {
      brand: item.brand,
      placeholder,
    };
  });

  // 处理特殊渠道，如德邦8折渠道、原价渠道等，默认存在月结渠道
  // 德邦八折， 接口增加 brand=dp -> [channel_types.offline]
  // 变更为加盟商也显示.............
  if (isMember) {
    if (brands && brands.length > 0) {
      brands.map((oDivideBrand) => {
        const { channel_types } = oDivideBrand || {};
        // 存在多种线下付款渠道的品牌
        if (checkIsDivideBrand(oDivideBrand)) {
          const limitWeightTag =
            oDivideBrand.weightLimitMin > 0 ? `${oDivideBrand.weightLimitMin}KG起` : '';
          const arr = [
            {
              brand: oDivideBrand.brand,
              placeholder,
              sub: '月结',
              value: oDivideBrand.brand,
              limitWeightTag,
            },
          ];
          // 修正渠道名
          const key = `${oDivideBrand.brand}_original`;
          channel_types.offline.forEach((v) => {
            arr.push({
              brand: oDivideBrand.brand,
              placeholder,
              sub: v.label,
              value: replaceOriginalKey(v, oDivideBrand),
              limitWeightTag,
            });
          });
          if (!isSetting) {
            // 去掉原价渠道
            const index = arr.findIndex((i) => i.value.includes('_original'));
            if (index > -1) {
              arr.splice(
                arr.findIndex((i) => i.value === key),
                1,
              );
            }
          }
          //添加到配置列表
          list.splice(
            list.findIndex((v) => v.brand == oDivideBrand.brand),
            1,
            ...arr,
          );
        }
      });
    }
  }

  return list;
};

/**
 * 按照直营或者加盟快递分类，返回品牌费用设置列表或折扣设置列表
 * @param {string} type fee | discount
 * @param {Array} allUserBrands 可用的下单品牌
 *  */
export const formatBrandConfig = ({ allUserBrands = [], type }, isMember, isSetting) => {
  const sort_brands = sortBrands(allUserBrands) || [];
  const oTypeConfig = keyMap[type] || {};
  const { placeholder, key: sortKey } = oTypeConfig;
  const oSortBrands = sort_brands.find((i) => i.key == sortKey);
  const { items = [] } = oSortBrands || {};

  return createFormatBrandConfigList({ brands: items, placeholder, isMember, isSetting });
};

/**
 * 格式化到现付品牌配置列表
 * @param {*} opts
 * @param {Array} opts.allUserBrands 可用的下单品牌
 */
export const formArriveAndCashPayConfig = (opts = {}) => {
  const { allUserBrands = [], isMember } = opts || {};
  // 整理符合到现付品牌列表
  const arriveAndCashPayList = allUserBrands.filter(
    (i) => i.cost_pay_types && (i.cost_pay_types.includes(1) || i.cost_pay_types.includes(2)),
  );
  // 返回格式化后的列表字段
  return createFormatBrandConfigList({ brands: arriveAndCashPayList, isMember });
};

/**
 * 获取团长折扣设置范围
 *  */
export function getDiscountThreshold(params) {
  return new Promise((resolve) => {
    getBrands(true, params)
      .then((allUserBrands) => {
        const obj = {};
        allUserBrands.forEach((val) => {
          obj[val.brand] = {
            ...val,
          };
        });
        resolve(obj);
      })
      .catch(() => {
        resolve(defaultDiscount);
      });
  });
}

/**
 * @description 获取折扣选项
 * @param current 品牌
 * @param {boolean} toSting 是否将数据转换为string类型
 * @param {string} source batch | detail 来源
 *  */
export function getDiscountOptions({
  current,
  toSting = false,
  source = 'detail',
  discount,
  showIncome = false,
}) {
  return new Promise((reslove) => {
    const { rate } = discount[current] || {};
    const calculateIncome = (item) => {
      const num = Math.abs((rate * 1 || 0) - (item * 1 || 0));
      return Math.round(num * 10);
    };
    let options = setDiscount(rate).map((item) => {
      const income = calculateIncome(item);
      return {
        label: `${item}折`,
        value: toSting ? item.toString() : item,
        subTitle: showIncome && {
          className: 'kb-color__brand',
          text: `${income}%利润`,
        },
      };
    });
    options.unshift({
      label: `原价`,
      value: toSting ? '10' : 10,
      subTitle: showIncome && {
        className: 'kb-color__brand',
        text: `${calculateIncome(10)}%利润`,
      },
    });
    if (source == 'batch') {
      options.unshift({
        label: `原始折扣`,
        value: toSting ? '0' : 0,
        subTitle: showIncome && {
          className: 'kb-color__brand',
          text: `${calculateIncome(10)}%利润`,
        },
      });
    }
    reslove(options);
    return options;
  });
}

/**
 * 获取德邦返佣利率
 *  */
export function getDpRate() {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/regimentDpProfitRate',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        const { code, data } = res;
        if (code == 0) {
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
}

export const priceShowConfig = [
  {
    label: '仅展示原价',
    key: 1,
  },
  {
    label: '仅显示成本价',
    key: 2,
  },
  {
    label: '同时展示成本价和原价',
    key: 3,
  },
];

/**
 * 获取下单时，价格展示的策略
 * @returns {string} 1：仅展示原价，2：仅显示成本价，3：同时展示成本价和原价
 *  */
export const getPriceShowConfig = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/getShowQuotationType',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        const { code, data } = res;
        const { quotation_type = 2 } = data || {};
        if (code == 0) {
          resolve(Number(quotation_type));
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};
/**
 * 设置价格展示的策略
 * @returns {string} 1：仅展示原价，2：仅显示成本价，3：同时展示成本价和原价
 *  */
export const setPriceShowConfig = (quotation_type) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Regiment/changeShowQuotationType',
      toastLoading: true,
      toastError: true,
      toastSuccess: true,
      data: {
        quotation_type,
      },
    })
      .then((res) => {
        const { code, data } = res;
        if (code == 0) {
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};
