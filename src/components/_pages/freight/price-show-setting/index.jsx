/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useRef } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbFloatLayout from '@base/components/floatLayout';
import { noop } from '@base/utils/utils';
import KbCheckbox from '@base/components/checkbox';
import './index.scss';
import { getPriceShowConfig, priceShowConfig } from '../utils';

const KbPriceShowSetting = (props) => {
  const { onCancel = noop, onConfirm = noop, isOpened } = props;

  const currentType = useRef('');

  const [value, setValue] = useState('');

  const handleConfirm = () => {
    if (value == currentType.current) {
      onCancel();
    } else {
      onConfirm && onConfirm(value);
    }
  };

  const handleClick = (v) => {
    setValue(v);
  };

  useEffect(() => {
    isOpened &&
      getPriceShowConfig().then((type) => {
        setValue(type);
        currentType.current = type;
      });
  }, [isOpened]);

  return (
    <KbFloatLayout isOpened={isOpened} onClose={onCancel}>
      <View className='kb-float-layout__bars at-row__justify--between kb-margin-xl-lr kb-border-b'>
        <View className='layout-bars__cancel' hoverClass='kb-hover' onClick={onCancel}>
          取消
        </View>
        <View className='layout-bars__confirm' hoverClass='kb-hover' onClick={handleConfirm}>
          确定
        </View>
      </View>
      <View className='kb-float-layout-wrap'>
        <View className='kb-price-show-setting__wrapper'>
          <View className='kb-price-show-setting__wrapper--item kb-price-show-setting__title'>
            个人寄件页价格显示设置
          </View>
          {priceShowConfig.map((v) => (
            <View
              key={v.key}
              className='kb-price-show-setting__wrapper--item'
              hoverClass='kb-hover'
              onClick={handleClick.bind(null, v.key)}
            >
              <View className='at-row at-row__align--center'>
                <View className='at-col kb-text__center'>{v.label}</View>
                <View className='checkbox'>
                  <KbCheckbox onChange={handleClick.bind(null, v.key)} checked={v.key == value} />
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    </KbFloatLayout>
  );
};

KbPriceShowSetting.options = {
  addGlobalClass: true,
};

export default KbPriceShowSetting;
