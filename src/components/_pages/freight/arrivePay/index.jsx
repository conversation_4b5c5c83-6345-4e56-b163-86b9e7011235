/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { RichText, ScrollView, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import { AtAvatar } from 'taro-ui';
import './index.scss';

const KbArrivePayBrandsList = (props) => {
  const { list = [], discount = {} } = props;
  const { brands } = useSelector((state) => state.global);
  return (
    <ScrollView className='kb-arrivePayBrandsList kb-scrollview' scrollY>
      <View className='kb-fright kb-margin-md'>
        <View className='kb-fright__card kb-background__white'>
          {list.map((item) => {
            const {
              offline = {},
              channel_rate: { offline: dp_offline } = {},
              type,
            } = discount[item.brand] || {};
            const { name, logo_link } = brands[item.brand] || {};
            return (
              <View key={item.brand} className='kb-fright__card--item kb-spacing-lg'>
                <View className='at-row at-row__align--center'>
                  <AtAvatar className='kb-avatar__middle kb-margin-md-r' circle image={logo_link} />
                  <View className='flex-1'>
                    <View className='title'>
                      {name}
                      {type === 'big_package' && (
                        <View className='title-tag'>经济货运30kg起重</View>
                      )}
                    </View>
                    <View className='desc'>
                      {dp_offline
                        ? dp_offline[item.value].role_desc && (
                            <RichText nodes={dp_offline[item.value].role_desc} />
                          )
                        : offline.role_desc && <RichText nodes={offline.role_desc} />}
                    </View>
                  </View>
                  {/* 到付成本价查询特殊处理 */}
                  <View className='kb-size__base kb-margin-md-l'>
                    {item.value == item.brand
                      ? item.brand === 'ky'
                        ? '8折'
                        : '原价'
                      : item.sub
                      ? item.sub
                      : offline.desc
                      ? offline.desc
                      : '原价'}
                  </View>
                </View>
              </View>
            );
          })}
        </View>
      </View>
    </ScrollView>
  );
};

KbArrivePayBrandsList.options = {
  addGlobalClass: true,
};

KbArrivePayBrandsList.defaultProps = {
  list: [],
};

export default KbArrivePayBrandsList;
