/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, Text, View } from '@tarojs/components';
import { AtAvatar, AtIcon } from 'taro-ui';
import { useSelector } from '@tarojs/redux';
import { noopConnect } from '@base/utils/utils';
import './index.scss';

const KbMonthPayBrandsList = (props) => {
  const {
    leaderList = [],
    discount = {},
    subBrands = [],
    dhList = [],
    handleShowSetting = noopConnect,
  } = props;
  const { brands } = useSelector((state) => state.global);
  const _subBrands = JSON.parse(JSON.stringify(subBrands));
  const cnggBrand = discount.cngg;

  if (cnggBrand && cnggBrand.product_types && cnggBrand.product_types.online) {
    const arr = [];
    cnggBrand.product_types.online.forEach((item) => {
      arr.push({
        brand: 'cngg',
        label: item.label,
      });
    });
    _subBrands.splice(
      _subBrands.findIndex((v) => v.brand == 'cngg'),
      1,
      ...arr,
    );
  }
  const list = [
    {
      key: 'efficiency',
      title: '品质时效',
      icon: 'efficiency',
      color: 'red',
      subList: leaderList,
      brandTitle: '直营快递',
    },
    {
      key: 'affordable',
      title: '经济实惠',
      icon: 'affordable',
      color: 'brand',
      subList: _subBrands,
      brandTitle: '加盟快递',
    },
    {
      key: 'big_package',
      title: '经济货运',
      icon: 'big_package',
      color: 'orange',
      subList: dhList,
      brandTitle: '30kg+大货',
    },
  ];

  return (
    <ScrollView className='kb-monthPayBrandsList kb-scrollview' scrollY>
      <View className='kb-fright kb-margin-md-lr'>
        {list.map((v) => {
          const { title, icon, color, subList, brandTitle } = v || {};
          return (
            <View className=' kb-margin-lg-b' key={title}>
              <View className='kb-fright-title'>
                <View>{brandTitle}</View>
                {icon == 'efficiency' && (
                  <AtIcon
                    onClick={handleShowSetting}
                    hoverClass='kb-hover-opacity'
                    size={15}
                    prefixClass='kb-icon'
                    value='set'
                  />
                )}
              </View>
              <View className='kb-background__white kb-fright__card'>
                <View className='kb-spacing-lg-lr kb-spacing-xl-tb'>
                  <View className='at-row at-row__align--center'>
                    <AtIcon
                      prefixClass='kb-icon'
                      className={`kb-icon-size__lg kb-color__${color}`}
                      value={icon}
                    />
                    <View className='kb-margin-md-l kb-size__bold'>{title}</View>
                  </View>
                </View>
                {subList.map((item) => {
                  const { online = {}, type } = discount[item.brand] || {};
                  const { name, logo_link } = brands[item.brand] || {};
                  const hide =
                    (item.value && item.value.includes('_rate')) ||
                    (v.key !== 'big_package' && type === 'big_package');
                  return hide ? null : (
                    <View
                      className='kb-fright__card--item kb-spacing-md-tb kb-spacing-lg-lr'
                      key={item.brand}
                    >
                      <View className='at-row at-row__align--center at-row__justify--between'>
                        <View className='kb-flex'>
                          <AtAvatar
                            className='kb-margin-md-r'
                            circle
                            size='small'
                            image={logo_link}
                          />
                          <View className='title'>
                            {name}
                            {item.label ? `(${item.label})` : ''}
                            {type === 'big_package' && (
                              <View className='title-tag'>经济货运30kg起重</View>
                            )}
                          </View>
                        </View>
                        <View>
                          <Text className='kb-size__md'>{v.icon == 'efficiency' ? '≈' : ''}</Text>
                          <Text className='kb-size__x'>{online.desc || '--'}</Text>
                        </View>
                      </View>
                    </View>
                  );
                })}
              </View>
            </View>
          );
        })}
        <View className='kb-size__sm kb-color__grey'>
          1、成本价格指品牌快件的运费，保价费及包装费等其它费用需要另行收费；
        </View>
        <View className='kb-spacing-md-b kb-size__sm kb-color__grey'>
          2、直营快递的折扣是根据原价基础上预估测算，供参考使用，具体以查询的报价单为准；加盟公司的线路价格以价格明细为准；
        </View>
      </View>
    </ScrollView>
  );
};

KbMonthPayBrandsList.options = {
  addGlobalClass: true,
};

export default KbMonthPayBrandsList;
