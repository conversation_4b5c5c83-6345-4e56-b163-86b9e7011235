/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import icon_upgrade from '@/assets/icon/icon_upgrade.png';
import { getCurrentUser, noop } from '@base/utils/utils';
import { useEffect, useState } from '@tarojs/taro';
import './index.scss';
import { getCurrentUserLevel, upgradeProxy } from '../utils';
import { getUserTittleAndTagImg } from '../../index/_utils';

const Index = (props) => {
  const { onUpgrade = noop } = props;

  const [tag, setTag] = useState('');
  const [img, setImg] = useState('');

  const handleUpgrade = () => {
    upgradeProxy().then(() => {
      onUpgrade();
    });
  };

  const getTag = async () => {
    const level = getCurrentUserLevel() + 1;
    const { tag: _tag, img } = (await getUserTittleAndTagImg({ level })) || {};
    setTag(_tag);
    setImg(img);
  };

  useEffect(() => {
    getTag();
  }, []);

  return (
    <View className='kb-upgrade-proxy kb-margin-lg'>
      {getCurrentUser('league') ? (
        <View className='at-row at-row__align--center'>
          <Image
            className='kb-upgrade-proxy__img--first kb-margin-md-r'
            mode='widthFix'
            src={img}
          />
          <Text className='kb-size__md'>享4项特权！</Text>
        </View>
      ) : (
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='kb-size__md'>
            <Image
              className='kb-upgrade-proxy__img kb-margin-md-r'
              mode='widthFix'
              src={icon_upgrade}
            />
            <Text className='kb-size__bold kb-color__brown'>{`升级${tag}`}</Text>
            <Text> | 解锁4项特权！</Text>
          </View>
          <View
            onClick={handleUpgrade}
            hoverClass='kb-hover-opacity'
            className='kb-upgrade-proxy__btn'
          >
            立即升级
            <Image className='kb-upgrade-proxy__btn--tag' src={img} mode='widthFix' />
          </View>
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
