/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { getCurrentUser } from '@base/utils/utils';
import KbPageAvatar from '../../index/avatar';
import { getUserTittleAndTagImg } from '../../index/_utils';
import { useCommissionTeamData } from '../utils';
import './index.scss';

const Index = (props) => {
  const [tag, setTag] = useState('');

  const { userInfo: { nickname } = {} } = Taro.kbLoginData || {};
  const { data = {} } = props;
  const { inviter = '' } = data;
  const { datalist: infoList = [] } = useCommissionTeamData();

  const getTag = async () => {
    const { tag: _tag } = (await getUserTittleAndTagImg()) || {};
    setTag(_tag);
  };

  useEffect(() => {
    getTag();
  }, []);

  const handleClick = () => {
    Taro.navigator({
      url: 'commission/list',
    });
  };

  const handleJump = ({ navigator }) => {
    if (navigator) {
      Taro.navigator(navigator);
    }
  };

  return (
    <View className='kb-background__white kb-proxy-info'>
      <View className='at-row'>
        <KbPageAvatar />
        <View className='at-row at-row__justify--between kb-margin-md-l'>
          <View>
            <View className='kb-proxy-info__name kb-margin-xs-b'>{nickname}</View>
            <View className='kb-size__xs kb-color__grey kb-margin-sm-b'>专享{tag}折扣</View>
            <View className='kb-size__sm'>
              推荐人：{getCurrentUser('partner') ? '微快递特惠寄' : inviter}
            </View>
          </View>
        </View>
      </View>
      <View className='kb-proxy-info__title'>
        <View>团队数据</View>
        <View
          className='kb-size__sm kb-color__grey'
          hoverClass='kb-hover-opacity'
          onClick={handleClick}
        >
          团队列表
          <AtIcon prefixClass='kb-icon' value='arrow' className='kb-icon-size__xs kb-color__grey' />
        </View>
      </View>
      <View className='kb-proxy-info__statistic kb-margin-lg-t at-row at-row__align--center'>
        {infoList.map((v) => {
          return (
            <View
              key={v.key}
              className='kb-proxy-info__statistic-item kb-text__center at-col at-col-4'
              onClick={() => handleJump(v)}
            >
              <View className='kb-margin-sm-b kb-size__xl kb-size__bold'>
                {v.unit && <Text className='kb-size__xs'>{v.unit}</Text>}
                {data[v.key] || '0'}
              </View>
              <View className='kb-size__sm'>{v.label}</View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
