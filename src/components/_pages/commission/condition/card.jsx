/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtAvatar, AtIcon, AtProgress } from 'taro-ui';
import { useSelector } from '@tarojs/redux';
import { dateCalendar, defaultAvatarUrl } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import './card.scss';
import { getCardList } from '../utils';

const Index = (props) => {
  const [list, setList] = useState([]);
  const { loginData = {} } = useSelector((state) => state.global);
  const { logined } = loginData;
  const {
    total = 0,
    current = 0,
    topTag,
    title,
    conditionText,
    color,
    checkMember,
    url,
    list: propsList,
  } = props;

  const percent = total * current ? (Number(current) / total) * 100 : 0;

  const handleClick = () => {
    Taro.navigator({
      url: 'commission/list',
    });
  };

  useEffect(() => {
    if (logined) {
      if (url) {
        getCardList(url).then((res) => {
          if (isArray(res)) {
            setList(res);
          }
        });
      } else if (propsList && propsList.length > 0) {
        setList(propsList);
      }
    }
  }, [logined, url, propsList]);

  return (
    <View className='kb-duration-card'>
      {topTag && <View className='kb-duration-card__topTag'>{topTag}</View>}
      <View className='at-row at-row__align--center at-row__justify--between kb-margin-lg-b'>
        <View className='flex'>
          <Text className='kb-size__lg kb-size__bold'>{title}</Text>
          <Text className='kb-size__sm'>
            （已完成 {current}/{total}）
          </Text>
        </View>
        {checkMember && (
          <View
            className='kb-size__sm kb-color__grey'
            hoverClass='kb-hover-opacity'
            onClick={handleClick}
          >
            查看全部
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon-size__xs kb-color__grey'
            />
          </View>
        )}
      </View>
      <View
        className={`kb-duration-card__progress kb-margin-md-b kb-duration-card__progress--${color}`}
      >
        <AtProgress percent={percent} strokeWidth={8} isHidePercent />
      </View>
      <View className='kb-size__sm kb-color__grey kb-margin-lg-b'>{conditionText}</View>
      <View className='kb-duration-card__list'>
        {list &&
          list.length > 0 &&
          list.map((v) => {
            return (
              <View key={v.id} className='at-row at-row__justify--between at-row__align--center'>
                <View className='kb-size__sm flex'>
                  <AtAvatar
                    className='kb-avatar__base kb-margin-md-r'
                    circle
                    size='small'
                    image={v.avatar || defaultAvatarUrl}
                  />
                  <Text className='kb-margin-sm-r'>{v.nickname || '--'}</Text>
                  <Text>{v.mobile}</Text>
                </View>
                <View className='kb-size__sm'>{dateCalendar(v.time)}</View>
              </View>
            );
          })}
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
