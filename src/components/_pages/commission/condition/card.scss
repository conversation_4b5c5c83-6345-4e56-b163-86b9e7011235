/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-duration-card {
  position: relative;
  margin-top: 40px;
  padding: 40px $spacing-h-lg;
  background-color: #f7f8fa;
  border-radius: $border-radius-xxl;

  &__topTag {
    position: absolute;
    top: -40px;
    left: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 88px;
    height: 40px;
    color: #fff4eb;
    font-size: 22px;
    background: linear-gradient(90deg, #56555b 0%, #18171f 100%);
    border-radius: 20px 2px 20px 2px;
    transform: translateY(50%);
  }

  .flex {
    display: flex;
    align-items: center;
  }
}

.kb-duration {
  &__invite {
    position: relative;
    display: flex;
    align-items: center;
    Image {
      width: 40px;
      height: 40px;
    }
    .at-button {
      position: absolute;
      top: 0;
      z-index: 5;
      width: 100%;
      height: 100%;
      background: none;
      border: none;
      opacity: 0;
    }
  }
  &__progress {
    position: relative;
    margin-right: 40px;
    padding-top: $spacing-h-lg;
    &-after {
      position: absolute;
      right: -50px;
      bottom: 4px;
      font-size: $font-size-xs;
    }
    &-inside {
      position: absolute;
      bottom: 4px;
      left: 10px;
      z-index: 10;
      font-size: $font-size-xs;
    }
    &-flag {
      position: absolute;
      top: -30px;
      left: 28%;
      z-index: 10;
      padding: $spacing-h-xs $spacing-h-sm;
      color: $color-brand;
      font-size: $font-size-xs;
      background-color: $color-brand-lightest;
      border-radius: $border-radius-md;
      &::before {
        position: absolute;
        bottom: -10px;
        left: 15px;
        border: 15px solid $color-brand-lightest;
        border-right-color: transparent;
        border-bottom: 0;
        border-left-color: transparent;
        content: '';
      }
    }
  }
}
.kb-border-radius__xxl {
  border-radius: $border-radius-xxl;
}
.kb-size__md {
  font-size: 28px;
}
