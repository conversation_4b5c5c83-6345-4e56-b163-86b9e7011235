/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import { getCurrentUser } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import { AtIcon } from 'taro-ui';
import KbProgressCard from './card';
import { getMemberUpdateDetail } from '../utils';
import './index.scss';

const Index = (props) => {
  const { data = {} } = props;
  const { order_count = 0, upgrade_regiment_count = 0, invite_count, user_count = 0 } = data;

  const [memberUpdateDetail, setMemberUpdateDetail] = useState({});

  const handleClick = () => {
    Taro.navigator({
      url: 'commission/list',
    });
  };

  useUpdate(({ logined } = {}) => {
    if (!logined) return;
    if (getCurrentUser('member')) {
      getMemberUpdateDetail().then((res) => {
        setMemberUpdateDetail(res);
      });
    }
  });

  const calculatePercent = (current, total) => {
    return (Number(current) / total) * 100;
  };

  const totalProgress =
    (calculatePercent(order_count, 500) + calculatePercent(upgrade_regiment_count, 20)) / 2;

  const {
    invite_user_count = 0,
    invite_user_list = [],
    user_order_count = 0,
    order_user_list = [],
  } = memberUpdateDetail || {};

  const is_custom_z = getCurrentUser('custom-z');
  // 分销版
  const is_custom_f = getCurrentUser('custom-f');

  return (
    <View className='kb-spacing-lg kb-background__white kb-condition'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        <View className='kb-condition__img'>
          <Image
            className=''
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/img_update.png?v=2'
          />
        </View>
        {getCurrentUser('member') ? (
          <View
            className='kb-size__sm kb-color__grey'
            hoverClass='kb-hover-opacity'
            onClick={handleClick}
          >
            查看会员
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon-size__xs kb-color__grey'
            />
          </View>
        ) : (
          <View className='kb-size__sm'>
            <AtIcon
              prefixClass='kb-icon'
              value='flash'
              className='kb-icon-size__xs kb-color__orange'
            />
            进度总完成 {totalProgress.toFixed(0)}%
          </View>
        )}
      </View>
      {getCurrentUser('member') && (
        <View>
          <KbProgressCard
            total={is_custom_z || is_custom_f ? 30 : 10}
            current={invite_user_count}
            list={invite_user_list}
            color='orange'
            topTag='条件一'
            title='邀请有效用户'
            conditionText={
              is_custom_f
                ? '直邀30位新用户，完成特惠寄小程序注册，并加入团队'
                : is_custom_z
                ? '直邀人数累计达到30人'
                : '邀请10位新用户'
            }
          />
          <KbProgressCard
            total={is_custom_f ? 5 : is_custom_z ? 2 : 10}
            current={user_order_count}
            list={order_user_list}
            color='orange'
            topTag='条件二'
            title='有效订单数'
            conditionText={
              is_custom_f
                ? '直邀5人下单（快递签收、生活服务交易完结单）'
                : `月结下单总单量达到${is_custom_z ? 2 : 10}单`
            }
          />
        </View>
      )}
      {getCurrentUser('regiment') && (
        <View>
          <KbProgressCard
            url='/api/Order/latestValidOrderUserList'
            total={is_custom_z || is_custom_f ? 100 : 500}
            current={order_count}
            color='orange'
            topTag='条件一'
            title='团队总订单数'
            conditionText={
              is_custom_f
                ? '团队总订单数累计达到100单(快递签收、生活服务交易完结单)'
                : `团队内月结下单总单量达到${is_custom_z ? 100 : 500}单`
            }
          />
          <KbProgressCard
            url='/api/CustomInviteStatistic/myMemberUpgradeRegimentList'
            total={is_custom_f ? 10 : is_custom_z ? 200 : 20}
            current={is_custom_z ? invite_count : upgrade_regiment_count}
            topTag='条件二'
            title={is_custom_f ? '铂金推荐官数量' : is_custom_z ? '团队直邀人数' : '二级代理数量'}
            conditionText={
              is_custom_f
                ? '团队内10个直邀高级寄件会员升级为铂金推荐官'
                : is_custom_z
                ? '团队直邀人数累计达到200人'
                : '团队内20个团员升级为二级代理'
            }
            checkMember
          />
          {is_custom_f && (
            <KbProgressCard
              url='/api/CustomInviteStatistic/latestTeamUserList'
              total={300}
              current={user_count}
              topTag='条件三'
              title='团队人员总数'
              conditionText='团队人员总数累计达到300人'
              checkMember
            />
          )}
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
