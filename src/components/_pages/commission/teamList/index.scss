.kb-team-list {
  border-radius: $border-radius-xxl;
  &__item {
    display: flex;
    align-items: flex-start;
    padding-top: $spacing-h-lg;
    &--avatar {
      position: relative;
      &__img {
        display: block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border-radius: $border-radius-circle;
      }

      &__tag {
        position: absolute;
        right: 0;
        bottom: 0;
        &--sm {
          width: 28px;
          height: 28px;
        }
        &--md {
          width: 43px;
          height: 28px;
        }
      }
    }
    &--content {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      padding-bottom: $spacing-h-lg;
      border-bottom: $border-lighter;
    }

    &:last-child {
      .kb-team-list__item--content {
        border-bottom: none;
      }
    }
  }
}
