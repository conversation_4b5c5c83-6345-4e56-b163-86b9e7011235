/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import icon_regiment from '@/assets/icon/icon_leader.png';
import { dateCalendar, defaultAvatarUrl } from '@base/utils/utils';
import { getTeamList } from '../utils';
import './index.scss';

const Index = () => {
  const handleClick = () => {
    Taro.navigator({
      url: 'commission/list',
    });
  };

  const [list, setList] = useState([]);

  useEffect(() => {
    getTeamList().then((res) => {
      setList(res);
      console.log(res);
    });
  }, []);

  return (
    <View className='kb-margin-lg kb-spacing-lg kb-team-list kb-background__white'>
      <View className='kb-margin-md-b at-row at-row__align--center at-row__justify--between'>
        <View className='kb-size__lg'>团队列表</View>
        <View
          className='kb-size__sm kb-color__grey'
          onClick={handleClick}
          hoverClass='kb-hover-opacity'
        >
          查看全部
          <AtIcon prefixClass='kb-icon' value='arrow' className='kb-icon-size__xs kb-color__grey' />
        </View>
      </View>
      {list.map((item) => {
        return (
          <View key={item.user_id} className='kb-team-list__item' hoverClass='kb-hover-opacity'>
            <View className='kb-team-list__item--avatar'>
              <Image
                className='kb-team-list__item--avatar__img'
                src={item.avatar || defaultAvatarUrl}
              />
              <Image
                className='kb-team-list__item--avatar__tag kb-team-list__item--avatar__tag--sm'
                src={icon_regiment}
                mode='widthFix'
              />
            </View>
            <View className='kb-team-list__item--content'>
              <View className='kb-margin-md-l'>
                <View className='kb-size__md kb-margin-sm-b'>{item.nickname}</View>
                <View className='kb-size__sm kb-color__grey'>{item.mobile || '暂无手机号'}</View>
              </View>
              <View className='kb-size__sm kb-color__grey'>{dateCalendar(item.register_at)}</View>
            </View>
          </View>
        );
      })}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
