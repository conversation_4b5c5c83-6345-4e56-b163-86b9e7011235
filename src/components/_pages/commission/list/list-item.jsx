/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import KbText from '@base/components/text';
import { defaultAvatarUrl, getCurrentUser, noop, importFieldHide } from '@base/utils/utils';
import { AtAvatar, AtIcon } from 'taro-ui';
import './list-item.scss';

const Index = (props) => {
  const { data, current, onClick } = props;

  const {
    avatar,
    nickname,
    order_count,
    order_profit,
    register_at,
    user_id,
    mobile,
    user_count,
    is_detach = false,
  } = data || {};

  return (
    <View hoverClass='kb-hover' onClick={onClick}>
      <View className='kb-list__item'>
        <View>
          <AtAvatar
            className='kb-avatar__middle kb-margin-md-r'
            circle
            image={avatar || defaultAvatarUrl}
          />
        </View>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='at-col'>
            <View className='at-row at-row__align--center'>
              <View className='kb-size__lg kb-margin-sm-r'>{nickname}</View>
              <KbText className='kb-size__sm kb-color__greyer' copy={{ text: user_id }}>
                ID:{user_id}
              </KbText>
            </View>
            {mobile ? (
              <View className='kb-size__sm kb-color__greyer'>
                授权手机号: {importFieldHide(mobile, 3, 7)}
              </View>
            ) : null}
            <View className='kb-list__item--stats'>
              <View className='kb-list__item--statsItem'>
                <View className='label'>订单数</View>
                <View className='value'>{order_count}</View>
              </View>
              {!getCurrentUser('member') ? (
                <View className='kb-list__item--statsItem'>
                  <View className='label'>产出总收益</View>
                  <View className='value'>{order_profit}</View>
                </View>
              ) : null}
              {current != 0 && (
                <View className='kb-list__item--statsItem'>
                  <View className='label'>团队总人数</View>
                  <View className='value'>{user_count}</View>
                </View>
              )}
            </View>
            <View className='kb-size__sm kb-color__greyer'>注册时间: {register_at}</View>
          </View>
          <View>
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon-size__sm kb-color__grey'
            />
          </View>
        </View>
      </View>
      {getCurrentUser('custom-f') && is_detach && (
        <View className='kb-list__item--detach'>
          <AtIcon prefixClass='kb-icon' value='zhuyi' className='kb-icon-size__base' />
          <View className='kb-margin-sm-l'>
            当前用户已升级【钻石推荐官】并脱离团队，脱离后订单数、收益将不再统计至本团队！
          </View>
        </View>
      )}
    </View>
  );
};

Index.defaultProps = {
  data: {},
  onClick: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
