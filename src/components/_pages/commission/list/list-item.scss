/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-list__item {
  display: flex;
  align-items: flex-start;
  padding: $spacing-h-lg;
  &--stats {
    display: flex;
    margin: $spacing-h-lg 0;
  }
  &--statsItem {
    flex: 1;
    .label {
      margin-bottom: $spacing-h-sm;
      color: #646566;
      font-size: 24px;
    }
    .value {
      color: #646566;
    }
  }
  &--detach {
    display: flex;
    align-items: center;
    padding: 10px $spacing-h-md;
    color: #fff;
    font-size: 22px;
    background: #e18380;
  }
  .kb-size__lg {
    line-height: 48px;
  }
  .kb-size__base {
    line-height: 42px;
  }
  .kb-color__greyer {
    color: #646566 !important;
  }
}
