/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo, useRef, useState } from '@tarojs/taro';
import KbLongList from '@base/components/long-list';
import { View } from '@tarojs/components';
import { getCurrentUser, noop } from '@base/utils/utils';
import { extendMemo } from '@base/components/_utils';
import isArray from 'lodash/isArray';
import KbOrderListItem from './list-item';
import './list-content.scss';

const Index = (props) => {
  const {
    active,
    searchData: search,
    sortData,
    tabKey,
    tabTotal = 0,
    current,
    onSwitchTab = noop,
    onUpdateSearchLoop = noop,
    setTotal = noop,
  } = props;
  const actionRef = useRef({});
  const [list, updateList] = useState(null);

  const listData = {
    pageKey: 'page',
    api: {
      url: '/api/CustomInviteStatistic/userInviteLevelList',
      data: { level: tabKey },
      formatRequest: (data) => {
        return {
          ...data,
        };
      },
      formatResponse: (res) => {
        const { data } = res;
        const { total, list } = data || {};
        const hasList = isArray(list) && list.length > 0;
        if (hasList) {
          return {
            data: {
              list,
            },
            total,
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (data, res, req) => {
        updateList(data);
        if (req.page == 1) {
          setTotal(res.total, true);
        } else if (!req.order_by) {
          setTotal(res.total || 0);
        }
        if (req.page == 1) {
          // 根据需求(https://tower.im/teams/258300/todos/110835/)增加循环搜索逻辑
          if (req.search) {
            if (data.length <= 0) {
              const searchLoop = onUpdateSearchLoop('get');
              let _current = req.level || 0;
              if (searchLoop > tabTotal) {
                onUpdateSearchLoop('set', 0);
                return;
              }
              const nextSearchLoop = searchLoop + 1;
              onUpdateSearchLoop('set', nextSearchLoop);
              const n = _current >= tabTotal - 1 ? 0 : _current + 1;
              onSwitchTab(n, 'search');
            } else {
              onUpdateSearchLoop('set', 0);
            }
          }
        }
      },
    },
  };

  const handleClickItem = (item) => {
    const { user_id, is_detach } = item;
    // 好友详情详情
    Taro.navigator({
      url: 'commission/detail',
      options: {
        user_id,
        is_detach: is_detach ? 1 : 0,
      },
    });
  };

  // 列表准备就绪
  const handleReady = (ins) => {
    actionRef.current.listIns = ins;
    props.onReady(ins);
  };

  const isCustom = getCurrentUser('custom');

  const searchData = useMemo(() => {
    const params = {
      ...search,
    };
    if (sortData) {
      const key = Object.keys(sortData)[0];
      const order_by = `${key}_${sortData[key]}`;
      params.order_by = order_by;
    }
    return params;
  }, [search, sortData]);

  return (
    <KbLongList
      active={active && searchData}
      data={listData}
      enableMore
      onReady={handleReady}
      noDataText={isCustom ? '暂无团队数据' : '暂无好友数据'}
    >
      {list && (
        <View className='kb-list'>
          {list.map((item) => {
            const { user_id } = item;
            return (
              <View className='kb-list__item--wrapper' key={user_id}>
                <KbOrderListItem
                  data={item}
                  current={current}
                  onClick={handleClickItem.bind(null, item)}
                />
              </View>
            );
          })}
        </View>
      )}
    </KbLongList>
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  searchData: null,
  active: false,
  onReady: noop,
};

export default extendMemo(Index);
