/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useRef, useState } from '@tarojs/taro';
import { AtTabs, AtTabsPane } from 'taro-ui';
import { getCurrentUser, noop } from '@base/utils/utils';
import { useUpdate } from '~base/hooks/page';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import KbListContent from './list-content';
import { getCustomName } from '../utils';
import './index.scss';
import ListFilter from './list-filter';

const Index = (props) => {
  const { onSwitchTab, onReady, isAdmin, tabKey, onFilter, active, ...restProps } = props;

  const [current, updateCurrent] = useState(0);
  const [tabList, setTabList] = useState([]);
  const actionRef = useRef({ searchLoop: 0 });
  const [total, setTotal] = useState(0);
  const [sortData, setSortData] = useState(null);

  // 切换tab
  const handleSwitchTab = (current, actionSource = '') => {
    updateCurrent(current);
    setSortData(null);
    // 因切换tab时，参数不变，列表组件不重新请求，故做重置计数处理
    if (actionSource === 'search') {
      const searchLoop = handleUpdateSearchLoop('get');
      if (searchLoop >= tabList.length) {
        handleUpdateSearchLoop('set', 0);
      }
    } else {
      setTotal(0);
    }
  };

  // list组件准备就绪
  const handleReady = (key, ins) => {
    onReady(ins, key);
  };

  useUpdate(() => {
    getCustomName().then((res) => {
      let resList = res || [];
      const isFenXiao = getCurrentUser('custom-f');
      if (isFenXiao) {
        if (getCurrentUser('regiment') && !getCurrentUser('league')) {
          resList = resList.filter((i) => i.key == 0 || i.key == 1);
        } else if (getCurrentUser('member') && !getCurrentUser('regiment')) {
          resList = resList.filter((i) => i.key == 0);
        }
      }
      setTabList(resList);
    });
  }, []);

  const handleUpdateSearchLoop = (key = 'get', val) => {
    if (key === 'set') {
      actionRef.current.searchLoop = val;
    }
    if (key === 'get') {
      return actionRef.current.searchLoop;
    }
  };

  const handleFilter = (v) => {
    setSortData(v);
  };

  const tabCls = classNames('kb-tabs kb-tabs__short kb-tabs__short--commission', {
    'kb-tabs__hidetab': tabList.length <= 1,
  });
  return (
    <AtTabs
      tabList={tabList}
      onClick={handleSwitchTab}
      current={current}
      animated={false}
      swipeable={false}
      className={tabCls}
    >
      {tabList.length > 0 &&
        tabList.map((item, index) => {
          return (
            <AtTabsPane key={item.key} current={current} index={index}>
              <View className='at-row at-row__direction--column kb-overflow-hidden'>
                <ListFilter
                  data={sortData}
                  current={current}
                  total={total}
                  onFilter={handleFilter}
                />
                <View className='flex1'>
                  <KbListContent
                    {...restProps}
                    active={current === index && active}
                    sortData={sortData}
                    onReady={handleReady.bind(null, item.key)}
                    tabKey={item.key}
                    current={current}
                    isAdmin={isAdmin}
                    show={current === index}
                    tabTotal={tabList.length}
                    onUpdateSearchLoop={handleUpdateSearchLoop}
                    onSwitchTab={handleSwitchTab}
                    setTotal={(v, isRefresh) => setTotal((pre) => (isRefresh ? v : pre + v))}
                  />
                </View>
              </View>
            </AtTabsPane>
          );
        })}
    </AtTabs>
  );
};

export default Index;

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  onSwitchTab: noop,
  onReady: noop,
};
