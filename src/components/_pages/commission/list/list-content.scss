/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-list {
  margin: $spacing-h-lg;
  padding: 0;
  overflow: hidden;
  border-radius: $border-radius-xxl;
  &__item {
    &--wrapper {
      margin-bottom: $spacing-h-md;
    }
  }
  &__filter {
    padding: 28px;
    background-color: $color-white;
    &-list {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding-right: $spacing-h-lg;
    }
    &-item {
      position: relative;
      padding-right: 24px;
      &-triangle {
        position: absolute;
        top: 8px;
        right: 0;
        width: 0;
        height: 0;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #8b8c8e;
        border-left: 8px solid transparent;
        &--active {
          border-bottom-color: $color-brand;
        }
        &--bottom {
          top: unset;
          bottom: 8px;
          transform: rotate(180deg);
        }
      }
    }
  }
}
