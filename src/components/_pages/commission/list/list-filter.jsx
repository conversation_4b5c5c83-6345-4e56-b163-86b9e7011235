/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useMemo } from '@tarojs/taro';
import classNames from 'classnames';
import './list-content.scss';

const ListFilter = (props) => {
  const { data, current, total, onFilter } = props;
  const items = useMemo(() => {
    return [
      {
        key: 'register',
        label: '注册时间',
      },
      {
        key: 'order',
        label: '订单数',
      },
      {
        key: 'profit',
        label: '产出总收益',
      },
      {
        key: 'user',
        label: '团队总人数',
        hide: current == 0,
      },
    ].filter((item) => !item.hide);
  }, [current]);

  const handleClick = (key) => {
    onFilter({
      [key]: data && data[key] == 'asc' ? 'desc' : 'asc',
    });
  };

  return (
    <View className='kb-list__filter at-row at-row__align--center kb-size__base'>
      <View>总数 {total}</View>
      <View className='at-col'>
        <View className='kb-list__filter-list kb-size__base'>
          {items.map((item) => {
            return (
              <View
                className='kb-list__filter-item'
                key={item.key}
                hoverClass='kb-hover-opacity'
                onClick={() => handleClick(item.key)}
              >
                <View className='kb-color__grey'>{item.label}</View>
                <View className='kb-list__filter-item-value'>
                  <View
                    className={classNames('kb-list__filter-item-triangle', {
                      'kb-list__filter-item-triangle--active': data && data[item.key] == 'asc',
                    })}
                  />
                  <View
                    className={classNames(
                      'kb-list__filter-item-triangle kb-list__filter-item-triangle--bottom',
                      {
                        'kb-list__filter-item-triangle--active': data && data[item.key] == 'desc',
                      },
                    )}
                  />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
};

ListFilter.options = {
  addGlobalClass: true,
};

export default ListFilter;
