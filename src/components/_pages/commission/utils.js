/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import request from '@base/utils/request';
import { removeStorageSync, getCurrentUser } from '@base/utils/utils';
import { loginStorageKey } from '@/utils/config';
import { CUSTOM_NAMES } from '@/constants/business';
import isArray from 'lodash/isArray';
import { useSelector } from '@tarojs/redux';

export const upgradeProxy = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/User/userUpgrade',
      toastLoading: true,
      toastSuccess: true,
      toastError: true,
      onThen: (res) => {
        const { code } = res;
        if (code == 0) {
          removeStorageSync(loginStorageKey);
          Taro.kbLogin().then(resolve);
        }
      },
    });
  });
};

/**
 * 获取好友详情
 *  */
export const getUserDetail = (user_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/inviteUserDetail',
      toastLoading: true,
      toastSuccess: false,
      toastError: false,
      data: {
        invite_user: user_id,
      },
      onThen: (res) => {
        const { code, data = {} } = res;
        if (code == 0) {
          resolve(data);
        }
      },
    });
  });
};

/**
 * 分佣管理，获取当前用户的详情
 *  */
export const getCurrentDetail = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/myselfLevelDetail',
      toastLoading: true,
      toastError: true,
      toastSuccess: false,
      onThen: (res) => {
        const { code, data = {} } = res;
        if (code == 0) {
          resolve(data);
        }
      },
    });
  });
};

/**
 * 分佣管理，获取升级条件列表
 *  */
export const getCardList = (url) => {
  return new Promise((resolve) => {
    request({
      url,
      toastLoading: true,
      toastError: true,
      toastSuccess: false,
      onThen: (res) => {
        const { code, data = {} } = res;
        if (code == 0) {
          resolve(data);
        }
      },
    });
  });
};

export const getCurrentUserLevel = () => {
  const isLeague = getCurrentUser('league');
  const isPartner = getCurrentUser('partner');
  if (getCurrentUser('member')) {
    return 0;
  } else if (getCurrentUser('regiment') && !isLeague && !isPartner) {
    return 1;
  } else if (isLeague && !isPartner) {
    return 2;
  } else if (isPartner) {
    return 3;
  }
};

// 默认的层级名称
export const default_custom_names = [
  {
    key: 0,
    title: '高级寄件会员',
  },
  {
    key: 1,
    title: '铂金推荐官',
    tips: '在您升级铂金推荐官后，专属折扣信息和数据分析会在这里显示',
  },
  {
    key: 2,
    title: '钻石推荐官',
    tips: '在您升级钻石推荐官后，专属折扣信息和数据分析会在这里显示',
  },
];

/**
 * 代理商模式，获取层级名称
 */
export function getCustomName() {
  return new Promise((resolve) => {
    const cacheData = Taro.kbGetGlobalData(CUSTOM_NAMES);
    if (cacheData) {
      resolve(cacheData);
      return;
    }
    request({
      url: '/api/RegimentUser/customLevelName',
      toastError: false,
      toastSuccess: false,
      toastLoading: false,
      onThen: ({ code, data }) => {
        if (code == 0) {
          Taro.kbSetGlobalData(CUSTOM_NAMES, data);
          resolve(data);
        } else {
          Taro.kbSetGlobalData(CUSTOM_NAMES, default_custom_names);
          resolve(default_custom_names);
        }
      },
    });
  });
}

/**
 * 代理商模式，团队列表
 */
export function getTeamList() {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/userInviteLevelList',
      data: { level: 0, page: 1, size: 10 },
      onThen: (res) => {
        const { data, code } = res;
        const { list } = data || {};
        if (code == 0 && isArray(list)) {
          const arr = list.slice(0, 3);
          resolve(arr);
        }
      },
    });
  });
}

/**
 * 修改好友备注
 *  */
export const modifyUserDetail = ({ invite_user_id, remark } = {}) => {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/setUserRemark',
      toastLoading: false,
      toastSuccess: true,
      toastError: true,
      data: {
        invite_user_id,
        remark,
      },
      onThen: (res) => {
        const { code } = res;
        if (code == 0) {
          resolve();
        }
      },
    });
  });
};

export const createRightBars = () => {
  return [
    {
      key: 'discount',
      image: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/icon_star.png?v=1',
      value: '享受专属寄件折扣',
    },
    {
      key: 'profit',
      image: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/icon_ranking.png?v=1',
      value: '团队下单佣金返利',
    },
    {
      key: 'platform',
      image: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/icon_like.png?v=1',
      value: '平级奖励金补贴',
    },
    {
      key: 'service',
      image: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/icon_msg.png?v=1',
      value: '专属客服对接服务',
    },
  ];
};

export const createRightMsg = () => {
  return [
    {
      key: 0,
      title: '高级寄件会员',
      desc: '享受专属寄件折扣 专属客服对接服务',
      bg: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/right_bg01.png?v=1',
      level_img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/level2.png',
    },
    {
      key: 1,
      title: '铂金推荐官',
      desc: '享受专属寄件折扣 | 团队下单佣金返利 | 专属客服对接服务',
      bg: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/right_bg02.png?v=1',
      level_img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/level1.png',
    },
    {
      key: 2,
      title: '钻石推荐官',
      desc: '享受专属寄件折扣 | 团队下单佣金返利 | 平级奖励金补贴 | 专属客服对接服务',
      bg: 'https://cdn-img.kuaidihelp.com/qj/miniapp/wboss/right_bg03.png?v=1',
    },
  ];
};

// 获取普通团员升级条件详情
export const getMemberUpdateDetail = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/customUpgradeRegimentDetail',
      onThen: (res) => {
        resolve(res.data);
      },
    });
  });
};

export const useCommissionTeamData = () => {
  const { loginData } = useSelector((state) => state.global);
  const { userInfo = {} } = loginData || {};

  const datalist = useMemo(() => {
    const isFenXiao = getCurrentUser('custom-f');
    if (getCurrentUser('member')) {
      return [
        {
          label: isFenXiao ? '团队总人数' : '推荐总人数',
          key: 'invite_count',
        },
        {
          label: isFenXiao ? '团队总订单' : '推荐人总订单',
          key: 'invite_order_count',
        },
        {
          label: isFenXiao ? '团队今日订单' : '推荐人今日订单',
          key: 'today_invite_order_count',
        },
      ];
    }
    return [
      {
        label: '总人数',
        key: 'user_count',
      },
      {
        label: '直推人数',
        key: 'direct_invite_user_count',
      },
      {
        label: '间推人数',
        key: 'indirect_invite_user_count',
      },
      {
        label: '总签收订单',
        key: 'order_count',
      },
      {
        label: '今日已签收订单',
        key: 'today_order_count',
      },
      {
        label: '待入账订单',
        key: 'estimate_order_count',
        navigator: {
          url: 'order',
          target: 'tab',
          key: 'routerParamsChange',
          options: {
            tabKey: 'wait_rebate',
          },
        },
      },
      {
        label: '累计入账佣金',
        key: 'profit',
        unit: '￥',
      },
      {
        label: '今日入账佣金',
        key: 'today_profit',
        unit: '￥',
      },
      {
        label: '待入账佣金',
        key: 'estimate_profit',
        unit: '￥',
      },
    ];
  }, [userInfo]);

  const shortDataList = useMemo(() => {
    if (datalist.length <= 0) return [];
    if (getCurrentUser('member')) {
      return datalist;
    }
    return datalist.filter(
      (item) => !['order_count', 'today_order_count', 'estimate_order_count'].includes(item.key),
    );
  }, [datalist, userInfo]);

  return {
    datalist,
    shortDataList,
  };
};
