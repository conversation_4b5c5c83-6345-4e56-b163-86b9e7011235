/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-price-table {
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  background: #fff;
  border-radius: 24px;
  $table-border: $width-base solid #e6e6e6;
  $table-fixed-width: 180px;
  $table-tr-th-height: 172px;
  $table-tr-height: 80px;
  $table-tr-height-s: 72px;
  &-head {
    flex-shrink: 0;
    float: left;
    box-sizing: border-box;
    width: $table-fixed-width;
    .tr-th {
      box-sizing: border-box;
      height: $table-tr-th-height !important;
      font-weight: 500px;
    }
  }
  &-body {
    flex: 1;
    box-sizing: border-box;
    max-width: 522px;
    &-scroll {
      width: 100%;
      &--multiple {
        display: flex !important;
        flex-wrap: nowrap !important;
        overflow-x: scroll;
        .table-body-card {
          flex-shrink: 0;
          width: 360px !important;
        }
      }
    }
    .table-body-card {
      width: 100%;
      &-1 {
        .tr-th {
          background: #f2f3f6;
        }
      }
      &-2 {
        .tr-th {
          background: #e8edff;
        }
      }
      &-3 {
        .tr-th {
          background: #efe3ff;
        }
      }
    }
    .tr-th {
      box-sizing: border-box;
      height: $table-tr-th-height !important;
      .td {
        font-weight: 500px;
        font-size: 28px;
      }
    }
    .tr-title {
      box-sizing: border-box;
      height: 100px !important;
    }
    .weight {
      display: flex;
      align-items: baseline;
      &-type {
        font-size: 24px;
      }
      &-unit {
        margin-left: 10px;
        font-size: 20px;
      }
    }
  }
  .brandInfo {
    display: flex;
    align-items: center;
    justify-content: center;
    .brand-logo {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      border-radius: 50%;
    }
  }
  .tr {
    display: flex;
    box-sizing: border-box;
    height: $table-tr-height;
    &-small {
      height: $table-tr-height-s;
    }
  }
  .td {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    color: #230707;
    font-size: 24px;
    &-flex {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-line {
      border-right: $table-border;
      border-bottom: $table-border;
      &-r {
        border-right: $table-border;
      }
      &-b {
        border-bottom: $table-border;
      }
    }
  }
}
