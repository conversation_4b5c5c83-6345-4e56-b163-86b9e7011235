/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Fragment } from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import { getBrandItem } from '@/components/_pages/_utils';
import { useKbPriceTable } from './_utils';
import './index.scss';
import classNames from 'classnames';

const KbPriceTable = () => {
  const { brands, priceConfig, aBrandPrice = [] } = useKbPriceTable();
  const { brand_list = [] } = priceConfig || {};

  const multiple = aBrandPrice && aBrandPrice.length > 1;
  const scrollCls = classNames('kb-price-table-body-scroll', {
    'kb-price-table-body-scroll--multiple': multiple,
  });
  return (
    <Fragment>
      {aBrandPrice && aBrandPrice.length > 0 && (
        <View className='kb-price-table'>
          <View className='kb-price-table-head'>
            <View className='tr tr-th'>
              <View className='td td-flex td-line td-th'>品牌</View>
            </View>
            {brand_list.map((item) => {
              const brandItem = getBrandItem(item.brand, brands);
              return (
                <View className='tr' key={item.brand}>
                  <View className='td td-flex td-line'>
                    <View className='brandInfo'>
                      <Image className='brand-logo' mode='widthFix' src={brandItem.logo_link} />
                      <Text>{brandItem.short_name}</Text>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
          <View className='kb-price-table-body'>
            <View className={scrollCls}>
              {aBrandPrice.map((item, itemIndex) => {
                const cardCls = classNames(`table-body-card table-body-card-${itemIndex + 1}`);
                return (
                  <View className={cardCls} key={item.type}>
                    <View className='tr tr-th'>
                      <View className='td td-line'>
                        <View className='tr tr-title'>
                          <View className='td td-line-b td-flex'>{item.label}</View>
                        </View>
                        <View className='tr tr-small'>
                          <View className='td td-line-r td-flex'>
                            <View className='weight'>
                              <Text className='weight-type'>首重</Text>
                              <Text className='weight-unit'>kg</Text>
                            </View>
                          </View>
                          <View className='td td-flex'>
                            <View className='weight'>
                              <Text className='weight-type'>续重</Text>
                              <Text className='weight-unit'>kg</Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                    {item.list &&
                      item.list.length > 0 &&
                      item.list.map((i) => {
                        return (
                          <View className='tr' key={i}>
                            {i.type === 'discount' ? (
                              <View className='td td-flex td-line'>
                                {i.rate ? `${i.rate}运费` : '-'}
                              </View>
                            ) : (
                              <Fragment>
                                <View className='td td-flex td-line'>
                                  {i.rate > 0 ? i.rate : '-'}元
                                </View>
                                <View className='td td-flex td-line'>
                                  {i.s_fee > 0 ? i.s_fee : '-'}元
                                </View>
                              </Fragment>
                            )}
                          </View>
                        );
                      })}
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      )}
    </Fragment>
  );
};

KbPriceTable.options = {
  addGlobalClass: true,
};

export default KbPriceTable;
