/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState, useEffect, useMemo } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import request from '@base/utils/request';
import isEmpty from 'lodash/isEmpty';
import isArray from 'lodash/isArray';
import { getCurrentUser } from '@base/utils/utils';

const formatConfigBrands = (discount_brands, append_brands) => {
  const arr = [];
  if (isArray(discount_brands) && discount_brands.length > 0) {
    discount_brands.map((i) => {
      arr.push({
        brand: i,
        type: 'discount',
      });
    });
  }
  if (append_brands && append_brands.length > 0) {
    append_brands.map((i) => {
      arr.push({
        brand: i,
        type: 'append',
      });
    });
  }
  return arr;
};

const formatConfigPrice = (config, brands) => {
  const { discount, append, ...restBrandConfig } = config || {};
  const arr = [];
  if (isArray(brands) && brands.length > 0) {
    brands.map((item) => {
      const oBrandConfig = restBrandConfig[item.brand];
      const oItem = oBrandConfig ? oBrandConfig : item.type === 'discount' ? discount : append;
      arr.push(oItem);
    });
  }
  return arr;
};

// 获取张盟主定制品牌折扣
const getBrandConfigLists = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/CustomInviteStatistic/getBrandConfigLists',
      toastLoading: false,
      onThen: (res) => {
        if (!isEmpty(res.data)) {
          const { discount_brand = [], append_brand = [], brand_config = {} } = res.data;
          const { partner, league, regiment, user } = brand_config;
          const brands = formatConfigBrands(discount_brand, append_brand);
          res.data = {
            brand_list: brands,
            partner: formatConfigPrice(partner, brands),
            league: formatConfigPrice(league, brands),
            regiment: formatConfigPrice(regiment, brands),
            user: formatConfigPrice(user, brands),
          };
        }
        resolve(res.data);
      },
    });
  });
};

const useGetPriceConfig = () => {
  const [priceConfig, setPriceConfig] = useState({});

  useEffect(() => {
    getBrandConfigLists().then((res) => {
      console.log('res', res);
      setPriceConfig(res);
    });
  }, []);

  return {
    priceConfig,
  };
};

const formatPriceConfig = (priceConfig) => {
  const { league, regiment, user } = priceConfig || {};
  const userConfig = {
    type: 'user',
    label: '高级寄件会员返利',
    list: user,
  };
  const regimentConfig = {
    type: 'regiment',
    label: '铂金推荐官返利',
    list: regiment,
  };
  const leagueConfig = {
    type: 'league',
    label: '钻石推荐官返利',
    list: league,
  };
  return {
    userConfig,
    regimentConfig,
    leagueConfig,
  };
};

// eslint-disable-next-line import/prefer-default-export
export const useKbPriceTable = () => {
  const { brands, loginData } = useSelector((state) => state.global);
  const { userInfo: { sessionid } = {} } = loginData || {};
  const { priceConfig } = useGetPriceConfig();

  const aBrandPrice = useMemo(() => {
    const { userConfig, regimentConfig, leagueConfig } = formatPriceConfig(priceConfig);
    let arr = [];
    if (getCurrentUser('partner')) {
      arr = [userConfig, regimentConfig, leagueConfig];
    } else if (getCurrentUser('league')) {
      arr = [userConfig, regimentConfig];
    } else if (getCurrentUser('regiment')) {
      arr = [userConfig];
    }
    return arr;
  }, [priceConfig, sessionid]);

  return {
    brands,
    priceConfig,
    aBrandPrice,
  };
};
