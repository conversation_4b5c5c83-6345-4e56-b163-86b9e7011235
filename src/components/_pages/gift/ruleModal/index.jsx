/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import KbModal from '@/components/errorModal';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import './index.scss';

const Index = ({ isOpened, onClose, times = {} }) => {
  const { activity_start_time, activity_end_time } = times;
  const [time, setTime] = useState({
    start: '',
    end: '',
  });

  const formatDay = (day) => dayjs(day).format('YYYY年MM月DD日');

  useEffect(() => {
    if (isOpened) {
      setTime({
        start: formatDay(activity_start_time),
        end: formatDay(activity_end_time),
      });
    }
  }, [isOpened, activity_start_time, activity_end_time]);

  return (
    <KbModal
      renderFooter={
        <View
          onClick={onClose}
          hoverClass='kb-hover'
          className='kb-rules__footer kb-size__xl kb-text__center'
        >
          我已知晓
        </View>
      }
      title={false}
      buttons={[]}
      isOpened={isOpened}
      onClose={onClose}
    >
      <View className='kb-rules__title kb-size__17 kb-text__center kb-margin-xl-b'>
        团长专属新人活动礼规则
      </View>
      <View className='kb-rules__subTitle kb-size__lg'>活动周期：</View>
      <View className='kb-rules__text kb-size__14'>
        {time.start} - {time.end}
      </View>
      <View className='kb-rules__text kb-size__14'>团长专属新人活动礼规则</View>
      <View className='kb-rules__subTitle kb-size__lg'>一重礼：</View>
      <View className='kb-rules__text kb-size__14'>
        您团队内任何成员完成首次寄付月结订单下单并结算后，系统派发3元现金红包 ，可用于提现使用
      </View>
      <View className='kb-rules__subTitle kb-size__lg'>二重礼：</View>
      <View className='kb-rules__text kb-size__14'>
        您邀请团员数量累计达到活动数量后触发解锁现金礼包，系统自动发放对应现金奖励至钱包余额；
      </View>
      <View className='kb-size__14'>1人：0.2元</View>
      <View className='kb-size__14'>5人：0.5元</View>
      <View className='kb-size__14'>10人：0.9元</View>
      <View className='kb-size__14'>15人：1.4元</View>
      <View className='kb-rules__subTitle kb-size__lg'>三重礼：</View>
      <View className='kb-rules__text kb-size__14'>
        您团队内前八单寄付月结类型订单均享受0.5元/单的现金奖励；
      </View>
      <View className='kb-size__sm kb-color__grey-3'>
        注：活动最终解释权归“微快递特惠寄”小程序所有！
      </View>
    </KbModal>
  );
};

Index.options = {
  addGlobalClass: true,
};
