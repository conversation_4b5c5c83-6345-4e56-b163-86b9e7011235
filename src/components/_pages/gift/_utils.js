/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable import/prefer-default-export */
import request from '@base/utils/request';

/**
 * 获取邀请人数+邀请人数奖励配置+活动时间
 *  */
export const getGiftInfo = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/PromotionActivity/invite',
      toastLoading: false,
      toastSuccess: false,
      toastError: false,
    })
      .then((res = {}) => {
        if (res.code == 0) {
          resolve(res.data || {});
        } else {
          resolve({});
        }
      })
      .catch(reject);
  });
};
