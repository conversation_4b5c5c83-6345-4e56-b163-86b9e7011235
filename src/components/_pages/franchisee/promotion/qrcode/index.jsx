/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import KbLoader from '@base/components/loader';
import KbCanvas from '@base/components/canvas';
import { noop } from '@base/utils/utils';
import { useMemo, useState } from '@tarojs/taro';
import { getShareParams, getShareQrcodeUrl } from '@/utils/share';
import './index.scss';

const Index = (props) => {
  // generatedUrl: 已生成的二维码地址，防止重复生成
  const { bindId, type, action, onComplete = noop, generatedUrl } = props;

  const canvasId = `${type}_${bindId}`;

  const [img, setImg] = useState(null);

  const params = useMemo(
    () =>
      getShareParams({
        user_type: type,
        invite_type: action,
        id: bindId,
      }),
    [type, action, bindId],
  );

  const url = getShareQrcodeUrl(type, params);

  const template = useMemo(
    () => ({
      action: 'qrcode',
      data: [
        {
          value: url,
          path: url,
          width: 102,
          height: 102,
          x: 0,
          y: 0,
        },
      ],
    }),
    [url],
  );

  const imgSrc = img || generatedUrl;

  const onDrawComplete = (e) => {
    const { tempFilePath } = e || {};
    if (tempFilePath) {
      onComplete(e.tempFilePath);
      setImg(tempFilePath);
    }
  };

  return (
    <View className='kb-qrcode'>
      {!generatedUrl && (
        <KbCanvas
          canvasId={canvasId}
          template={[template]}
          width={102}
          height={102}
          actionRef='canvas'
          hidden
          toImage
          onDrawComplete={onDrawComplete}
        />
      )}
      {imgSrc ? (
        <Image className='canvasImg' src={imgSrc} mode='widthFix' />
      ) : (
        <KbLoader loadingText={false} size='small' />
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
