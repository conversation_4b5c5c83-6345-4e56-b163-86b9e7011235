/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtInput } from 'taro-ui';
import KbFloatLayout from '@base/components/floatLayout';
import Form from '@base/utils/form';
import './index.scss';

class Index extends Component {
  constructor() {
    this.state = {
      form: {
        disabled: true,
        data: {},
      },
    };
  }

  componentDidMount() {
    this.createForm();
  }

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise((resolve) => {
      this.formIns = new Form(
        {
          form: {
            name: {
              required: true,
              tag: '姓名',
            },
            mobile: {
              required: true,
              reg: 'phone',
            },
            notes: {
              required: true,
              tag: '备注',
            },
          },
          onSubmit: (formData) => {
            const { onClose, onConfirm } = this.props;
            onClose && onClose();
            onConfirm && onConfirm(formData);
          },
          onReady: resolve,
        },
        this,
      );
    });
  };

  // 更新表单
  updateFormData = (data) => {
    if (!this.formIns) return;
    this.formIns.update(data);
  };

  handleSubmit = () => {
    const { code, msg } = this.formIns.check();
    if (code == 0) {
      this.onSubmit_form();
    } else {
      Taro.kbToast({
        text: msg,
      });
    }
  };

  render() {
    const { isOpened, onClose } = this.props;
    const {
      form: { data },
    } = this.state;
    return (
      <KbFloatLayout
        isOpened={isOpened}
        title='推广信息'
        onClick={this.handleSubmit}
        onClose={onClose}
        buttons={[
          {
            key: 'confirm',
            label: '创建推广链接',
          },
        ]}
      >
        {isOpened && (
          <View className='kb-promotion-modal kb-margin-lg'>
            <View className='kb-form'>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>姓名</View>
                <View className='item-content'>
                  <AtInput
                    maxLength={10}
                    cursor={-1}
                    value={data.name}
                    placeholder='请输入姓名'
                    alwaysEmbed
                    onChange={this.onChange_form.bind(this, 'name')}
                  />
                </View>
              </View>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>手机号</View>
                <View className='item-content'>
                  <AtInput
                    cursor={-1}
                    value={data.mobile}
                    placeholder='请输入联系电话'
                    alwaysEmbed
                    onChange={this.onChange_form.bind(this, 'mobile')}
                  />
                </View>
              </View>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>备注</View>
                <View className='item-content'>
                  <AtInput
                    maxLength={50}
                    cursor={-1}
                    value={data.notes}
                    placeholder='请输入备注'
                    alwaysEmbed
                    onChange={this.onChange_form.bind(this, 'notes')}
                  />
                </View>
              </View>
            </View>
          </View>
        )}
      </KbFloatLayout>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
