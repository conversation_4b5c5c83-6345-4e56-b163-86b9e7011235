import Taro, { useState } from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import { makePhoneCall } from '@base/utils/utils';
import classNames from 'classnames';
import icon_promotion from '@/assets/icon/promotion.jpg';
import KbQrcode from '../qrcode';
import './index.scss';

const Index = (props) => {
  const { data = {} } = props;
  const {
    id,
    invite_id,
    name,
    mobile,
    notes,
    regiment_num = 0,
    regiment_order_num = 0,
    users_order_num = 0,
    create_at,
  } = data || {};

  const [visible, setVisible] = useState(false);
  const [qrcodeUrl, setQrcodeUrl] = useState({});

  const handleCall = (phone) => {
    makePhoneCall(phone);
  };

  const toggleVisible = () => {
    setVisible(!visible);
  };

  const handelJump = (url, options) => {
    Taro.navigator({
      url,
      options,
    });
  };

  const handleSavePic = (item) => {
    if (!item) {
      Taro.kbToast({
        text: '暂无可保存的二维码',
      });
      return;
    }
    Taro.saveImageToPhotosAlbum({
      filePath: item,
      success: () => {
        Taro.showToast({
          title: '保存成功',
        });
      },
    });
  };

  const updateQrcodeUrl = (type, url) => {
    setQrcodeUrl((prevState) => ({
      ...prevState,
      [type]: url,
    }));
  };

  return (
    <View className='kb-promotion__item kb-background__white'>
      <View className='kb-promotion__item--info kb-spacing-lg'>
        <View className='at-row'>
          <View className='info-icon kb-margin-md-r'>
            <Image className='info-icon__img' src={icon_promotion} mode='widthFix' />
          </View>
          <View className='flex-1'>
            <View className='at-row at-row__justify--between'>
              <View>
                <View className='kb-size__lg'>{name}</View>
                <View className='kb-size__md kb-color__grey kb-margin-sm-b'>{notes}</View>
              </View>
              <View
                className='info-icon info-icon__phone'
                hoverClass='kb-hover-opacity'
                onClick={handleCall.bind(null, mobile)}
              >
                <AtIcon prefixClass='kb-icon' value='phone1' className='kb-icon-size__md' />
              </View>
            </View>

            <View className='at-row at-row__justify--between at-row__align--center' onClick={toggleVisible} hoverClass='kb-hover-opacity'>
              <View className='kb-size__base kb-color__grey'>{create_at}</View>
              <View className='kb-spacing-sm'>
                <AtIcon
                  prefixClass='kb-icon'
                  value='arrow'
                  className={classNames([
                    'kb-size__sm kb-color__grey',
                    `kb-icon__direction-${visible ? 'up' : 'down'}`,
                  ])}
                />
              </View>
            </View>
          </View>
        </View>
      </View>
      {visible && (
        <View className='kb-promotion__item--detail'>
          <View className='at-row at-row__align--center kb-margin-40'>
            <View className='detail-qrcode kb-margin-md-r'>
              <KbQrcode
                generatedUrl={qrcodeUrl.regiment}
                bindId={invite_id}
                type='regiment'
                action='promotion'
                onComplete={(url) => {
                  updateQrcodeUrl('regiment', url);
                }}
              />
            </View>
            <View className='flex-1'>
              <View className='kb-size__lg'>专属团长码</View>
              <View className='kb-size__md kb-color__grey'>{mobile}</View>
            </View>
            <AtButton
              size='small'
              circle
              type='primary'
              onClick={handleSavePic.bind(null, qrcodeUrl.regiment)}
            >
              保存
            </AtButton>
          </View>
          <View className='at-row at-row__align--center kb-margin-40'>
            <View className='detail-qrcode kb-margin-md-r'>
              <KbQrcode
                generatedUrl={qrcodeUrl.user}
                bindId={invite_id}
                type='user'
                action='promotion'
                onComplete={(url) => {
                  updateQrcodeUrl('user', url);
                }}
              />
            </View>
            <View className='flex-1'>
              <View className='kb-size__lg'>专属下单码</View>
            </View>
            <AtButton
              size='small'
              circle
              type='primary'
              onClick={handleSavePic.bind(null, qrcodeUrl.user)}
            >
              保存
            </AtButton>
          </View>
          <View
            className='kb-navigator kb-margin-lg-b'
            hoverClass='kb-hover-opacity'
            onClick={handelJump.bind(null, 'franchisee/promotion/regiment', {
              lps_id: id,
            })}
          >
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View>累计推广团长</View>
              <View>{regiment_num || 0}人</View>
            </View>
          </View>
          <View
            className='kb-navigator'
            hoverClass='kb-hover-opacity'
            onClick={handelJump.bind(null, 'order/listDetail', {
              title: '有效订单',
              lps_id: id,
              source: 'promotion',
            })}
          >
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View>累计有效订单</View>
              <View>{regiment_order_num * 1 + users_order_num * 1 || 0}单</View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
