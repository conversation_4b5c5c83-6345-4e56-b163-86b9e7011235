.kb-promotion__item {
  border-radius: $border-radius-xxl;
  &--info {
    .info-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 96px;
      height: 96px;
      background-color: #f7f8fa;
      border-radius: $border-radius-circle;
      &__img {
        width: 46px;
        height: 46px;
      }
      &__phone {
        width: 60px;
        height: 60px;
      }
    }
  }

  &--detail {
    padding: 40px;
    border-top: $width-base dashed #dcdee0;
    .detail-qrcode {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 120px;
      background-color: #f7f8fa;
    }
  }
  .flex-1 {
    flex: 1;
  }
  .kb-margin-40 {
    margin-bottom: 40px;
  }
  .kb-navigator {
    padding: 0;
    &::before {
      border: none;
    }
  }
}
