/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

/**
 * 加盟商，昨日信息，7日走势图
 *  */
export const getFranchiseeData = () =>
  new Promise((resolve, reject) => {
    request({
      url: '/api/League/profitTrend',
      toastLoading: true,
      toastError: false,
    }).then((res = {}) => {
      const { code, data = {}, msg } = res;
      if (code == 0 && data) {
        resolve(data);
      } else {
        reject(msg);
      }
    });
  });

/**
 * 新增推广人
 *  */
export const addPromotion = (data) =>
  new Promise((resolve) => {
    request({
      url: '/api/LeaguePromotionSpecialist/add',
      toastLoading: true,
      toastError: false,
      toastSuccess: true,
      data,
      onThen: (res = {}) => {
        const { code } = res;
        if (code == 0) {
          resolve();
        }
      },
    });
  });

// 获取团长代补款订单
export const getUnPaymentOrders = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/League/unPaymentOrders',
      toastLoading: false,
      toastError: false,
      data: {
        page: 1,
        page_size: 10,
      }
    }).then((res = {}) => {
      const { code, data = {}, msg } = res;
      if (code == 0 && data) {
        resolve(data);
      } else {
        reject(msg);
      }
    });
  });
}
