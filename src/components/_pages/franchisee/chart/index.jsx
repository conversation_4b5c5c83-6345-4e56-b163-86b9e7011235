/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbLine from '@base/components/echarts/line';
import KbEmpty from '@base/components/empty';
import dayjs from 'dayjs';
import './index.scss';

const Chart = (props) => {
  const { chartData = [] } = props;

  const xValue = chartData.map((v) => dayjs(v.static_date).get('date') + '日');
  const yValue = chartData.map((v) => v.profit * 1);

  return (
    <View className='kb-charts kb-background__white kb-spacing-lg'>
      <View className='kb-charts--title'>近七日收益趋势</View>
      <View className='kb-charts--container'>
        {chartData && chartData.length > 0 ? (
          <KbLine
            data={chartData}
            config={{
              color: '#165DFF',
              tooltip: {
                show: true,
                trigger: 'axis',
                valueFormatter: (value) => value.toFixed(2) + '￥',
              },
              xAxis: {
                type: 'category',
                boundaryGap: true,
                data: xValue,
                axisTick: {
                  alignWithLabel: true,
                },
                axisLabel: {
                  color: '#86909C',
                  margin: 25,
                },
              },
              yAxis: {
                type: 'value',
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  color: '#86909C',
                },
                axisLine: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    show: true,
                    type: 'dashed',
                  },
                },
              },
              series: [
                {
                  name: '收益',
                  type: 'line',
                  data: yValue,
                  smooth: true,
                  symbol: 'emptyCircle',
                  symbolSize: 6,
                  lineStyle: {
                    width: 2,
                  },
                  itemStyle: {
                    borderWidth: 2,
                  },
                },
              ],
            }}
          />
        ) : (
          <View className='kb-charts--empty at-row at-row__align--center at-row__justify--center'>
            <View className='kb-charts--empty__content'>
              <KbEmpty description='暂无近7日数据' />
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

Chart.options = {
  addGlobalClass: true,
};

export default Chart;
