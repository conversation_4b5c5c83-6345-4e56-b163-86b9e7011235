import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import numeral from 'numeral';
import './index.scss';

const FranchiseeInfo = (props) => {
  const { data = {} } = props;
  const { yesterday_profit = 0, regiment_num = 0 } = data;

  const formatNum = (num = 0) => numeral(num).format('0.00');

  const onJump = (url) => {
    Taro.navigator({
      url,
    });
  };

  return (
    <View className='kb-info'>
      <View className='at-row'>
        <View
          onClick={onJump.bind(null, 'profit?pageSource=leader')}
          hoverClass='kb-hover-opacity'
          className='at-col at-col-6 kb-size__base'
        >
          <View className='kb-info__wrapper kb-spacing-md kb-background__white kb-navigator kb-margin-sm-r'>
            <View className='kb-width-100'>
              <View className='kb-info__title'>收益管理</View>
              <View className='kb-info__num'>{formatNum(yesterday_profit)}</View>
              <View className='kb-info__subTitle kb-color__grey'>昨日</View>
            </View>
          </View>
        </View>
        <View
          onClick={onJump.bind(null, 'team/manage?pageSource=leader')}
          hoverClass='kb-hover-opacity'
          className='at-col at-col-6 kb-size__base'
        >
          <View className='kb-info__wrapper kb-spacing-md kb-background__white kb-navigator kb-margin-sm-l'>
            <View className='kb-width-100'>
              <View className='kb-info__title'>团长管理</View>
              <View className='kb-info__num'>{regiment_num}</View>
              <View className='kb-info__subTitle kb-color__grey'>昨日</View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

FranchiseeInfo.options = {
  addGlobalClass: true,
};

export default FranchiseeInfo;
