/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import { reportAnalytics } from '@base/utils/utils';
import Taro, { useMemo } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import { extendMemo } from '~base/components/_utils';

/**
 *
 * @description 合并列表数据
 * @param {*} current
 * @param {*} list
 */
export const mergeListByTitle = (current, list, callback) => {
  if (!isArray(current)) return list;
  current.map((item) => {
    const formattedItem = isFunction(callback) ? callback(item) : item;
    const { title: itemTitle, list: itemList } = formattedItem;
    const index = list.findIndex(({ title }) => !itemTitle || title === itemTitle);
    if (index >= 0) {
      list[index].list = list[index].list.concat(itemList);
    } else {
      list.push(formattedItem);
    }
  });
  return list;
};

export { extendMemo };

/**
 *
 * @description 记录点击tab
 * @param {*} item
 */
export function tabItemTapCall(item) {
  const { text: options } = item;
  reportAnalytics({
    key: 'tab_click',
    options,
  });
}
export const creatSignature = (params) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/dak/ShareDakDetail/saveSign',
      data: { ...params },
      onThen: (_, res) => {
        const { code, msg, data } = res;
        if (code === 0) {
          resolve({ data });
        } else {
          reject({ msg });
        }
      },
    });
  });
};

export const formatShareTime = (time, statue) => {
  return time
    ? decodeURIComponent(time).split('-').slice(1).join('-')
    : statue == 'inTime'
    ? '暂无'
    : '未出库';
};

/**
 * @description 不需要拦截的页面（注册页面，邀请页面，咨询页面，人脸识别）
 *  */
export const dontAuthRoute = (currentPath) => {
  const needCheckRoute = [
    '/pages-0/pages/team/create/index',
    '/pages-0/pages/team/invite/index',
    '/pages/consulting/index',
    '/pages-0/pages/team/scan/index',
    '/pages-0/pages/guide/index',
  ];
  return needCheckRoute.includes(currentPath);
};

/**
 * @description 不展示页脚背景的路由
 *  */
export const dontShowFootBg = (currentPath) => {
  const tabBraRoute = [
    '/pages/index/index',
    // '/pages/order/index',
    '/pages/consulting/index',
    '/pages/user/index',
    '/pages-0/pages/address/batch/index',
    '/pages-3/pages/user/account/index',
    '/pages-0/pages/team/scan/index',
    '/pages-0/pages/order/edit/goods/index',
    '/pages-2/pages/freight/query/index',
    '/pages-0/pages/order/edit/brand/index',
  ];
  return tabBraRoute.includes(currentPath);
};

/**
 * 记录打开来源
 *  */
export const recordFromPath = (source) => {
  request({
    url: '/api/ChannelStatistic/effectiveClick',
    toastError: false,
    toastLoading: false,
    toastSuccess: false,
    data: { source },
  });
};

/**
 * 手机号脱敏
 *  */
export function phoneDesensitization(phone) {
  const pat = /(\d{3})\d*(\d{4})/;
  return `${phone}`.replace(pat, '$1****$2');
}

/**
 * 获取品牌数据
 */
export const getBrandItem = (brand, brands = Taro.brands) => {
  return brand && brands ? brands[brand] || {} : {};
};

export const useBrandItem = (opt = {}) => {
  const { brand } = opt || {};
  const { brands } = useSelector((state) => state.global);

  const brandItem = useMemo(() => {
    return getBrandItem(brand, brands);
  }, [brand, brands]);

  return {
    brandItem,
  };
};
