

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


export const verifyServiceForm = (data, serviceConfig) => {
  const { isDecVal } = serviceConfig || {};
  const { keep_account, collection, is_arrive_pay } = data || {};
  if (!keep_account && isDecVal) {
    return { code: 104, msg: "请填写增值服务声明价值" };
  }
  if (collection && is_arrive_pay) {
    return { code: 106, msg: "到付和代收只能填写其中一项" };
  }
  return { code: 0 };
};
