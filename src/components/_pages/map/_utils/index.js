/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getStorage, setStorage } from '@base/utils/utils';
import request from '@base/utils/request';

/**
 * 地图计算工具
 */
export const mapCalculation = {
  /**
   * 计算角度
   */
  rad(d) {
    return (d * Math.PI) / 180.0;
  },
  /* 计算两点间直线距离
   * @param a 表示纬度差
   * @param b 表示经度差
   * @return 返回的是距离，单位m
   */
  getDistance(latFrom, lngFrom, latTo, lngTo) {
    var EARTH_RADIUS = 6378136.49;
    var radLatFrom = this.rad(latFrom);
    var radLatTo = this.rad(latTo);
    var a = radLatFrom - radLatTo;
    var b = this.rad(lngFrom) - this.rad(lngTo);
    var distance =
      2 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLatFrom) * Math.cos(radLatTo) * Math.pow(Math.sin(b / 2), 2),
        ),
      );
    distance = distance * EARTH_RADIUS;
    distance = Math.round(distance * 10000) / 10000;
    return parseFloat(distance.toFixed(0));
  },
  /**
   * 计算坐标库中符合一定距离的坐标
   * @param 目标点坐标
   * @param 坐标库
   * @param 距离，单位km
   */
  queryCoordinate(latFrom, lngFrom, list, d) {
    const distance = d * 1000;
    let arr = [];
    if (list && list.length > 0) {
      list.map((item) => {
        const { longitude, latitude } = item;
        let distanceFrom = this.getDistance(latFrom, lngFrom, latitude, longitude);
        if (distanceFrom <= distance || distance === 0) {
          arr.push({
            ...item,
            dis: distanceFrom,
          });
        }
      });
    }
    return this.fixedPointsQuantity(arr);
  },
  /**
   * 获取不同缩放级别的中心点距离范围
   * @param scale
   * @returns distance 单位 km
   */
  getDistanceRange(scale) {
    // <11 50km 全部范围
    // 16-20 1km
    let maxScale = 10,
      minScale = 16;
    if (scale <= maxScale) {
      return 0;
    }
    if (scale >= minScale) {
      return 1;
    }
    if (scale <= 11) {
      return 30;
    }
    if (scale <= 12) {
      return 10;
    }
    if (scale <= 13) {
      return 6;
    }
    if (scale <= 14) {
      return 4;
    }
    return 2;
  },
  // 固定点数量
  fixedPointsQuantity(arr) {
    let max = 2000;
    let points = arr;
    let pointsLen = points.length;
    if (pointsLen > max) {
      // 排序
      // points = points.sort((a, b) => a.dis - b.dis);
      // 分组
      const groupNum = 4; // 组数
      const groupNumMax = Math.ceil(max / groupNum) || 1; // 每组提取最大数量
      const space = Math.ceil(pointsLen / groupNum); // 每组数量
      const pointsTml = []; // 临时存放
      for (let i = 0; i < groupNum; i++) {
        let items = points.slice(i * space, i * space + Math.min(space, groupNumMax));
        // items = this.getRandomPoint(items, groupNumMax);
        pointsTml.push(...items);
      }
      points = pointsTml;
    }
    console.log('获取的的标记点', points.length);
    return points;
  },
  /**
   * 获取随机点
   * @param arr 源数组
   * @param 需要取出的数量
   * @returns 取出的结果数组
   */
  getRandomPoint(arr, num) {
    let out = [];
    let count = 0;
    while (count < num) {
      let temp = (Math.random() * arr.length) >> 0;
      let item = arr.splice(temp, 1)[0];
      if (item) {
        out.push(item);
      }
      count++;
    }
    return out;
  },
};

// 地图数据版本号缓存key
export const mapDataVersionKey = 'mapDataVersionKey';
// 地图数据缓存key
export const mapDataKey = 'mapDataKey';
// 区域级别缓存key
export const mapDataLevelKey = 'mapDataLevelKey';

/**
 * 检查是否需要刷新
 */
export const checkAndRefresh = () => {
  // 检查是否需要更新数据
  return new Promise((resolve) => {
    getStorageVersion().then((data) => {
      const { version: storageVersion = '', level_version: storageLevel_version } = data || {};
      request({
        url: '/g_wkd/v2/EpidemicMessage/lastCovid19LocationFile',
        toastLoading: false,
        toastError: true,
        onThen: ({ data }) => {
          const { version, file, level_version, level_file } = data || {};
          resolve({
            refresh: storageVersion !== version || !version || !storageVersion,
            url: file,
            level_refresh:
              storageLevel_version !== level_version || !level_version || !storageLevel_version,
            level_url: level_file,
          });
          if (version || level_version) {
            setStorage({
              key: mapDataVersionKey,
              data: version + '-' + level_version,
            });
          }
        },
      });
    });
  });
};

/**
 * 加载地图数据逻辑
 * @param refresh 是否需要刷新
 * @param url 最新数据文件地址
 * @param key 本地缓存数据的
 */
export const loadMapData = (refresh, url, key) => {
  let msg = key == 'mapDataLevelKey' ? '区域级别数据' : '地图数据';
  return new Promise((resolve) => {
    if (refresh && url) {
      console.log(`${msg}==>需要更新==>重新拉取`);
      getRequestMapData(url, key).then((data) => {
        resolve(data);
      });
    } else {
      getStorageMapData(key).then((res) => {
        console.log(`使用本地缓存${msg}`);
        if (res && res.data) {
          resolve(res.data);
        } else {
          console.log(`本地缓存${msg}==>不存在，重新拉取`);
          getRequestMapData(url, key).then((data) => {
            resolve(data);
          });
        }
      });
    }
  });
};

/**
 * 获取请求的地图数据
 */
export const getRequestMapData = (url, key) => {
  return new Promise((resolve) => {
    request({
      url,
      method: 'GET',
      toastError: true,
      onThen: (res) => {
        if (res.code == 0) {
          resolve(res.data);
          setStorage({
            key,
            data: res.data,
          });
        } else {
          resolve(null);
        }
      },
    });
  });
};

/**
 * 获取缓存的地图数据
 * @param key 缓存数据key
 */
export const getStorageMapData = (key) => {
  return new Promise((resolve) => {
    getStorage({
      key,
    })
      .then((res) => {
        resolve({ data: res.data && res.data.data });
      })
      .catch(resolve);
  });
};

/**
 * 获取缓存版本号
 */
export const getStorageVersion = () => {
  return new Promise((resolve) => {
    getStorage({
      key: mapDataVersionKey,
    })
      .then((res) => {
        const { data: sData } = res.data || {};
        const [version, level_version] = sData ? sData.split('-') : [];
        resolve({ version, level_version });
      })
      .catch(resolve);
  });
};

export const getShareTitle = (day) => {
  if (day >= 14) {
    return `已经连续【${day}】天无阳，再接再厉，胜利就在眼前！`;
  }
  if (day > 3) {
    return `已经连续【${day}】天无阳，做好防护，继续保持！`;
  }
  return `近日有新增哦，请不要聚集，保护好自己！`;
  // 管控或风控区
};
