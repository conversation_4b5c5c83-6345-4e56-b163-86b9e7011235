/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-edit-ai {
  box-sizing: border-box;
  height: 330px;
  border-radius: 24px;
  .item-bar {
    width: auto !important;
    &__item {
      padding-left: $spacing-v-lg;
      line-height: 1;
    }
  }
  .kb-item-wrap {
    overflow: hidden;
  }
  &--textareaBox {
    background: #f7f8fa;
    border-radius: $border-radius-xl;
  }
}

.kb-background {
  &__white {
    background-color: $color-white;
  }
  &__grey {
    background-color: $color-grey-5;
  }
}

.kb-flex-grw {
  display: flex;
  flex-grow: 1;
}
