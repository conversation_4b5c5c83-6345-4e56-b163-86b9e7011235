/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { labelRedFn } from '@/components/_pages/address/_utils';
import { createListener } from '@base/utils/utils';
import classNames from 'classnames';
import qs from 'qs';
import './index.scss';

const Index = (props) => {
  const { placeholder, value, onChange, locked, customStyle = '' } = props;

  const onClick = () => {
    if (locked) return;
    const { province = '', city = '', district = '' } = value || {};
    // 监听区域选择
    createListener('citySelect', onChange);
    Taro.navigator({
      url: `city?${qs.stringify({
        current: `${province}-${city}-${district}`,
      })}`,
    });
  };
  const areaCls = classNames(
    {
      'kb-nav__icon': !locked,
    },
    'kb-area',
  );
  return (
    <View hoverClass='kb-hover' className={areaCls} onClick={onClick} style={customStyle}>
      {value && value.province ? (
        <View className='kb-area__value'>
          <Text className={labelRedFn(value.province_confidence)}>{value.province}</Text>{' '}
          <Text className={labelRedFn(value.city_confidence)}>{value.city}</Text>{' '}
          <Text className={labelRedFn(value.district_confidence)}>{value.district}</Text>
        </View>
      ) : (
        <View className='kb-area__placeholder'>{placeholder}</View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  value: null,
  org: '',
  placeholder: '省市区',
  onChange: () => {},
};

export default Index;
