/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-v-lg $spacing-h-lg;
  line-height: 1.5;
  background-color: $color-white;
  &__value,
  &__placeholder {
    flex-grow: 1;
    padding-right: $spacing-h-lg;
  }
  &__placeholder {
    @include input-placeholder;
  }
  &::after {
    color: $color-black-1;
    font-size: 22px;
    font-family: 'kb-icon';
  }
  &.kb-hover {
    border-radius: 0 !important;
  }
}
.kb-nav__icon {
  &::after {
    margin-right: -$spacing-h-sm;
    content: '\e661';
  }
}
