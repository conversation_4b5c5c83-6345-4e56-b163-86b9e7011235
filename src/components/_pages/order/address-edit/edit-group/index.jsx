/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbAddressEditManual from '@/components/_pages/order/address-edit/edit-manual';
import KbAddressEditAi from '@/components/_pages/order/address-edit/edit-ai';
import { debounce, noop } from '@base/utils/utils';
import { supportAi } from '@/components/_pages/order/address-edit/utils';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import './index.scss';

class Index extends Component {
  // eslint-disable-next-line no-unused-vars
  constructor(props) {
    this.state = {
      addressData: {},
    };
    this.triggerFormDataChange = debounce(this.triggerFormDataChange);
  }

  componentDidMount() {
    const { data } = this.props;
    const hasData = Object.values(data).some((v) => v);
    hasData && this.triggerFormDataChange(data);
  }

  triggerFormDataChange = (data) => {
    if (isEmpty(data)) return;
    const { addressData } = this.state;
    this.setState({
      addressData: { ...addressData, ...data },
    });
  };

  handleAddressChange(key, data) {
    const { onChange } = this.props;
    switch (key) {
      case 'ai':
        // eslint-disable-next-line no-param-reassign
        data = isArray(data) ? data[0] : data;
        this.triggerFormDataChange(data);
        break;
      case 'manual':
        break;
    }
    onChange({ ...data });
  }

  render() {
    const { addressData } = this.state;
    const { allowUpload, locked, allowAi, org, source, action, allowSetDefault, onParseImg } =
      this.props;
    const aiArray = supportAi.filter((key) => {
      return source === 'list' && key == 'book' ? false : true;
    });

    return (
      <View className='kb-margin-lg'>
        {!locked && allowAi && (
          <KbAddressEditAi
            supportAi={aiArray}
            type={org}
            className='kb-background__white kb-margin-lg-tb kb-spacing-lg'
            onFormDataUpdate={this.handleAddressChange.bind(this, 'ai')}
            onParseImg={onParseImg}
          />
        )}
        <KbAddressEditManual
          ref={this.addressEditRef}
          org={org}
          data={addressData}
          locked={locked}
          action={action}
          showSaveBtn={allowUpload}
          showSetDefaultBtn={allowSetDefault}
          showTitle
          onChange={this.handleAddressChange.bind(this, 'manual')}
        />
      </View>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  org: 'send', // 地址类型
  data: {}, //地址信息
  allowUpload: false,
  allowAi: true,
  onChange: noop,
};

export default Index;
