

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-pos-r {
  position: relative;
}
.kb-block--inline {
  position: relative;
  display: inline-block;
}
.kb-tip {
  position: absolute;
  top: -60px;
  left: 50%;
  padding: 0 $spacing-h-xs;
  transform: translateX(-57%);
  &_text {
    display: inline-block;
    padding: 8px 52px 8px $spacing-h-md;
    color: $color-white;
    font-size: $font-size-sm;
    white-space: nowrap;
    background-color: rgba($color: $color-black-1, $alpha: 0.6);
    border-radius: 4px;
    &::before {
      position: absolute;
      bottom: -19px;
      left: 55%;
      display: block;
      width: 0;
      height: 0;
      border: 12px solid;
      border-color: rgba($color: $color-black-1, $alpha: 0.5) transparent transparent;
      content: '';
    }
  }
  &--close {
    @include close-icon(22px, $color-white);
    position: absolute;
    top: 50%;
    right: 6px;
    display: inline-block;
    width: 22px;
    font-size: $font-size-xs;
    transform: translateY(-50%);
  }
}
