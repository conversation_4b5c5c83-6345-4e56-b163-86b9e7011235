

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { org, showTip, onClose, onClipboard } = props;
  const iconCls = `kb-icon-size__lg kb-color__${
    org === 'send' ? 'brand' : 'orange'
  } kb-clipborard-text`;

  const handleCloseClipborardTip = () => {
    onClose && onClose();
  };
  const handleClipboard = () => {
    onClipboard && onClipboard();
  };
  return (
    <View className='kb-pos-r'>
      <View
        className='kb-block--inline'
        onClick={handleClipboard.bind(null, (e) => e.stopPropagation())}
      >
        <View className='kb-spacing-xl-lr kb-spacing-sm-tb kb-tip at-row '>
          {showTip && (
            <View className='kb-tip_text'>
              <Text>点击粘贴,一键录入收寄件地址</Text>
              <Text
                hoverClass='kb-hover'
                onClick={handleCloseClipborardTip.bind(null, (e) => e.stopPropagation())}
                className='kb-tip--close'
              ></Text>
            </View>
          )}
        </View>
        <AtIcon className={iconCls} prefixClass='kb-icon' value='clipboard-address'></AtIcon>
      </View>
    </View>
  );
};

export default Index;
