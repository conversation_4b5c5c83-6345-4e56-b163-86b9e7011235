/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import { AtIcon, AtInput } from 'taro-ui';
import KbArea from '@/components/_pages/order/address-edit/area';
import KbTextarea from '@base/components/textarea';
import KbGps from '@base/components/gps';
import KbCheckBox from '@base/components/checkbox';
// import KbLoginAuth from '@base/components/login/auth';
import { extractData, debounce } from '@base/utils/utils';
import Form from '@base/utils/form';
import { extendMemo } from '@base/components/_utils';
import { getFormItem } from '@/components/_pages/order/_utils';
import { addressKeys, chooseAddress, checkDataComplete } from '@/components/_pages/address/_utils';
import { checkLegalAddress } from '@/components/_pages/address/_utils/address.edit';
import { envNames } from '~/utils/config';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  constructor() {
    this.state = {
      form: {
        data: {},
        errKeys: {},
        disabled: true,
      },
      errKeys: {},
      is_default: 1,
      focusMap: {
        name: false,
        mobile: false,
        address: false,
      },
    };
    this.orgMap = {
      send: '寄件',
      receive: '收件',
    };
    this.disabled = true;
    this.handleChange = debounce(this.handleChange, 300);
  }

  componentDidMount() {
    this.createForm();
  }

  componentDidUpdate(preProps) {
    const { data } = preProps;
    let { data: currentData } = this.props;
    if (data !== currentData) {
      currentData = this.dealSendDefault(currentData);
      this.updateFormData(currentData);
    }
  }

  dealSendDefault = (data) => {
    // 发件地址默认勾选保存
    const { org } = this.props;
    let sendSaveDefault = !!(org == 'send' && data && data.save === '');
    if (sendSaveDefault) {
      data.save = 1;
    }
    return data;
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise((resolve) => {
      const { storageKey, data } = this.props;
      const form = getFormItem({
        keys: [...addressKeys, 'province_confidence', 'city_confidence', 'district_confidence'],
        data: { ...data },
      });

      this.formIns = new Form(
        {
          form,
          storageKey,
          onUpdate: (data) => {
            const { form = {}, is_default, errKeys } = this.state;
            // ai、地址簿和图片识别后去校验地址
            if (data.formEvent == 'no' && data.data) {
              this.validateAddress(form.data);
            }
            // 增量更新错误提示
            if (data.eventType == 'outter' || data.eventType == 'change') {
              this.updateData = {
                ...(this.updateData || {}),
                ...(data.data || {}),
              };
              let errKeys = form.errKeys || {};
              if (!isEmpty(errKeys) && !isEmpty(this.updateData)) {
                let keys = Object.keys(this.updateData) || [];
                for (let key in errKeys) {
                  if (!keys.includes(key)) {
                    delete errKeys[key];
                  }
                }
              }
              this.setState({
                errKeys,
              });
            } else if (data.eventType == 'clean') {
              this.updateData = {};
              this.setState({
                errKeys: {},
              });
            }
            const { showSetDefaultBtn } = this.props;
            return this.props.onChange({
              ...data,
              ...(showSetDefaultBtn ? { is_default } : {}),
              disabled: data.disabled || !isEmpty(errKeys) || this.disabled,
            });
          },
          onReady: resolve,
        },
        this,
      );
    });
  };

  // 更新表单
  updateFormData = (data) => {
    if (!this.formIns) return;
    this.formIns.update(data);
  };

  // 选择小程序平台地址
  handleSelector = () => {
    chooseAddress()
      .then((data) => {
        data.name && this.updateFormData(data);
      })
      .catch(() => {});
  };

  handleMiniAppAuthPhone = (res) => {
    if (res.code == 0) {
      res.data && this.updateFormData({ mobile: res.data.mobile });
    }
  };

  handleGetPhone = () => {
    const { loginData = {} } = this.props;
    const { userInfo = {} } = loginData;
    const { mobile } = userInfo;
    this.updateFormData({ mobile });
  };

  // 选择省市区
  onChange_city = (data) => {
    this.updateFormData(data);
  };

  // 定位
  onGps = (data) => {
    this.updateFormData(extractData(data, ['province', 'city', 'district', 'address']));
  };

  // 处理保存地址
  handleSaveAddress(ev) {
    this.updateFormData({
      save: ev ? 1 : 0,
    });
  }

  // 大客户默认地址
  handleSetDefault(ev) {
    const { onChange } = this.props;
    this.setState(
      {
        is_default: ev ? 1 : 0,
      },
      () => {
        const { is_default } = this.state;
        onChange({ is_default });
      },
    );
  }

  // 处理聚焦
  handleFocus(action, key) {
    const { focusMap } = this.state;
    // 失焦校验地址是否合法
    if (action == 'blur') {
      const formData = this.formIns.getState();
      this.validateAddress(formData.data);
    }
    focusMap[key] = action == 'focus' ? true : false;
    this.forceUpdate({
      focusMap,
    });
  }

  /**
   * 校验地址是否合法
   *  */
  async validateAddress(address) {
    const { org } = this.props;
    const addressKeys = ['province', 'city', 'district', 'address'];
    const { complete } = checkDataComplete(address, addressKeys);
    if (!complete) return;
    this.disabled = true;
    // 校验地址是否合法
    const msg = await checkLegalAddress({
      type: org,
      province: address.province,
      city: address.city,
      county: address.district,
      address: address.address,
    });
    this.disabled = Boolean(msg);
    // 强制更新disabled字段
    this.formIns.update();

    if (msg) {
      this.setState((prevState) => {
        return {
          errKeys: {
            ...prevState.errKeys,
            address: {
              errorMsg: msg,
            },
          },
          form: {
            ...prevState.form,
            errKeys: {
              ...prevState.form.errKeys,
              address: {
                errorMsg: msg,
              },
            },
            disabled: true,
          },
        };
      });
    }
  }

  handleChange(key, value, ev) {
    if (ev && ev.type === 'input') return;
    this.onChange_form(key, value, ev);
  }

  render() {
    const {
      locked,
      className,
      org,
      showSaveBtn,
      showSetDefaultBtn,
      showName,
      showTitle = false,
    } = this.props;
    const {
      form: { data },
      errKeys = {},
      is_default,
      focusMap,
    } = this.state;
    const wrapCls = classNames('kb-edit-manual kb-from', className);
    const nameError = errKeys.name || {};
    const mobileError = errKeys.mobile || {};
    const areaError = errKeys.province || errKeys.city || errKeys.district || {};
    const addressError = errKeys.address || {};
    return (
      <View className={wrapCls}>
        {showTitle && (
          <View className='kb-edit-manual-title'>
            <View className='kb-edit-manual-title--title'>手动录入</View>
            {!locked.selector && (
              <View
                className='kb-edit-manual-title--book'
                onClick={this.handleSelector}
                hoverClass='kb-hover-opacity'
              >
                {envNames[process.env.PLATFORM_ENV]}地址薄
              </View>
            )}
          </View>
        )}
        {showName && (
          <Fragment>
            <View className='kb-form__item'>
              <View className='at-col'>
                <View className='at-row'>
                  <View className='item-content'>
                    <AtInput
                      cursor={-1}
                      value={data.name}
                      disabled={locked.name}
                      placeholder='姓名'
                      focus={focusMap.name}
                      onBlur={this.handleFocus.bind(this, 'blur', 'name')}
                      onChange={this.onChange_form.bind(this, `name`)}
                    >
                      {!showTitle && !locked.selector && (
                        <View
                          className='extra-icon'
                          onClick={this.handleSelector}
                          hoverClass='kb-hover-opacity'
                        >
                          <AtIcon
                            className='kb-color__brand'
                            prefixClass='kb-icon'
                            value={process.env.PLATFORM_ENV}
                          />
                        </View>
                      )}
                    </AtInput>
                  </View>
                </View>
                {nameError.errorMsg && (
                  <View
                    className='kb-color__red kb-size__base kb-margin-sm-b'
                    onClick={this.handleFocus.bind(this, 'focus', 'name')}
                  >
                    *{nameError.errorMsg}
                  </View>
                )}
              </View>
            </View>
          </Fragment>
        )}
        <View className='kb-form__item'>
          <View className='at-col'>
            <View className='at-row'>
              <View className='item-content'>
                <AtInput
                  cursor={-1}
                  value={data.mobile}
                  type='text'
                  disabled={locked.mobile}
                  placeholder='手机号或固话，如都填写中间用空格分开'
                  onChange={this.onChange_form.bind(this, `mobile`)}
                  onBlur={this.handleFocus.bind(this, 'blur', 'mobile')}
                  focus={focusMap.mobile}
                >
                  <View className='kb-edit-manual__phone'>
                    {/* <KbLoginAuth
                      scope='phoneNumber'
                      useOpenType
                      onlyGetPhone
                      showIcon='phone2'
                      text=''
                      linked
                      className='kb-button__mini'
                      size='normal'
                      onAuthComplete={this.handleMiniAppAuthPhone.bind(this)}
                      onClick={this.handleGetPhone}
                    /> */}
                    <AtIcon
                      onClick={this.handleGetPhone}
                      className='kb-color__brand'
                      prefixClass='kb-icon'
                      value='phone2'
                    />
                  </View>
                </AtInput>
              </View>
            </View>
            {mobileError.errorMsg && (
              <View
                className='kb-color__red kb-size__base kb-margin-sm-b'
                onClick={this.handleFocus.bind(this, 'focus', 'mobile')}
              >
                *{mobileError.errorMsg}
              </View>
            )}
          </View>
        </View>
        <View className='kb-form__item kb-clear__form--item'>
          <KbArea
            locked={locked.area}
            value={data}
            onChange={this.onChange_city}
            customStyle={{ paddingLeft: '0', paddingRight: '22px' }}
          />
          {areaError.errorMsg && (
            <View className='kb-color__red kb-size__base kb-spacing-sm-b kb-margin-md-l'>
              *{areaError.errorMsg}
            </View>
          )}
        </View>
        <View className='kb-form__item'>
          <View className='at-col'>
            <View className='at-row'>
              <View className='item-content item-content__edit'>
                <View className='at-row at-row__align--center kb-textarea-wrap'>
                  <View className='at-col'>
                    <KbTextarea
                      placeholder='详细地址(如X栋X单元号)'
                      value={data.address}
                      disabled={locked.address}
                      maxLength={100}
                      count={false}
                      height={process.env.PLATFORM_ENV === 'weapp' ? 0 : 50}
                      // onChange={this.onChange_form.bind(this, `address`)}
                      onChange={this.handleChange.bind(this, `address`)}
                      onBlur={this.handleFocus.bind(this, 'blur', 'address')}
                    />
                  </View>
                  <KbGps auto={!data.address && org == 'send'} onGps={this.onGps} custom>
                    <Image src='https://cdn-img.kuaidihelp.com/qj/miniapp/icon/map.png' />
                  </KbGps>
                </View>
              </View>
            </View>
            {addressError.errorMsg && (
              <View
                className='kb-color__red kb-size__base kb-margin-sm-b'
                onClick={this.handleFocus.bind(this, 'focus', 'address')}
              >
                *{addressError.errorMsg}
              </View>
            )}
          </View>
        </View>
        {(showSaveBtn || showSetDefaultBtn) && (
          <View className='kb-form__item kb-spacing-lg'>
            <View className='item-content'>
              <View className='at-row at-row__justify--between at-row__align--center'>
                <View className='kb-save-address'>
                  {showSaveBtn && (
                    <KbCheckBox
                      className='kb-margin-md-r'
                      checked={data.save == 1}
                      label='保存到地址薄'
                      onChange={this.handleSaveAddress.bind(this)}
                    />
                  )}
                  {showSetDefaultBtn && (
                    <KbCheckBox
                      checked={is_default == 1}
                      label='保存到默认地址'
                      onChange={this.handleSetDefault.bind(this)}
                    />
                  )}
                </View>
                <View>
                  {org === 'send' && (
                    <View
                      className='kb-delete-address'
                      hoverClass='kb-hover'
                      onClick={() => this.formIns.clean()}
                    >
                      <AtIcon
                        prefixClass='kb-icon'
                        className='kb-size__base kb-color__black'
                        value='delete'
                      />
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  showName: true, // 是否修改名称
  locked: {}, //需要锁定的信息;例如{name:"张三"}
  data: {}, // 外部表单数据
  storageKey: '', // 表单缓存key
  org: '', // 地址类型；send寄件/receive收件
  action: '', // 处理地址行为 select/fix完善地址
  showSaveBtn: false, //展示保存选项
  className: '',
  onChange: () => {},
  showTitle: false,
};

export default extendMemo(Index);
