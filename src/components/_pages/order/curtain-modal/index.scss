

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-curtain {
  &--container {
    box-sizing: border-box;
    width: 93%;
    margin: 0 auto;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 9%, $color-white 10%);
    border-radius: 0 0 $border-radius-xl $border-radius-xl;
  }

  &--header {
    position: relative;
    padding: $spacing-h-xl * 2 $spacing-h-xl;
    border-radius: 0 0 45% 45%/0 0 35% 35%;
    border-top-left-radius: $border-radius-lg;
    border-top-right-radius: $border-radius-lg;
    &-desc {
      color: #f5f5f5;
      line-height: $font-size-xl;
    }
    &-bor {
      position: relative;
      display: inline-block;
      width: 100%;
      height: 100%;
      margin-left: $spacing-h-sm;
      padding: $spacing-h-xs;
      text-align: center;
    }
    &-float {
      position: absolute;
      bottom: 30px;
      left: 50%;
      box-sizing: border-box;
      width: 95%;
      padding: $spacing-h-xl * 1.5 $spacing-h-xl * 2 $spacing-h-sm;
      background: red;
      border-radius: $border-radius-lg $border-radius-lg 0 0;

      transform: translateX(-50%);
    }
    &-tag {
      padding: 0 $spacing-h-xs;
      color: $color-white;
      font-size: $font-size-sm;
      background-color: #06c05f;
      border-radius: $border-radius-sm;
    }
  }
  &--body {
    padding: 0 $spacing-h-lg;
  }
  &--footer {
    padding: $spacing-h-xl 0;
  }
}

.kb-background {
  &--blur-linear {
    background: linear-gradient(90deg, #1480ff 0%, #1480ff 0%, #1443ff 100%, #1443ff 100%);
  }
  &--blur-linear-bg {
    background: linear-gradient(
      90deg,
      #7da5d3 0%,
      #1480ff 0%,
      #1443ff 90%,
      #1443ff 96%,
      #7da5d3 100%
    );
  }
  &--grey-linear {
    background: linear-gradient(90deg, #5a6878 0%, #5a6878 0%, #393e55 100%, #393e55 100%);
  }
  &--grey-linear-bg {
    background: linear-gradient(50deg, #5a6878 0%, #5a6878 0%, #393e55 90%, #5a6878 100%);
  }
}
