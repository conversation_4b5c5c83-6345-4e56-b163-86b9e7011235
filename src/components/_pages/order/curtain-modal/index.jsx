/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { AtCurtain } from 'taro-ui';
import { View, Text } from '@tarojs/components';
import KbButton from '@base/components/button';
import classNames from 'classnames';
import { noop } from '@base/utils/utils';
import './index.scss';

const Index = (props) => {
  const { isOpened, onConfirm, onClose, confirmText, head } = props;
  const { headColor = 'grey-linear', title, desc, tag, icon } = head || {};
  const headCls = classNames('kb-curtain--header', `kb-background--${headColor}-bg`);
  const headFloatCls = classNames('kb-curtain--header-float', `kb-background--${headColor}`);
  const iconCls = classNames('kb-image-icon', `kb-image-icon--${icon} `);
  return (
    <AtCurtain isOpened={isOpened} onClose={onClose}>
      {isOpened && (
        <View className='kb-curtain--container'>
          <View className={headCls}>
            <View className={headFloatCls}>
              <View className='at-row at-row__justify--between'>
                <View className='at-col at-col-9'>
                  <View className='kb-spacing-lg-b'>
                    <Text className='kb-spacing-lg-r kb-color__white kb-size__xl'>{title}</Text>
                    {tag && <Text className='kb-curtain--header-tag'>{tag}</Text>}
                  </View>
                  <View className='kb-size__sm kb-curtain--header-desc kb-color__white'>
                    {desc}
                  </View>
                </View>
                {icon && (
                  <View className='at-col at-col-3'>
                    <View className='kb-spacing-sm-l kb-curtain--header-bor'>
                      <View className={iconCls}></View>
                    </View>
                  </View>
                )}
              </View>
            </View>
          </View>
          <View className='kb-curtain--body'>{props.children}</View>
          <View className='kb-curtain--footer'>
            <View className='kb-margin-xl-lr'>
              <KbButton onClick={onConfirm} className='kb-button' circle type='primary'>
                {confirmText}
              </KbButton>
            </View>
          </View>
        </View>
      )}
    </AtCurtain>
  );
};
Index.defaultProps = {
  headColor: 'grey-linear',
  onConfirm: noop,
  onClose: noop,
  confirmText: '确认',
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
