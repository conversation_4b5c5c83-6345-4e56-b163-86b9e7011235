

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-coupon {
  overflow: hidden;
  border-radius: $border-radius-lg;

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    display: block;
    width: 100%;
    height: 20px;
    background: linear-gradient(to right, #ff5567, #ffe284);
    content: '';
  }

  .coupon-info {
    position: relative;
    display: flex;
    align-items: center;
    padding: $spacing-v-md $spacing-h-xxl;
    overflow: hidden;
    background-color: $color-white;
    border-radius: 0 0 $border-radius-lg $border-radius-lg;

    &::before,
    &::after {
      position: absolute;
      top: 50%;
      z-index: 1;
      display: block;
      width: 30px;
      height: 30px;
      background-color: currentColor;
      border-radius: $border-radius-circle;
      transform: translateY(-50%);
      content: '';
    }

    &::before {
      left: -15px;
    }

    &::after {
      right: -15px;
    }

    &__cost {
      padding-right: $spacing-h-md;
      color: $color-red;
      font-size: $font-size-xxl;

      &::before {
        font-size: $font-size-sm;
        vertical-align: middle;
        content: '￥';
      }
    }

    &__content {
      flex-grow: 1;
      color: $color-grey-1;
      font-size: $font-size-base;

      &--item {
        margin-bottom: $spacing-v-sm;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  &__border {
    position: relative;

    &::after {
      position: absolute;
      top: 20px;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 0;
      border: $border-lightest;
      border-top: 0;
      border-radius: 0 0 $border-radius-lg $border-radius-lg;
      content: '';
    }

    .coupon-info {
      &::before,
      &::after {
        border: $border-lightest;
      }
    }
  }
}
