/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { formatNumeral } from '@/components/_pages/order/_utils/order.pay';
import request from '@base/utils/request';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const [isOpened, updateIsOpened] = useState(false);
  const [coupon, updateCoupon] = useState({});

  useEffect(() => {
    if (data && data.coupon_id) {
      const { phone, coupon_id, index_shop_id } = data;
      request({
        url: '/g_wkd/v1/WeApp/createCustomCoupon',
        data: { phone, coupon_id, index_shop_id },
        toastLoading: false,
        toastError: true,
        onThen({ data, code }) {
          if (code == 0 && data) {
            data.cost = data.cost ? formatNumeral(data.cost) : '0.00';
            updateCoupon(data);
            updateIsOpened(true);
          }
        },
      });
    }
  }, [data]);

  const handleClose = () => {
    updateIsOpened(false);
  };

  const handleToCard = () => {
    Taro.navigator({
      url: 'order/card',
    });
  };

  return (
    <AtCurtain isOpened={isOpened} onClose={handleClose} closeBtnPosition='bottom-center'>
      <View className='kb-scan-coupon'>
        <View className='kb-scan-coupon--fee'>{coupon.cost || 0}</View>
        <View className='kb-button kb-scan-coupon--btn' onClick={handleToCard}>
          去看看{'>'}
        </View>
      </View>
    </AtCurtain>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {}, //二维码数据信息
};

export default Index;
