

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-scan-coupon {
  position: relative;
  width: 544px;
  height: 604px;
  margin: 0 auto;
  color: rgb(241, 16, 46);
  background: url(https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/scan-coupon.png?v=2) no-repeat
    center top / 100% auto;
  &--fee {
    &::before {
      font-size: $font-size-base;
      content: '¥';
    }
    position: absolute;
    top: 240px;
    left: 50%;
    font-size: $font-size-xxl * 1.5;
    text-align: center;
    transform: translateX(-50%);
  }
  &--btn {
    position: absolute;
    bottom: 60px;
    left: 50%;
    z-index: 999;
    display: inline-block;
    padding: 20px 100px;
    letter-spacing: 6px;
    white-space: nowrap;
    background-image: linear-gradient(to right, rgb(255, 245, 198), rgb(255, 223, 107));
    border: none;
    border-radius: $border-radius-arc;
    transform: translateX(-50%);
  }
}
