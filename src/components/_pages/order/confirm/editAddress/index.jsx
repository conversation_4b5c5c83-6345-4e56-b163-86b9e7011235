/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbFloatLayout from '@base/components/floatLayout';
import { AtInput } from 'taro-ui';
import { noop, extractData } from '@base/utils/utils';
import { check } from '@base/utils/rules';
import KbGps from '@base/components/gps';
import debounce from 'lodash/debounce';
import KbArea from '@/components/_pages/order/address-edit/area';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    data: {},
    onClose: noop,
    onChange: noop,
    isOpened: false,
  };

  static options = {
    addGlobalClass: true,
  };

  constructor() {
    this.state = {
      addressData: {},
    };
    this.onFieldChange = debounce(this.onFieldChange, 300, { leading: false, trailing: true });
    this.onGps = debounce(this.onGps, 500, { leading: true, trailing: false });
  }

  onHandel = (action) => {
    const { addressData } = this.state;
    const { onClose, onChange } = this.props;
    if (action === 'confirm') {
      const { code, msg } = check('contact', addressData.phone);
      const isContact = code == 0;
      if (!isContact) {
        Taro.kbToast({
          text: msg,
        });
        return;
      }
      if (!addressData.detail) {
        Taro.kbToast({
          text: '请输入详细地址',
        });
        return;
      }
      if (!addressData.city) {
        Taro.kbToast({
          text: '请选择省市区',
        });
        return;
      }
      const data = extractData(addressData, [
        ['shipper_province', 'province'],
        ['shipper_city', 'city'],
        ['shipper_district', 'district'],
        ['shipper_address', 'detail'],
        ['shipper_mobile', 'phone'],
      ]);
      onChange(data);
    }
    onClose(false);
  };

  onFieldChange = (data) => {
    this.setState((prevState) => {
      return {
        addressData: {
          ...prevState.addressData,
          ...data,
        },
      };
    });
  };

  onGps = (data) => {
    const { province, city, district, address } = data;
    this.onFieldChange({ province, city, district, detail: address });
  };

  render() {
    const { addressData } = this.state;
    const { isOpened } = this.props;
    return (
      <KbFloatLayout isOpened={isOpened} onClose={this.onHandel.bind(this, 'cancel')}>
        <View className='kb-float-layout__bars at-row__justify--between kb-margin-xl-lr'>
          <View
            className='layout-bars__cancel'
            hoverClass='kb-hover'
            onClick={this.onHandel.bind(this, 'cancel')}
          >
            取消
          </View>
          <View
            className='layout-bars__confirm'
            hoverClass='kb-hover'
            onClick={this.onHandel.bind(this, 'confirm')}
          >
            确定
          </View>
        </View>

        <View className='kb-float-layout-wrap'>
          <View className='kb-box kb-margin-md'>
            <View className='kb-form kb-margin-xl-b'>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>手机号码</View>
                <View className='item-content kb-margin-lg-l'>
                  <AtInput
                    type='number'
                    cursorSpacing={50}
                    value={addressData.phone}
                    placeholder='手机号或固话'
                    onChange={(phone) => this.onFieldChange({ phone })}
                    alwaysEmbed
                  />
                </View>
              </View>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>省市区</View>
                <View className='item-content'>
                  <KbArea className='kb-areas' value={addressData} onChange={this.onFieldChange} />
                </View>
              </View>
              <View className='kb-form__item kb-form__item--name'>
                <View className='kb-form__title'>详细地址</View>
                <View className='item-content kb-margin-lg-l'>
                  <AtInput
                    cursorSpacing={50}
                    value={addressData.detail}
                    placeholder='街道门牌、楼层房间号等信息'
                    onChange={(detail) => this.onFieldChange({ detail })}
                    alwaysEmbed
                  />
                </View>
                <View className='item-extra item-extra__line kb-spacing-md-r'>
                  <KbGps auto={false} onGps={this.onGps} iconColor='grey' />
                </View>
              </View>
            </View>
          </View>
        </View>
      </KbFloatLayout>
    );
  }
}

export default Index;
