/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { View, Text, Image } from '@tarojs/components';
import { noop, getCurrentUser } from '@base/utils/utils';
import { extendMemo } from '@base/components/_utils';
import { setClipboardData } from '@/utils/qy';
import KbCheckbox from '@base/components/checkbox';
import KbToolBar from '@/components/_pages/order/tool-bar/index2';
import classNames from 'classnames';
import KbNumber from '@base/components/number';
import KbExceptionSignOrder from './exception-sign-order';
import './list-item.scss';

const Index = (props) => {
  const {
    hasSelected,
    checked,
    data: item,
    onClick,
    onUpdate,
    orderType,
    orderDate,
    brands,
    isAdmin,
    tabKey,
    showToolBar,
    batchNoSearched,
    filterKey,
    source,
  } = props;
  const { associated_order } = item;
  const _showToolBar = associated_order != 1 && showToolBar;

  const handleClickItem = (e) => {
    if (item.associated_order == '1') return;
    onClick(e, item);
  };
  const handleUpdate = (action) => onUpdate(action, item);
  const handleCopy = (waybill) => {
    setClipboardData(waybill, '运单号已复制');
  };

  let sBrand = '';
  let brandName = '';
  let cityCls = '';
  let brandItem = {};
  if (item) {
    sBrand = item.brand || item.sourceBrand || 'other';
    brandItem = sBrand && brands ? brands[sBrand] || {} : {};
    const { name } = brands[sBrand] || {};
    brandName = name;
    cityCls = classNames('kb-order__info--city kb-size__xl', {
      'kb-color__grey': item.order_state === 'canceled',
    });
  }
  const showCheckBox =
    associated_order != 1 &&
    hasSelected &&
    (source === 'abnormal' ? true : tabKey === 'all' ? true : item.order_status == 1);
  const batchNoCls = classNames('kb-order__batch--no kb-size__sm', {
    'kb-order__batch--no--selected': batchNoSearched,
  });

  const handleBatchNoClick = () => {
    const { batch_number } = item;
    onUpdate('batchNo', { batch_number: batchNoSearched ? '' : batch_number, batchNoSearched });
  };

  const cantSelect = (key, item) => {
    if (item.associated_order == '1') return true;
    if (key == 'delete_order') {
      // 已取消 || 已签收且已结清
      return !(item.order_status == 0 || (item.logistic_status == 3 && item.pay_status == 2));
    }
    if (key == 'mark_order') {
      return item.pay_status != 1;
    }
    if (key == 'cancel_order') {
      return !(item.logistic_status_txt == '已下单' || item.logistic_status_txt == '已受理');
    }
    if (key == 'remind_order') {
      // 已取消 || 已结清
      return item.order_status == 0 || item.pay_status == 2;
    }
    return item.order_status == 0;
  };

  const handleShowQuestionToast = (e) => {
    e.stopPropagation();
    Taro.kbToast({
      text: `自主寄件订单，不返佣金`,
    });
  };

  const isCustomF = getCurrentUser('custom-f');
  const isYiwu = getCurrentUser('yiwu');

  return (
    <Fragment>
      <View
        hoverClass='kb-hover'
        onClick={handleClickItem}
        onLongPress={handleClickItem}
        className={classNames('kb-list__item--box', {
          'kb-list__item--box--disabled': hasSelected && cantSelect(filterKey, item),
        })}
      >
        {item.order_mark === 'cngg' && <View className='kb-cngg-tag'>智能分配</View>}
        <View className='kb-list__item kb-order__brand'>
          <View className='item-icon'>
            <Image src={brandItem.logo_link} className='item-icon__img' />
          </View>
          <View className='item-content'>
            <View className='item-content__title'>
              <View className='at-row at-row__align--center at-row__justify--between'>
                <View>
                  {item.waybill ? (
                    <View
                      hoverStopPropagation
                      hoverClass='kb-hover-opacity'
                      onClick={handleCopy.bind(null, item.waybill, (e) => e.stopPropagation())}
                      className='kb-size__base at-row at-row__align--center'
                    >
                      <Text className='kb-margin-sm-r'>{brandName}</Text>
                      <Text className='kb-icon__text--mr'>{item.waybill}</Text>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='copy-text'
                        className='kb-icon-size__sm'
                      />
                    </View>
                  ) : (
                    <Text className='kb-color__grey'>暂无单号</Text>
                  )}
                </View>
              </View>
            </View>
          </View>
          {item.associated_order == '1' ? (
            (item.regiment_profit > 0 || isCustomF) && (
              <View className='item-extra kb-color__green'>
                <Text className='kb-size__xs'>￥</Text>
                <KbNumber fontsize={[34, 20]} number={item.regiment_profit || '0.00'} />
              </View>
            )
          ) : isCustomF ? (
            <View className='item-extra kb-color__green'>
              <Text className='kb-size__xs'>￥</Text>
              <KbNumber fontsize={[34, 20]} number={item.regiment_profit || '0.00'} />
            </View>
          ) : item.is_admin == 1 && (item.pay_method == 2 || item.pay_method == 1) ? (
            <Fragment>
              <View
                className='item-extra kb-color__red'
                hoverStopPropagation
                hoverClass='kb-hover-opacity'
                onClick={handleShowQuestionToast}
              >
                <View className='at-row at-row__align--center'>
                  <Text className='kb-size__xs'>￥</Text>
                  <KbNumber fontsize={[34, 20]} number='0.00' />
                  <AtIcon
                    prefixClass='kb-icon'
                    value='question'
                    className='kb-color__black kb-size__sm'
                  />
                </View>
              </View>
            </Fragment>
          ) : (
            <Fragment>
              {item.pay_status == 1 && item.wait_pay_price && (
                <View className='item-extra kb-color__red'>
                  <Text className='kb-size__xs'>￥</Text>
                  <KbNumber fontsize={[34, 20]} number={item.wait_pay_price} />
                </View>
              )}
              {item.regiment_profit > 0 &&
                item.pay_status == 2 &&
                item.order_status >= 2 &&
                isAdmin && (
                  <View className='item-extra kb-color__green'>
                    <Text className='kb-size__xs'>￥</Text>
                    <KbNumber fontsize={[34, 20]} number={item.regiment_profit} />
                  </View>
                )}
            </Fragment>
          )}
        </View>
        <View className='kb-list__item kb-order__info'>
          {showCheckBox && (
            <View className='item-checkbox'>
              <KbCheckbox
                disabled={cantSelect(filterKey, item)}
                checked={checked}
                onChange={handleClickItem}
              />
            </View>
          )}
          <View className='item-content'>
            {!item.incomplete ? (
              <Fragment>
                <View className='at-row at-row__align--center at-row__justify--between kb-margin-md-b'>
                  <View className='kb-order__info kb-text__left'>
                    <View className={cityCls}>{item.shipper_city}</View>
                    <View className='kb-order__info--name'>{item.shipper_name}</View>
                  </View>
                  <View className='kb-order__sign'>
                    <View>
                      {item.logistic_status_txt && (
                        <Text className='kb-order__sign--text kb-size__sm'>
                          {item.logistic_status_txt}
                        </Text>
                      )}
                    </View>
                    <View className='kb-order__sign--longArrow at-row at-row__align--end at-row__justify--center'>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='long-arrow2'
                        className='kb-color__black'
                        size={7}
                      />
                    </View>
                    <KbExceptionSignOrder data={item} />
                  </View>
                  <View className='kb-order__info kb-text__right'>
                    <View className={cityCls}>{item.shipping_city}</View>
                    <View className='kb-order__info--name'>{item.shipping_name}</View>
                  </View>
                </View>
                <View className='item-extraBox'>
                  <View className='kb-size__base item-line'>
                    <View className='at-row at-row__align--center at-row__justify--between'>
                      <View>下单时间：{item.create_at}</View>
                      {item.isDiscount && (
                        <View className='kb-tag__red kb-size__sm kb-color__white kb-spacing-sm-lr'>
                          {item.isDiscount}
                        </View>
                      )}
                    </View>
                  </View>
                  {item.last_logistics && (
                    <View className='kb-size__base item-line item-line'>
                      {brandName && (
                        <Text className='kb-color__grey kb-order__fix--l'>【{brandName}】</Text>
                      )}
                      <Text>{item.last_logistics}</Text>
                    </View>
                  )}
                  {!isCustomF && item.can_label == 0 && item.regiment_remark && (
                    <View className='kb-size__base item-line kb-order__remark'>
                      <Text className='kb-order__remark--icon kb-size__sm'>团员</Text>
                      <Text className='kb-spacing-xs-lr'>
                        {decodeURIComponent(item.regiment_remark)}
                      </Text>
                    </View>
                  )}
                  {isCustomF ? (
                    <View className='item-line'>
                      {item.label && <View className='order-role'>{item.label}</View>}
                      {item.promotion_type && (
                        <View className='order-promotion_type'>{item.promotion_type}</View>
                      )}
                      {item.user_nickname && (
                        <View className='order-nickname'>{item.user_nickname}</View>
                      )}
                    </View>
                  ) : null}
                  <View className='item-line'>
                    {associated_order != 1 &&
                      getCurrentUser('regiment') &&
                      !['1', '2'].includes(item.pay_method) &&
                      (item.estimate_profit || item.regiment_profit) && (
                        <View className='kb-size__base kb-order__fee'>
                          <Text className='kb-spacing-xs-lr'>
                            {item.logistic_status != 3 ? '预估' : ''}返佣金额: ￥
                            {item.logistic_status != 3
                              ? item.estimate_profit
                              : item.regiment_profit}
                          </Text>
                        </View>
                      )}
                    {item.freight && (
                      <View className='kb-size__base kb-order__fee'>
                        <Text className='kb-spacing-xs-lr'>运费: ￥{item.freight}</Text>
                      </View>
                    )}
                    {isCustomF && (
                      <View className='kb-size__base kb-order__fee'>
                        <Text className='kb-spacing-xs-lr'>计费重量: {item.charging_weight}KG</Text>
                      </View>
                    )}
                  </View>
                  {associated_order == 1 && isYiwu && (
                    <View className='item-line kb-list__item-relation'>
                      {item.label ? (
                        <View className='kb-list__item-relation-item'>
                          <View className='tag tag-primary'>{item.label}</View>
                          <View className='tag'>
                            {item.label.includes('货代企业')
                              ? '加盟商'
                              : item.label.includes('企业销售')
                              ? '团长'
                              : '团员'}
                          </View>
                        </View>
                      ) : null}
                      {item.user_nickname ? (
                        <View className='kb-list__item-relation-item'>{item.user_nickname}</View>
                      ) : null}
                      {item.superior_name ? (
                        <View className='kb-list__item-relation-item'>
                          所属上级：{item.superior_name}
                        </View>
                      ) : null}
                    </View>
                  )}
                </View>
              </Fragment>
            ) : (
              <Text>
                {orderType === 'send' ? '收' : '发'}
                件人信息暂未提交
              </Text>
            )}
          </View>
        </View>
      </View>
      {!hasSelected && _showToolBar && (
        <View className='kb-order__toolbar'>
          <View className='at-row at-row__align--center at-row__justify--between'>
            <View>
              {isAdmin && item.batch_number && (
                <View
                  onClick={handleBatchNoClick}
                  hoverClass='kb-hover-opacity'
                  className='kb-order__batch at-row at-row__align--center'
                >
                  <View className={batchNoCls}>{item.batch_number}</View>
                  <View className='kb-order__batch--total'>
                    <Text className='kb-size__md'>{item.batch_no_current}</Text>
                    <Text className='kb-size__xs kb-color__grey'>/{item.batch_number_count}</Text>
                  </View>
                </View>
              )}
            </View>
            <KbToolBar
              source='orderList'
              className='kb-toolbar__orderList'
              tabKey={tabKey}
              orderType={orderType}
              orderDate={orderDate}
              data={item}
              onUpdate={handleUpdate}
              isAdmin={isAdmin}
            />
          </View>
        </View>
      )}
    </Fragment>
  );
};

Index.defaultProps = {
  data: {},
  brands: {},
  orderType: 'send',
  orderDate: '',
  onClick: noop,
  onUpdate: noop,
  hasSelected: false,
  checked: false,
  showToolBar: true,
  batchNoSearched: false,
};

Index.options = {
  addGlobalClass: true,
};

export default extendMemo(Index, [
  'data',
  'brands',
  'hasSelected',
  'checked',
  'batchNoSearched',
  'filterKey',
]);
