/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-param-reassign */
import { extractData, dateCalendar, getCurrentUser } from '@base/utils/utils';
import request from '@base/utils/request';
import numeral from 'numeral';
import apis from '@/utils/apis';
import { requestPayment } from '@/utils/qy';
import isNumber from 'lodash/isNumber';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
// import { phoneDesensitization } from '@/components/_pages/_utils';
import merge from 'lodash/merge';

/**
 * 时间格式化
 *  */
export const formatSectionTimer = (start, end) => {
  if (!start || !end) return [];
  const formatStart = dateCalendar(start, { timer: true });
  const formatEnd = dateCalendar(end, { timer: true });
  let data = '',
    startTime = '',
    endTime;
  data = formatStart.split(' ')[0];
  startTime = formatStart.split(' ')[1].split(':')[0];
  endTime = formatEnd.split(' ')[1].split(':')[0];

  return `${data} ${startTime}:00 ~ ${endTime}:00`;
};

/**
 * 创建按钮列表
 * @param data 订单详细数据
 *  */
export const createBar = (orderData, isAdmin, tabKey) => {
  const { order_status, pay_status } = orderData || {};
  // 待确认订单
  const unConfirm = order_status == 1;

  const canceled = order_status == 0;

  let bars = '';

  if (unConfirm) {
    bars = [
      {
        key: 'cancel',
        label: '取消订单',
      },
    ];
    if (isAdmin) {
      bars.push({
        key: 'confirm',
        label: '确认订单',
      });
    }
  }

  if (tabKey == 'all' && !canceled) {
    bars = [
      {
        key: 'print',
        label: '打印面单',
      },
    ];
    // 待支付、已结清订单不能取消
    if (pay_status == 1) {
      bars.push({
        key: 'cancel',
        label: '取消订单',
      });
    }
  }

  return bars;
};

/**
 *
 * @description 支付状态
 */
export const formatStatus = (status) => {
  const statusMap = {
    0: {
      text: '待结算',
      color: 'yellow',
      key: 0,
    },
    1: {
      text: '待补款',
      color: 'red',
      key: 1,
    },
    2: {
      text: '已结清',
      color: 'green',
      key: 2,
    },
  };

  return statusMap[status];
};

// 创建退款相关结果
export function createRefundResult({ refund_status }) {
  // 退款状态处理
  const refund_statusMap = {
    refund: {
      tips: '申请退款',
    },
    courierRefunding: {
      tips: '快递员审核中',
      label: '退款中',
      content: '您的退款申请已提交，等待快递员处理',
    },
    courierNotFunds: {
      tips: '快递员同意退款但余额不足',
      content: '您的退款申请快递员已同意，但快递员账户余额不足，请联系快递员',
    },
    custom: {
      tips: '联系客服处理',
      label: '已拒绝',
      content: '您的退款申请已被快递员拒绝，若有异议，请联系客服处理',
    },
    customRefunding: {
      tips: '客服审核中',
      label: '退款中',
      content: '您的退款申请已提交客服，等待客服核实处理',
    },
    customRefund: {
      tips: '客服拒绝',
      label: '已拒绝',
      content: '您的退款申请经客服核实，不满足退款要求，已被拒绝',
    },
    refundFail: {
      tips: '客服同意但退款失败',
      content: '快递员账户余额不足，请联系快递员',
    },
    refunding: {
      tips: '退款成功但未到账',
      label: '已退款',
      content: '您已退款成功，预计1-3天内退回原账户，请以银行实际入账时间为准，感谢您使用微快递',
    },
    refundSuccess: {
      tips: '退款成功且已到账',
      label: '已退款',
    },
  };
  const refundResult = ((data) => (status) => {
    const result = data[`${status}`];
    return result
      ? {
          label: '退款失败',
          ...result,
        }
      : null;
  })(refund_statusMap)(refund_status);
  return refundResult;
}

/**
 *
 * @description 兼容订单详情
 * @param {*} item
 * @returns
 */
export const fixOrderData = (orderDetail) => {
  // 增加二次结算订单数据修正逻辑
  if (orderDetail) {
    orderDetail.isKdgOrder = !isEmpty(orderDetail.cabinet_record);
  }
  if (orderDetail && !isEmpty(orderDetail.order_fee_repeat)) {
    return merge({}, orderDetail, orderDetail.order_fee_repeat, { order_fee_first: orderDetail });
  }
  return orderDetail;
};
export const formatOrderDetail = (item) => {
  const transFormKey = [
    'order_number',
    'user_id',
    'brand',
    'waybill',
    'delivery_type',
    'order_status',
    'pay_status',
    'logistic_status',
    'logistic_status_txt',
    'package_weight',
    'package_info',
    'package_note',
    'charging_weight',
    'place_volume',
    'settlement_volume',
    'f_fee',
    'f_kg',
    's_fee',
    'regiment_estimate_price',
    'regiment_wait_pay_price',
    'other_money',
    'pay_price',
    'wait_pay_price',
    'claiming_value',
    'warrant_price',
    'real_claiming_value',
    'real_warrant_price',
    'calculate_price_type',
    'collect_courier_mobile',
    'collect_courier_name',
    'reserve_start_time',
    'reserve_end_time',
    'reserve_time_desc',
    'create_at',
    'regiment_remark',
    'freight',
    'can_push_pay',
    'regiment_pay_price',
    'change_shipper_address_order',
    'order_package_pics',
    'regiment_profit',
    'arrive_pay',
    'settlement_price_details',
    'third_create_at',
    'pay_method',
    'pay_type',
    // [
    //   'courier',
    //   ({ courier }) => {
    //     return !isEmpty(courier)
    //       ? {
    //         name: courier.name,
    //         phone: phoneDesensitization(courier.phone),
    //         account_type: courier.account_type == 'inn' ? '驿站' : '快递员',
    //       }
    //       : null;
    //   },
    // ],
    ['send_name', 'shipper_name'],
    ['send_tel', 'shipper_tel'],
    ['send_mobile', 'shipper_mobile'],
    ['send_province', 'shipper_province'],
    ['send_city', 'shipper_city'],
    ['send_county', 'shipper_county'],
    ['send_district', 'shipper_county'],
    ['send_address', 'shipper_address'],
    ['receive_name', 'shipping_name'],
    ['receive_tel', 'shipping_tel'],
    ['receive_mobile', 'shipping_mobile'],
    ['receive_province', 'shipping_province'],
    ['receive_city', 'shipping_city'],
    ['receive_county', 'shipping_county'],
    ['receive_district', 'shipping_county'],
    ['receive_address', 'shipping_address'],
    'pickup_code',
    'order_fee_repeat',
    'freightBase',
    'is_partner_order',
    'channel_type',
    'courier',
    'cabinet_record',
    ['isKdgOrder', ({ cabinet_record }) => !isEmpty(cabinet_record)],
    [
      'change_address',
      ({ change_address, shipper_name }) => {
        return change_address && change_address.shipper_address
          ? extractData(change_address, [
              ['pickup_name', () => shipper_name],
              ['pickup_tel', 'shipper_tel'],
              ['pickup_mobile', 'shipper_mobile'],
              ['pickup_province', 'shipper_province'],
              ['pickup_city', 'shipper_city'],
              ['pickup_county', 'shipper_county'],
              ['pickup_district', 'shipper_county'],
              ['pickup_address', 'shipper_address'],
            ])
          : null;
      },
    ],
    'order_mark',
    'is_exception_sign_order',
    'delivery_type_name',
    'brand_type',
    'welfare_type',
    'welfare_money',
    'is_admin',
    'order_send_log',
    'associated_order',
    'modify_time',
  ];

  const orderDetail = extractData(item, transFormKey);
  return fixOrderData(orderDetail);
};

/**
 * 订单详情、订单列表页脚
 *  */
export const createOrderDetailBar = (data) => {
  const {
    order_status,
    pay_status,
    isAdmin,
    can_push_pay,
    pay_method,
    tabKey,
    order_number,
    isKdgOrder,
    cabinet_record,
  } = data;
  const is_admin = isAdmin && JSON.parse(isAdmin);
  const is_kop = getCurrentUser('kop');
  const cancelButton = {
    key: 'cancel',
    label: '取消订单',
    type: 'normal',
  };
  const confirmButton = {
    key: 'confirm',
    label: '确认订单',
    type: 'normal',
  };
  const payButton = {
    key: 'pay',
    label: '立即补款',
    type: 'normal',
  };
  const markPayed = {
    key: 'markPayOpen',
    label: '标记已付',
    type: 'normal',
  };
  const sharePay = {
    key: 'share',
    label: '推送支付',
    type: 'normal',
  };
  const print = {
    key: 'print',
    label: '打印面单',
    type: 'normal',
  };
  const clone = {
    key: 'clone',
    label: '再来一单',
    type: 'normal',
  };
  const proofImage = {
    key: 'certificate',
    label: '查看底单',
    type: 'normal',
  };
  const cancel_cabinet_order = {
    key: 'cancel_cabinet_order',
    label: '取消订单',
    type: 'normal',
  };
  const open_cabinet = {
    key: 'open_cabinet',
    label: '开柜',
    type: 'primary',
  };
  const remind_order = {
    key: 'remind_order',
    label: '短信催收',
    type: 'normal',
  };
  let bars = [];

  // 快递柜订单取消后不显示操作按钮
  if (isKdgOrder && order_status == 0) {
    return [];
  }

  // 快递柜异常情况---未存柜
  // console.log('data===>', data, tabKey);
  if (isKdgOrder && cabinet_record && !cabinet_record.into_cabinet) {
    return tabKey ? [] : [cancel_cabinet_order, open_cabinet];
  }

  if (!order_number || (!data.pay_status_info && !tabKey)) {
    return [clone];
  }

  // 已取消订单，已结清
  if (order_status == 0) {
    return [clone];
  }

  if (pay_status == 2) {
    bars = [print, clone];
    bars.push(proofImage);
    return bars;
  }

  // 待结算，(已发货，已退款不显示取消按钮)
  if (pay_status == 0 && order_status && !['4', '5'].includes(`${order_status}`)) {
    bars = [cancelButton];
  }
  bars.push(print);

  // 待补款，到付、寄付现结和支付分隐藏支付按钮（1支付分 2月结  3到付  4现付）
  if (pay_status == 1 && ['1', '2'].includes(`${pay_method}`) && !tabKey) {
    if (is_admin) {
      bars = [markPayed];
      if (pay_method == 1) {
        bars.push(payButton);
      }
    } else {
      bars = [payButton];
    }
    if (is_kop && is_admin) {
      bars.push(remind_order);
    }

    if (can_push_pay == 1) {
      bars.push(sharePay);
    }
    if (!bars.some((v) => v.key === 'print')) {
      bars.push(print);
    }
    bars.push(clone);
    bars.push(proofImage);
    return bars;
  }

  // 待确认
  if (order_status == 1) {
    if (is_admin) {
      bars = [cancelButton, confirmButton, clone];
    } else {
      bars = [cancelButton, clone];
    }
  }
  if (!bars.some((v) => v.key === 'clone')) {
    bars.push(clone);
  }
  if (!bars.some((v) => v.key === 'certificate')) {
    bars.push(proofImage);
  }

  // 订单页屏蔽取消按钮
  if (tabKey) {
    bars = bars.filter((v) => !['certificate', 'cancel'].includes(v.key));
    if (tabKey == 'all' || tabKey == 'waitPay') {
      return bars.filter((v) => v.key != 'confirm');
    }
  }

  return bars;
};

/**
 * 格式化订单详情数据
 * @returns
 */
export const formatResponseOrderDetail = (data = {}, isAdmin) => {
  const payMethodMap = {
    0: '寄付月结',
    1: '到付',
    2: '寄付现结',
  };

  const formatData = formatOrderDetail(data);
  if (!data.order_number) {
    return { data: void 0 };
  }
  // 测试数据
  // formatData.charging_weight = '2';
  // formatData.pay_status = 2;
  // formatData.pay_price = 6;
  // formatData.regiment_profit = 4.23;
  // formatData.wait_pay_price = 5.87;
  // formatData.regiment_remark = '测试一二三四五六七'

  const {
    order_number,
    pay_status,
    courier,
    order_fee_first,
    cabinet_record,
    isKdgOrder,
    is_exception_sign_order,
    brand_type,
    order_send_log,
    modify_time,
  } = formatData;
  const statusInfos = {
    logistic_status_txt: formatData.logistic_status_txt,
    logistic_status: formatData.logistic_status,
    order_status: formatData.order_status,
    pickup: formatSectionTimer(formatData.reserve_start_time, formatData.reserve_end_time),
    reserve_time_desc: formatData.reserve_time_desc,
    create_at: dateCalendar(formatData.create_at, { timer: true }),
    remark: formatData.regiment_remark,
    collect_courier_name: formatData.collect_courier_name,
    collect_courier_mobile: formatData.collect_courier_mobile,
    user_id: isAdmin == 1 ? formatData.user_id : '',
    courier,
    pickup_code: isKdgOrder ? cabinet_record.pickup_code : formatData.pickup_code,
    channel_type: formatData.channel_type,
    isKdgOrder,
    cabinet_record,
    is_exception_sign_order,
    modify_time,
  };

  const brandInfo = {
    delivery_type: formatData.delivery_type,
    brand: formatData.brand,
    waybill: formatData.waybill,
    order_number: formatData.order_number,
    is_partner_order: formatData.is_partner_order,
    arrive_pay: formatData.arrive_pay,
    order_mark: formatData.order_mark,
    delivery_type_name: formatData.delivery_type_name,
    brand_type,
  };

  const addressInfo = {
    send_name: formatData.send_name,
    send_tel: formatData.send_tel,
    send_mobile: formatData.send_mobile,
    send_province: formatData.send_province,
    send_city: formatData.send_city,
    send_county: formatData.send_county,
    send_address: formatData.send_address,
    receive_name: formatData.receive_name,
    receive_tel: formatData.receive_tel,
    receive_mobile: formatData.receive_mobile,
    receive_province: formatData.receive_province,
    receive_city: formatData.receive_city,
    receive_county: formatData.receive_county,
    receive_address: formatData.receive_address,
    change_shipper_address_order: formatData.change_shipper_address_order,
    change_address: formatData.change_address,
  };

  const goodsInfo = {
    list: [
      {
        icon: 'inPay',
        text: payMethodMap[formatData.arrive_pay],
      },
      {
        icon: 'shop',
        text: formatData.package_info,
      },
      {
        icon: 'goods',
        text:
          formatNumeral(formatData.package_weight, 'kg', '--') +
          ' / ' +
          formatNumeral(formatData.place_volume, 'cm³', '--'),
      },
      {
        icon: 'fee',
        text: formatNumeral(formatData.warrant_price, '元', '--'),
      },
    ],
    package_note: formatData.package_note,
    package_pics: formatData.order_package_pics || [],
    create_at: dateCalendar(formatData.create_at, { timer: true }),
    order_id: formatData.order_number,
    third_create_at: formatData.third_create_at,
  };

  const transformNum = (num) => {
    isNumber(num * 1000) ? num * 1000 : 0;
  };

  const floatAdd = (num, num2) => {
    return (transformNum(num) + transformNum(num2)) / 1000;
  };

  const getTotalFee = () => {
    if (pay_status == 2) {
      if (isAdmin) {
        return formatNumeral(formatData.regiment_pay_price, '元', '--');
      } else {
        return formatNumeral(formatData.pay_price, '元', '--');
      }
    } else {
      if (isAdmin) {
        return floatAdd(formatData.regiment_estimate_price, formatData.regiment_wait_pay_price);
      } else {
        return formatData.wait_pay_price;
      }
    }
  };

  const formatFeeDetail = (feeDetail = []) => {
    const packageFeeIndex = feeDetail.findIndex((i) => i.name.includes('包装'));
    if (packageFeeIndex > -1 && feeDetail.length > 0) {
      feeDetail[packageFeeIndex].showPackageFee = true;
    }
    return feeDetail;
  };

  const payInfo = {
    order_status: formatData.order_status,
    pay_status,
    logistic_status: formatData.logistic_status,
    can_push_pay: formatData.can_push_pay,
    pay_status_info: formatStatus(pay_status),
    charging_weight: formatNumeral(formatData.charging_weight, '', '--'),
    settlement_volume: formatNumeral(formatData.settlement_volume, '', '--'),
    real_claiming_value: formatNumeral(formatData.real_claiming_value, '', '--'),
    real_warrant_price: formatNumeral(formatData.real_warrant_price, '', '--'),
    other_money: formatNumeral(formatData.other_money, '', '--'), // 包装费
    pay_price: formatData.pay_price, // 已支付
    wait_pay_price: formatData.wait_pay_price, // 待支付
    freight: formatNumeral(formatData.freight, '', '--'),
    totalFee: getTotalFee(),
    f_fee: formatData.f_fee,
    f_kg: formatData.f_kg,
    s_fee: formatData.s_fee,
    regiment_profit: formatData.regiment_profit,
    calculate_price_type: formatData.calculate_price_type,
    settlement_price_details: isArray(formatData.settlement_price_details)
      ? formatFeeDetail(formatData.settlement_price_details)
      : [],
    pay_method: formatData.pay_method,
    pay_type: formatData.pay_type,
    freightBase: formatData.freightBase,
    welfare_money: formatData.welfare_money,
    welfare_type: formatData.welfare_type,
    is_admin: formatData.is_admin,
    order_fee_first,
  };

  const sendInfo = order_send_log;

  return {
    data: {
      statusInfos,
      order_number,
      brandInfo,
      addressInfo,
      goodsInfo,
      payInfo,
      sendInfo,
      formatOrderData: formatData,
    },
  };
};

// 格式化数字
export const formatNumeral = (val, unit = '元', defaultValue) => {
  if ((!val || val == 0) && defaultValue) {
    val = defaultValue;
  } else {
    val = numeral(val || 0).format('0.00');
  }
  return `${val}${unit}`;
};

/**
 * @description 获取关系头像
 */
export const getRelationAvatar = ({ id, type, orderType }) => {
  return id
    ? `https://upload.kuaidihelp.com/touxiang/counterman_${id}.jpg`
    : orderType == 'tcjs'
    ? `https://upload.kuaidihelp.com/touxiang/counterman_.jpg`
    : `https://cdn-img.kuaidihelp.com/brand_logo/icon_${type}.png?t=20230316`;
};

/**
 * 支付
 * @param order_id 订单id
 *  */
export const payOrder = (data = {}) => {
  return new Promise((resolve, reject) => {
    request({
      url: apis['order.detail.pay'],
      toastSuccess: false,
      toastError: true,
      toastLoading: true,
      data,
    })
      .then((res = {}) => {
        const { code, data } = res;
        if (code == 0) {
          requestPayment(data)
            .then(resolve)
            .catch(() => {
              reject({ status: '支付失败' });
            });
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * 团长标记支付
 * @param {*} data
 * @returns
 */
export const markOrderPay = (data = {}) => {
  return new Promise((resolve, reject) => {
    request({
      url: apis['order.detail.payMark'],
      toastSuccess: true,
      toastError: true,
      toastLoading: true,
      data,
    })
      .then((res = {}) => {
        const { code } = res;
        if (code == 0) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * 判断地址信息是否加密
 *  */
export const isEncryptionData = (data = {}) => {
  const str = Object.values(data).join('');
  return str.includes('*');
};
/**
 * 拼接用户信息
 *  */
export const combineData = (data = {}) => {
  return Object.values(data).join('');
};

/**
 * 删除订单照片
 * @param id 照片id
 *  */
export const deleteOrderPic = (id) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Order/deletePackagePics',
      toastError: true,
      toastLoading: true,
      data: {
        id,
      },
    })
      .then((res = {}) => {
        const { code } = res;
        if (code == 0) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * 获取物流轨迹
 *  */
export const getExpressTrack = ({ brand, waybill }) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Logistics/expressTrack',
      data: {
        brand,
        waybill,
      },
      onThen: ({ code, data }) => {
        const list = Array.isArray(data.list) ? data.list : [];
        if (code == 0 && list.length) {
          const item = list[list.length - 1] ? list[list.length - 1].info : '';
          resolve(item);
        }
      },
    });
  });
};
