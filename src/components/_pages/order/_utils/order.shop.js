/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { noop } from '@base/utils/utils';

/**
 *
 * @description 兼容微信与支付宝
 * @param {*} mapId
 * @param {*} $scope
 * @param {*} param2
 * @returns
 */
export const createMapContext = (
  mapId,
  $scope,
  { updateMarkers, updateIncludePoints, setCenterMarker },
) => {
  if (Taro.createMapContext) {
    return process.env.PLATFORM_ENV === 'weapp'
      ? Taro.createMapContext(mapId, $scope)
      : Taro.createMapContext(mapId);
  }

  // 兼容代码，基础库版本更新到一定程度后可取消以下兼容代码
  const addMarkers = ({ markers: markers_, clear, success = noop }) => {
    updateMarkers([...(clear ? [] : markers_), ...markers_]);
    success();
  };
  return {
    includePoints: ({ points }) => updateIncludePoints(points),
    moveToLocation: () => {},
    addMarkers,
    changeMarkers: addMarkers,
    translateMarker: ({ destination, animationEnd = noop }) => {
      setCenterMarker(destination, true);
      animationEnd();
    },
  };
};
