/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { extractData, mergeBySpace, getStorage, removeStorage } from '@base/utils/utils';
import rules from '@base/utils/rules';
import request from '@base/utils/request';
import isPlainObject from 'lodash/isPlainObject';
import debounce from 'lodash/debounce';
import { requestGetOnce } from '~base/utils/request/once';

// 地址key对应的内容
export const addressKeysMap = {
  all: '地址信息',
  whatever: '地址信息有误',
  name: '姓名',
  mobile: '联系电话',
  province: '省份',
  city: '城市',
  district: '地区',
  address: '详细地址',
  door: '门牌号',
  longitude: '经纬度',
  latitude: '经纬度',
  service: '增值服务',
};

export const addressKeys = ['name', 'mobile', 'province', 'city', 'district', 'address', 'save'];

export const defaultSendStorageKey = 'address';

// 本地收件人列表缓存key
export const receiveStorageKey = 'receiveList';
export const sendStorageKey = 'send';

// 订单编辑地址信息列表
export const addressList = [
  {
    key: 'send',
    placeholder: [
      {
        label: '请填写寄件人信息',
        className: 'kb-size__17',
      },
      {
        label: '支持智能识别文本、图片中的地址',
        className: 'kb-color__grey-3 kb-size__base',
      },
    ],
    color: 'brand',
    tag: '寄',
    selector: true,
  },
  {
    key: 'receive',
    placeholder: [
      {
        label: '请填写收件人信息',
        className: 'kb-size__17',
      },
      {
        label: '支持智能识别文本、图片中的地址',
        className: 'kb-color__grey-3 kb-size__base',
      },
    ],
    color: 'greener',
    tag: '收',
  },
];
const addressKeysRules = {
  name: {
    min: 1,
  },
  address: {
    validator: (value, currentRule = {}) => {
      let tag = currentRule.tag || '详细地址',
        verifyResult = false,
        min = 4,
        max = 60,
        rule = /^[A-Za-z0-9]+$/;
      let val = value + '';
      let valueSize = val.length;
      if (valueSize < min) {
        verifyResult = `${tag}长度不能小于${min}个汉字`;
      } else if (valueSize > max) {
        verifyResult = `${tag}长度不能大于${max}个汉字`;
      } else if (rule && rule.test(val)) {
        verifyResult = `${tag}不能填写为纯字母或纯数字格式`;
      }
      return verifyResult;
    },
  },
};
export const goodsKeys = ['goods_name', 'goods_weight', 'goods_remark', 'package_images'];
// 生成表单
const unRequiredKeys = [
  'company',
  'service',
  ...goodsKeys,
  'save',
  'province_confidence',
  'city_confidence',
  'district_confidence',
];

const noStorageKey = ['reserve_time', 'shipper_zipcode_del'];

const prefixs = {
  send: '寄件人',
  receive: '收件人',
};
// 提取公共地址部分
export function extractAddressInfo(data, merge = []) {
  if (process.env.MODE_ENV == 'wkd') {
    return extractData(data, [
      ['name', 'real_name'],
      'name',
      ['mobile', (item) => mergeBySpace(item.mobile, item.telephone_code, item.phone)],
      'province',
      ['province', 'address_province'],
      ['province', 'province_name'],
      'city',
      ['city', 'address_city'],
      ['city', 'city_name'],
      ['district', 'address_county'],
      ['district', 'county_name'],
      ['district', 'county'],
      ['address', 'address_detail'],
      ['address', 'detail'],
      ...merge,
    ]);
  }
  return extractData(data, [
    'name',
    ['mobile', (item) => mergeBySpace(item.mobile, item.tel)],
    'province',
    'city',
    ['district', 'county'],
    ['address', 'detail'],
    ...merge,
  ]);
}

// 检查联系方式组
export const checkContactGroup = (value, { rule, msg } = rules.contact) => {
  const list = `${value}`.split(/\s+|[,，;]+/g).filter((item) => !!item);
  let errorIndex = -1;
  for (let i = 0, len = list.length; i < len; i++) {
    if (!rule.test(list[i])) {
      errorIndex = i;
      break;
    }
  }
  if (errorIndex >= 0) {
    return msg;
  }
};

// 检查列表中是否存在错误的地址信息
export function getErrorAddressIndex(list, toast = true) {
  const index = list.findIndex((item) => item.error);
  let errMsg = '';
  if (index >= 0) {
    const { error } = list[index];
    const others = ['whatever']; // 其他错误，不用补充地址缺少
    const fix = others.includes(error) ? '' : `地址缺少`;
    errMsg = `第${1 + index}条${fix + addressKeysMap[error]}`;
    toast &&
      Taro.kbToast({
        text: errMsg,
      });
  }
  return { index, errMsg };
}

export function getFormItem({
  keys,
  data,
  form = {},
  prefix = '',
  clean = true,
  merge = {}, // 合入的表单配置项
}) {
  const formData = { ...data };
  keys.map((key) => {
    const formKey = prefix ? `${prefix}_${key}` : key;
    const commonRules = addressKeysRules[key] || {};
    const data = {
      value: formData[formKey] || '',
      clean,
      storage: !noStorageKey.includes(formKey),
      required: !unRequiredKeys.includes(key),
      ...commonRules,
      ...merge[formKey],
    };
    const addressKeyValue = addressKeysMap[key];
    if (addressKeyValue) {
      data.tag = (prefixs[prefix] || '') + addressKeyValue;
    }
    if (key === 'mobile') {
      data.customMsg = data.tag + '格式不正确';
      data.reg = 'contact';
      data.validator = checkContactGroup;
    }
    form[formKey] = data;
  });
  return form;
}

// 获取表单配置 action = order:创建订单；address:地址编辑
export const getForm = ({
  list = addressList,
  keys = addressKeys,
  action = 'order',
  data,
} = {}) => {
  const form = {};
  list.map((item) => {
    const prefix = item.key;
    getFormItem({
      keys,
      data,
      form,
      prefix,
      clean: action === 'address' ? true : prefix !== 'send',
    });
  });
  return form;
};

export const formatAddress = (
  data = {},
  key,
  { reverse = false, keys = addressKeys, replace = {} } = {},
) => {
  let has = false;
  let res = {
    data: extractData(
      data,
      keys.map((item) => {
        let from = item;
        let to = `${key}_${item}`;
        if (reverse) {
          [to, from] = [from, to];
        }
        if (data[to] && !has) {
          has = true;
        }
        // 替换key
        const str = replace[item];
        if (str) {
          from = from.replace(item, str);
        }
        return [from, to];
      }),
    ),
    has,
  };
  return res;
};

/**
 *
 * @description 兼容支付宝与微信
 * @returns
 */
export const chooseAddress = () => {
  return new Promise((resolve, reject) => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      Taro.chooseAddress()
        .then((res) => {
          const {
            userName: name,
            provinceName: province,
            cityName: city,
            countyName: district,
            detailInfo: address,
            telNumber: mobile,
          } = res;

          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        })
        .catch(reject);
    } else {
      if (!my.getAddress) {
        reject(new Error('支付宝版本过低无法使用该功能'));
        return;
      }
      my.getAddress({
        success: (res) => {
          const {
            fullname: name,
            mobilePhone: mobile,
            prov: province,
            city,
            area: district,
            address,
          } = res.result || {};
          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        },
        fail: reject,
      });
    }
  });
};

//获取本地缓存的寄件人地址---主要是类似粘贴板那种临时性质的地址
export const getSendStorageData = () => {
  return new Promise((resolve, reject) => {
    getStorage({
      key: sendStorageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data) {
          //使用一次后清空
          removeStorage({ key: sendStorageKey });
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};

export const checkLastStorageData = (storageKey) => {
  return new Promise((resolve, reject) => {
    if (!storageKey) reject();
    getStorage({
      key: storageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data && data.send_name) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};
// type转化为receive转化为collect
export const fixType = (type) => {
  return type === 'send' ? type : 'collect';
};
// 获取默认发件人
export const getDefaultSendAddress = (storageKey) => {
  return new Promise((resolve, reject) => {
    // 默认发件人
    const type = 'send';
    // 如果粘贴板上存在地址，将其设置为默认发件人
    getSendStorageData()
      .then((data) => {
        resolve(data);
      })
      .catch(() => {
        checkLastStorageData(storageKey)
          .then()
          .catch(() => {
            request({
              url: '/api/AddressBook/list',
              data: {
                address_type: fixType(type),
                page_per: 5,
                page: 1,
              },
              toastLoading: false,
              onThen: ({ code, data }) => {
                if (code == 0 && isPlainObject(data[0])) {
                  const { name, mobile, province, city, county: district, address } = data[0] || {};
                  if (!name) return reject(); // 无默认发件人地址时依然响应了空对象
                  resolve({
                    name,
                    mobile,
                    province,
                    city,
                    district,
                    address,
                  });
                } else {
                  reject();
                }
              },
            });
          });
      });
  });
};

// 对可信任度较低的区域做标记
export const labelRedFn = (confidence) => {
  if (!confidence && confidence !== 0) return '';
  return `${confidence < 3 ? 'kb-color__orange' : ''}`;
};

export const getQuotation = (formData, opt) => {
  const {
    source,
    address,
    weight: _weight,
    volume,
    delivery_type,
    payType,
    payDiscount,
    isBatch,
    receiveList,
    cabinetInfo,
    back_sign_bill,
    package_service,
    pickup_way,
    floor,
    reserve_start_time,
    decVal,
    brand,
  } = formData || {};
  const { toastLoading = false, quickTriggerThen } = opt || {};

  // 只需要开始时间
  const pickup_time = removeTrailingColon(reserve_start_time);
  // 处理重量
  const weight = _weight > 0 ? _weight : 1;
  // 处理体积
  let vol = {};
  if (volume && volume.checked && volume.volume) {
    vol = { ...volume };
    vol.place_volume = volume.volume;
    delete vol.checked;
  }
  // 其他参数
  const { cabinet_code } = cabinetInfo || {};
  const isDiscount = payDiscount == 'discount';
  const address_format = transferAddress(address || {});
  // 组装参数
  let params = {
    arrivePay: payType ? payType * 1 : 0,
    delivery_type,
    isDiscount,
    cabinet_code,
    reserve_start_time,
  };
  if (source === 'big_package') {
    params.other_service = {
      ...vol,
      back_sign_bill,
      package_service,
      pickup_way,
      floor,
      pickup_time,
    };
    params.brand = brand; //非必填-大货保价时需要
    params.decVal = decVal; //非必填-大货保价时需要
  } else if (source === 'arrive_pay_order') {
    params.source = 'arrive_pay';
  }
  if (isBatch && receiveList && receiveList.length > 0) {
    let shippings = [];
    receiveList.forEach((v) => {
      const { extraInfo } = v;
      const { goods_weight, volume: volume2 } = extraInfo || {};
      let vol2 = {};
      if (volume2 && volume2.checked && volume2.volume) {
        vol2 = volume2;
        vol2.place_volume = volume2.volume;
        delete vol2.checked;
      }

      shippings.push({
        ...address_format,
        weight: goods_weight || weight || 1,
        ...(vol2.volume ? vol2 : vol),
        shipping_province: v.province,
        shipping_city: v.city,
        shipping_district: v.district,
        shipping_address: v.address,
      });
    });
    params.addressList = shippings;
  } else {
    params.addressList = [
      {
        weight,
        ...vol,
        ...params,
        ...address_format,
      },
    ];
  }
  // console.log('报价单请求参数==>', params);
  return new Promise((resolve, reject) => {
    requestGetOnce({
      url:
        source === 'big_package'
          ? '/api/quotation/batchBigPackageQuotationList'
          : '/api/quotation/getBatchQuotationList',
      toastLoading,
      quickTriggerThen,
      data: params,
      onThen(res) {
        const { code, data } = res;
        if (code === 0) {
          resolve(data);
        } else {
          reject();
        }
      },
    });
  });
};

//转换标准微快递地址信息
export function transferAddress(data = {}, action = 'all') {
  let arr = [
    ['shipper_province', 'send_province'],
    ['shipper_city', 'send_city'],
    ['shipper_district', 'send_district'],
    ['shipper_address', 'send_address'],
    ['shipping_province', 'receive_province'],
    ['shipping_city', 'receive_city'],
    ['shipping_district', 'receive_district'],
    ['shipping_address', 'receive_address'],
  ];
  if (action == 'all') {
    arr = arr.concat([
      ['shipper_name', 'send_name'],
      ['shipper_mobile', 'send_mobile'],
      ['shipping_name', 'receive_name'],
      ['shipping_mobile', 'receive_mobile'],
    ]);
  }
  return extractData(data, arr);
}
export const transferWkdAddress = transferAddress;

/**
 * 分享朋友圈，假地址
 *  */
export const fakeAddress = {
  receive_address: '芙蓉山庄',
  receive_city: '无锡市',
  receive_district: '锡山区',
  receive_mobile: '178****8888',
  receive_name: '张某山',
  receive_province: '江苏省',
  receive_save: '',
  send_address: '新泾镇虹园一村',
  send_city: '上海市',
  send_district: '长宁区',
  send_mobile: '152****9581',
  send_name: '李某明',
  send_province: '上海市',
  send_save: '',
};

export const removeTrailingColon = (str) => {
  if (str && str[str.length - 1] == ':') {
    return str.slice(0, str.length - 1);
  }
  return str;
};

export const showNightFeeModal = debounce(
  (v) => {
    return new Promise((resolve) => {
      Taro.kbModal({
        top: false,
        closable: false,
        title: '夜间取货费',
        content: [
          {
            className: 'kb-color__greyer kb-size__base2',
            text: '服务介绍：跨越速运为寄方客户或收方客户提供24小时上门收派件服务。',
          },
          {
            className: 'kb-color__greyer kb-size__base2',
            text: '当前取货时间段预计产生',
          },
          {
            text: [
              {
                className: 'kb-color__greyer kb-size__base2 ',
                text: '夜间取货费用：',
              },
              {
                className: 'kb-color__brand kb-size__base2',
                text: v,
              },
              {
                className: 'kb-color__greyer kb-size__base2',
                text: '元',
              },
            ],
          },
          {
            className: 'kb-color__red kb-size__sm kb-margin-sm-t kb-text__center',
            text: '注：收费时间点以真实货好时间为准',
          },
        ],
        confirmText: '我已知晓',
        onConfirm: resolve,
      });
    });
  },
  500,
  { trailing: true, leading: false },
);

export const checkHasNightFee = (list = [], brand) => {
  return new Promise((resolve, reject) => {
    const item = list.find((i) => i.brand === brand) || [];
    if (Array.isArray(item.service_price)) {
      const cur = item.service_price.find((s) => s.service_name == '夜间取货费用');
      if (cur) {
        showNightFeeModal(cur.amount);
        resolve(cur.amount);
      }
      reject();
    }
    reject();
  });
};

export const checkHasDepotFee = (data = {}) => {
  const { unconfirm = [] } = data;
  if (Array.isArray(unconfirm)) {
    const item = unconfirm.find((i) => i.type == '20');
    if (item) {
      return item.amount;
    }
  }
  return null;
};

export const showDepotFeeModal = (v, page) => {
  return new Promise((resolve) => {
    Taro.kbModal(
      {
        top: false,
        closable: false,
        title: '入仓费',
        content: [
          {
            text: [
              {
                className: 'kb-color__greyer kb-size__base2 kb-margin-md-b',
                text: '尊敬的客户，由于地址匹配到国际货运三方仓、保税区或海关监管区等，我司会提供除派件之外的报关入仓服务，会收取',
              },
              {
                className: 'kb-color__brand kb-size__base2',
                text: v,
              },
              {
                className: 'kb-color__greyer kb-size__base2',
                text: '元/票报关入仓服务费，感谢理解！',
              },
            ],
          },
        ],
        confirmText: '我已知晓',
        onConfirm: resolve,
      },
      page,
    );
  });
};
