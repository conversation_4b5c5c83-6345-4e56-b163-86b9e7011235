/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import Taro from '@tarojs/taro';
import isFinite from 'lodash/isFinite';
import numeral from 'numeral';
import { getIntegralByMony } from '@/components/_pages/order/integral-bar/_utils';
/**
 *
 * @description 格式化金额展示
 * @param {*} val
 * @returns
 */
export const formatNumeral = (val) => `${numeral(val).format('0.00')}`;

/**
 *
 * @description 费用列表 - 项
 * @param {*} name
 * @param {*} info
 * @returns
 */
export function setFeeListItem(name, info) {
  return {
    name,
    info: isFinite(+info) ? `￥${formatNumeral(info)}` : info,
  };
}

/**
 *
 * @description 费用列表
 * @param {*} data
 * @param {*} param1
 */
export const createFeeList = (data, { orderType, extraText }) => {
  const feeList = [];
  if (process.env.MODE_ENV === 'wkd') {
    if (orderType === 'tcjs') {
      // 同城订单
      const {
        goods_name,
        goods_weight,
        car_name,
        cargo_price,
        distance,
        order_finish_code,
        is_use_insurance,
        insurance_fee,
        waybill,
        note,
        fee,
      } = data;
      const list = [
        ['物品信息', `${goods_name}/${goods_weight}kg`],
        ['车型选择', car_name],
        ['额外需求', extraText],
        ['声明价值', cargo_price > 0 && cargo_price],
        ['配送距离', distance > 0 && `${distance}km`],
        ['收货码', order_finish_code],
        ['保价服务', is_use_insurance > 0 && insurance_fee],
        ['订单编号', waybill],
        ['备注信息', note || '未填写'],
        ['配送费', fee],
      ];
      list.map((item) => {
        const [name, info] = item;
        info && feeList.push(setFeeListItem(name, info));
      });
    } else if (orderType === 'wzg') {
      // 微掌柜订单
      const { payment, items_freight } = data;
      feeList.push(setFeeListItem('商品费用', payment));
      if (items_freight > 0) {
        feeList.push(setFeeListItem('运费', items_freight));
      }
    } else {
      const { freight } = data;
      feeList.push(setFeeListItem('实际快递费', freight));
    }
  } else {
    const { pro_price } = data;
    if (pro_price > 0) {
      feeList.push(setFeeListItem('保价费', pro_price));
    }
  }
  return feeList;
};

/**
 *
 * @description 计算金额
 * @param {*} opts
 * @returns
 */
export const countMoney = (opts) => {
  const {
    orderType,
    data,
    integral: { num, integralMoney },
    coupon: { money: couponMoney, id, type: couponType },
    serviceMoney = 0,
    reckonInfo,
    fee,
    priceType,
  } = opts;
  let total = 0;
  if (process.env.MODE_ENV === 'wkd') {
    if (orderType === 'tcjs') {
      total = data.pay;
    } else {
      total = fee * 1 + serviceMoney;
    }
  } else {
    total = fee;
  }
  const isUsedEquityCard = couponType === 'discount_card' && id;
  const isUsedCard = id && couponType === 'coupon';
  if (isUsedEquityCard && reckonInfo && priceType === 'manual') {
    const { f_weight } = reckonInfo;
    total = parseFloat(total) - parseFloat(f_weight);
  } else if (num) {
    // 减积分抵扣
    total = total - integralMoney;
  } else if (isUsedCard) {
    // 减优惠券
    total = total - couponMoney;
  }
  const { pro_price } = data;
  total = total > 0 ? parseFloat(total) : 0;
  total = parseFloat(pro_price || 0) + parseFloat(total);
  return formatNumeral(total);
};

/**
 *
 * @description 检查切换优惠方式：券或积分
 * @param {*} type
 * @param {*} opts
 * @returns
 */
export const checkSwitchCoupon = (type, opts, page) => {
  return new Promise((resolve, reject) => {
    const {
      coupon: { id },
      integral: { num },
    } = opts;
    const typesMap = { coupon: '优惠券', integral: '积分' };
    let types = ['integral', 'coupon'];
    switch (type) {
      case 'coupon': // 切换优惠券
        if (!num) {
          types = null;
        }
        break;
      case 'integral': // 切换积分
        if (id) {
          types.reverse();
        } else {
          types = null;
        }
        break;
    }
    if (types) {
      Taro.kbModal(
        {
          content: `当前已经使用${typesMap[types[0]]}，且优惠方式只可以选择一种，是否要使用${
            typesMap[types[1]]
          }？`,
          cancelText: '取消',
          onConfirm: () => resolve(types),
          onCancel: () => reject(new Error('不允许切换')),
        },
        page,
      );
    } else {
      resolve(types);
    }
  });
};

/**
 *
 * @description 支付订单
 */
export const payOrder = (opts) => {
  return new Promise((resolve, reject) => {
    const createError = ({ code, msg }) => {
      return new Error(code > 0 ? msg : '获取签名失败');
    };
    let url = '';
    let reqData = {};
    let extraData = {};
    if (process.env.MODE_ENV !== 'wkd') {
      const {
        data: { order_id, price: orderPrice, pay_type },
        write,
        integral: { num: points, points_id, points_kind },
        coupon: { id: coupon_id, type: couponType },
        reckonInfo,
        pageParams: { order_random } = {},
      } = opts;
      url = '/api/weixin/mini/minpost/Pay/sign';
      const { price: reckonPrice } = reckonInfo || {};
      const price = parseFloat(
        orderPrice && pay_type == 0 ? orderPrice : write || reckonPrice || 0,
      );
      reqData = {
        order_id,
        ...(opts.reckonInfo || {}),
        price,
        price_type: pay_type == 0 && orderPrice ? 0 : write ? 2 : price ? 1 : '',
      };
      if (order_random) {
        url = '/api/weixin/mini/minpost/Pay/SharePaySign';
        reqData.order_random = order_random;
      }
      if (points_id && points) {
        const availablePoints = getIntegralByMony(points, price);
        extraData = { points: availablePoints, points_id, points_kind };
      } else if (coupon_id) {
        extraData = {
          [couponType === 'coupon' ? 'coupon_id' : 'card_id']: coupon_id,
        };
      }
    } else {
      // 微快递
      const {
        data: { order_id, freight, price, pay: form_price, platform, brand },
        write,
        coupon: { id: coupon_id },
        orderType,
        pageParams = {},
      } = opts;
      const { userInfo: { openid: open_id } = {} } = Taro.kbLoginData || {};
      url =
        process.env.PLATFORM_ENV === 'alipay'
          ? '/g_order_core/v2/PaymentStatus/appletAlipaySign'
          : '/g_order_core/v2/PaymentStatus/appletWxSign';
      reqData = {
        order_id,
        coupon_id,
        open_id,
        money: freight,
      };
      switch (orderType) {
        case 'wzg':
          extraData = { source: 'ws', freight };
          break;
        case 'tcjs':
          url = '/g_wkd/v1/rush/Rush/pay';
          extraData = {
            form_price,
          };
          break;
        default:
          if (platform === 'yjkd' && brand === 'jt') {
            url = '/g_wkd/v2/Yj/Order/orderPay';
          } else {
            reqData.money = price || write || freight;
          }
          break;
      }
      if (process.env.PLATFORM_ENV === 'alipay') {
        extraData.pay_method = 'aliapplet';
      }
      // app分享支付
      if (pageParams.share_pay_order_random) {
        extraData.share_pay_order_random = pageParams.share_pay_order_random;
      }
    }
    request({
      url,
      data: {
        ...reqData,
        ...extraData,
      },
      onThen: ({ code, data, msg }) => {
        if (code == 0 && data) {
          requestPayment(data)
            .then(resolve)
            .catch(() => {
              reject({ status: 'wx_fail' });
            });
        } else {
          reject(createError({ code, msg }));
        }
      },
    });
  });
};

// 预估费用
export const getReckonFee = (data, neglect) => {
  return new Promise((resolve) => {
    if (neglect) {
      resolve({ code: 1013, data: {} });
      return;
    }
    request({
      url:
        process.env.MODE_ENV == 'wkd'
          ? '/v1/WeApp/quotation'
          : '/api/weixin/mini/minpost/order/getQuotationPrice',
      toastLoading: false,
      loadingStatusKey: 'getting',
      data,
      onThen: (data) => {
        resolve(data);
      },
    });
  });
};

export const getReckonFeeApiDataByOrder = (data) => {
  const {
    send_province,
    send_city,
    send_district,
    send_address,
    receive_province,
    receive_city,
    receive_district,
    receive_address,
    goods_weight: weight,
    brand,
    relationData: { type, dakId, id, phone },
  } = data || {};
  const { customer } = Taro.kbRelationInfo.data || {};
  let params = {};
  if (process.env.MODE_ENV == 'wkd') {
    params = {
      shipper_province: send_province,
      shipper_city: send_city,
      shipper_district: send_district,
      shipper_address: send_address,
      shipping_province: receive_province,
      shipping_city: receive_city,
      shipping_district: receive_district,
      shipping_address: receive_address,
      weight: weight || 1,
      courier_id: id,
      phone: phone,
      area: receive_province,
    };
    if (customer && customer.id) {
      params.customer_id = customer.id;
    }
  } else {
    params = {
      area: receive_province,
      send_area: send_province,
      weight: weight || 1,
      brand,
    };
    type === 'dak' ? (params.dak_id = dakId) : (params.courier_id = id);
  }
  return params;
};

export const getPayConfig = () => {
  return new Promise((resolve, reject) => {
    if (process.env.MODE_ENV == 'wkd') {
      resolve(true);
    } else {
      request({
        url: '/api/weixin/mini/user/Config/getConfig',
        toastLoading: false,
        onThen: ({ data: { pay_status } = {}, code }) => {
          if (code !== 0) {
            reject();
            return;
          }
          resolve(pay_status == '0');
        },
      });
    }
  });
};

export const memoObject = (data = {}, change = {}) => {
  let modify = false;
  const update = {};
  Object.keys(change).forEach((key) => {
    if (data[key] !== change[key] && key in change) {
      modify = true;
      update[key] = change[key];
    }
  });
  return [modify, update];
};
