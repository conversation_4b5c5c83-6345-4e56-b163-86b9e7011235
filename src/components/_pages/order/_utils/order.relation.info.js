/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getPage } from '@base/utils/utils';
import {
  getCourierParams,
  getApiCourierDetail,
} from '@/components/_pages/order/_utils/courier.detail';
import { scanAction } from '@/utils/scan';
import { getStoreInfo, formatShopInfo, getCourierInfo } from '@/components/store-card/_utils';
import apis from '@/utils/apis';

export const getRelationType = () => {
  const page = getPage(-1);
  const { phone, courierId } = getCourierParams(page.$router.params);
  return phone || courierId ? 'courier' : 'dak';
};
export const createBars = (type) => {
  const bars = [
    {
      key: 'send',
      label: '寄件',
      explain: '便利快捷',
      around: 'blue-1',
      icon: 'send-3',
      iconColor: 'blue',
    },
    ...(type === 'dak'
      ? [
          {
            label: '取件',
            icon: 'pickup',
            key: 'pickup',
            explain: '预约取件得积分',
            around: 'yellow-1',
            iconColor: 'yellow',
          },
        ]
      : process.env.MODE_ENV === 'wkd'
      ? [
          {
            key: 'message',
            icon: 'message-1',
            label: '留言',
            explain: '咨询反馈',
            around: 'yellow-1',
            iconColor: 'yellow',
          },
        ]
      : []),
    ...(process.env.MODE_ENV !== 'weapp.third' && process.env.MODE_ENV !== 'wkd'
      ? [
          {
            key: 'reward',
            label: '加油包',
            icon: 'reward',
            explain: `给${type == 'dak' ? '驿站' : '快递员'}加油`,
            around: 'pink1',
            iconColor: 'red',
          },
        ]
      : []),
  ];
  return bars;
};
const getApiDakDetail = (params) => {
  const { dakId } = params || {};
  return {
    url: apis[`dak.detail`],
    data: process.env.MODE_ENV === 'wkd' ? { inn_id: dakId } : { dakId },
    formatResponse: ({ data, code }) => {
      if (code == 0) {
        return { data: formatShopInfo(data) };
      } else {
        return { data: void 0 };
      }
    },
  };
};
export const getApi = (_this) => {
  const type = getRelationType();
  const page = getPage(-1);
  let params = page.$router.params;
  return {
    ...(type == 'dak' ? getApiDakDetail(params) : getApiCourierDetail(params)),
    onIntercept(req, onThen) {
      const { q, qrCode } = params || {};
      // 特殊场景---扫码进入
      if (q || qrCode) {
        scanAction().then(({ dakId, courierId }) => {
          if (dakId) {
            _this.relationType = 'dak';
            getStoreInfo(dakId).then((data) => {
              onThen({ code: 0, data });
            });
          } else if (courierId) {
            _this.relationType = 'courier';
            getCourierInfo(courierId).then((data) => {
              onThen({ code: 0, data });
            });
          }
        });
        return true;
      }
    },
  };
};
