/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import apis from '@/utils/apis';
import request from '@base/utils/request';
import { getCurrentUser } from '@base/utils/utils';
import { fixOrderData } from './order.detail';

/**
 *
 * @description 合并类表数据
 * @param {*} current
 * @param {*} list
 */
export const mergeOrderList = (current, list, callback) => {
  if (!isArray(current)) return list;
  current.map((item) => {
    const formattedItem = isFunction(callback) ? callback(item) : item;
    const { title: itemTitle, list: itemList } = formattedItem;
    const index = list.findIndex(({ title }) => title === itemTitle);
    if (index >= 0) {
      list[index].list = list[index].list.concat(itemList);
    } else {
      list.push(formattedItem);
    }
  });
  return list;
};

/**
 *
 * @param {*} res
 * @param {*} req
 */
export const formatResponseOrderList = () => {
  return async (res) => {
    const { data } = res;
    let list = null;

    const { list: listArr, total } = data || {};
    list = listArr;

    const hasList = isArray(list) && list.length > 0;
    if (hasList) {
      return {
        data: {
          list: list.map((item) => {
            return fixOrderData(item);
          }),
        },
        total,
      };
    }
    return {
      data: void 0,
      total,
    };
  };
};

/**
 *
 * @description 订单列表初始处理
 * @param {*} param0
 * @returns
 */
export const orderListInit = ({ tabKey, isAdmin, lps_id, source }) => {
  // 不同请求的不同参数key
  let pageKey = 'page';
  let pageSizeKey = 'pageSize';
  let extraData = null;
  let noMoreText = '已显示全部订单';
  let noDataText = '';
  switch (tabKey) {
    case 'unConfirm':
      extraData = {
        order_status: 1,
      };
      noDataText = '暂无待确认订单';
      break;
    case 'all':
      extraData = {};
      if (isAdmin) {
        extraData.place_order = 1;
      }
      noDataText = '暂无订单';
      break;
    case 'waitPay':
      extraData = {
        arrears: 1,
      };
      noDataText = '暂无待补款订单';
      break;
    case 'wait_rebate':
      extraData = {
        wait_rebate: 1,
      };
      noDataText = '暂无待入账订单';
      break;

    default:
      break;
  }

  if (source == 'promotion') {
    extraData.lps_id = lps_id;
  }

  // 注销时异常订单列表
  if (source == 'abnormal') {
    extraData = {
      order_list_type: tabKey,
    };
    noDataText = '暂无订单';
  }

  return {
    pageKey,
    pageSizeKey,
    extraData,
    noMoreText,
    noDataText,
  };
};

/**
 * 订单tab栏数据
 * @param {boolean} isAdmin 是否是管理员，只有管理员有待确认页
 *  */
export const createOrderTabs = () => {
  const { userInfo: { is_admin } = {} } = Taro.kbLoginData || {};

  const wait_rebate =
    getCurrentUser('custom-f') && (getCurrentUser('regiment') || getCurrentUser('league'))
      ? [
          {
            title: '待入账',
            key: 'wait_rebate',
            max: 999,
          },
        ]
      : [];
  const tabs = [
    ...(is_admin == 1 && !getCurrentUser('custom')
      ? [
          {
            title: '待确认',
            key: 'unConfirm',
            max: 999,
          },
        ]
      : []),
    {
      title: '全部',
      key: 'all',
      max: 999,
    },
    {
      title: '待补款',
      key: 'waitPay',
      max: 999,
    },
    ...wait_rebate,
  ];
  return tabs;
};
/**
 * 更新tabList
 * @param {Array} tabList
 * @param {string} tabKey 当前页 （unConfirm | all | waitPay)
 * @param {string} res 请求后的数据
 *  */
export const updateTabList = (tabList = [], tabKey, res) => {
  const tabListArr = [...tabList];
  const total = res.total || 0;
  tabListArr.forEach((val) => {
    const title = val.title.split('(')[0];
    if (val.key == tabKey) {
      val.title = /[0-9]+/g.test(title) ? title.replace(/[0-9]+/g, total) : `${title}(${total})`;
      if (tabKey == 'unConfirm') {
        val.dot = total > 0;
      }
    } else {
      val.title = title;
    }
  });

  return tabListArr;
};

/**
 * 获取订单列表URL配置
 * @param {boolean} isAdmin 是否是团长
 *  */
export function getUrlAndDataOrderDetail({ extraData = {}, source }) {
  const data = { pageSize: 10, ...extraData };

  if (source == 'memberOrder') {
    return {
      url: apis['order.member.list'],
      data,
    };
  }
  if (source == 'promotion') {
    return {
      url: '/api/LeaguePromotionSpecialist/orderLists',
      data,
    };
  }
  if (source == 'arrivePay') {
    return {
      url: '/api/OrderStatic/arrivePayOrderDetail',
      data,
    };
  }
  return {
    url: () => {
      const { userInfo: { is_admin } = {} } = Taro.kbLoginData || {};
      return is_admin == 1 && (!getCurrentUser('custom') || getCurrentUser('custom-f'))
        ? apis['order.admin.list']
        : apis['order.member.list'];
    },
    data,
  };
}
// 获取订单数量
export function getOrderCount({ extraData, tabKey, source }) {
  return new Promise((resolve) => {
    if (source == 'abnormal') {
      request({
        url: '/api/Order/unfinishedOrderTypeStatistic',
        toastLoading: false,
      }).then((res) => {
        if (res.code === 0 && res.data) {
          resolve(res.data);
        }
      });
      return;
    }
    const { url, data } = getUrlAndDataOrderDetail({
      extraData,
      tabKey,
      source,
    });
    request({
      url,
      data,
      toastLoading: false,
      onThen: (res) => {
        if (res.code === 0 && res.data) {
          resolve(res.data.total || '0');
        }
      },
    });
  });
}

export const orderFilterOptions = [
  {
    label: '打印电子面单',
    key: 'print_order',
  },
  {
    label: '订单标记已付',
    key: 'mark_order',
  },
  {
    label: '取消订单',
    key: 'cancel_order',
  },
  {
    label: '删除订单',
    key: 'delete_order',
  },
];
export const orderFilterOptionsKOP = [
  {
    label: '短信催收',
    key: 'remind_order',
  },
];

/**
 * 删除订单，最大不超过10个一次
 *  */
export const deleteOrders = (order_ids) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Order/hideOrder',
      toastError: true,
      toastSuccess: true,
      nonceKey: 'md5:order_ids',
      data: {
        order_ids,
      },
    }).then((res) => {
      if (res.code == 0) {
        resolve();
      }
    });
  });
};
