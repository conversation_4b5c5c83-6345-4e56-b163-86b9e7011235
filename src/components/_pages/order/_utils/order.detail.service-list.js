/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import isArray from 'lodash/isArray';

/**
 *
 * @description 获取增值服务总金额
 * @param {*} list
 * @returns
 */
function getServiceMoney(list) {
  return list.reduce(
    (pre, { isActualcost, price }) => pre + (isActualcost && price ? 1 * price : 0),
    0,
  );
}

export const getServiceList = ({ order_id, ...data }, { orderType }) => {
  if (process.env.MODE_ENV !== 'wkd') return Promise.reject();
  return new Promise((resolve, reject) => {
    if (!order_id) {
      reject(new Error('缺少订单号'));
      return;
    }
    if (orderType === 'tcjs') {
      const {
        order_finish_code,
        is_use_insurance,
        insurance_fee,
        cargo_price,
        privacyNumber,
        brand,
        service = [],
      } = data;
      // 同城
      const list = [];
      const useServiceBrands = ['huolala', 'fczy'];
      let extraText = '';
      if (useServiceBrands.includes(brand) && isArray(service) && service.length > 0) {
        list.push(...service);
        extraText = service.reduce((pre, cur) => pre + cur.name, '');
      } else {
        if (order_finish_code) {
          list.push({
            id: 2,
            name: '收货码',
            isActualcost: false,
            desc: '骑手输入收货码完成订单',
          });
        }
        if (is_use_insurance > 0) {
          list.push({
            id: 1,
            name: '保价服务',
            isActualcost: true,
            price: insurance_fee,
          });
        }
        if (cargo_price > 0) {
          list.push({
            id: 3,
            name: '声明物品价值',
            isActualcost: false,
            price: cargo_price,
          });
        }
        if (privacyNumber > 0) {
          list.push({
            id: 4,
            name: '号码保护',
            isActualcost: false,
            desc: '对闪送员隐藏真实的手机号码，保护您的隐私',
          });
        }
      }
      if (list.length > 0) {
        resolve({ list, extraText, money: getServiceMoney(list) });
      } else {
        reject(new Error('暂无增值服务'));
      }
    } else {
      request({
        url: '/v1/GrabVas/selected',
        data: {
          source: 'mina',
          order_id,
        },
        toastLoading: false,
        onThen: ({ code, data: list, msg }) => {
          if (isArray(list) && list.length > 0) {
            resolve({ list, money: getServiceMoney(list) });
          } else {
            reject(new Error(code > 0 ? msg : '无增值服务'));
          }
        },
      });
    }
  });
};
