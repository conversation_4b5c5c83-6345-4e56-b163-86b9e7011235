

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


export const reasonList = [
  { id: 0, reason: '需求有变，不想寄了' },
  { id: 1, reason: '寄件信息填写错误' },
  { id: 2, reason: '联系不上快递员', resend: true },
  { id: 3, reason: '分配的快递员迟迟没有来', resend: true },
  { id: 4, reason: '不小心点错了' },
  { id: 5, reason: '其他原因' },
];

export const checkIsOther = ({ reason }) => reason === '其他' || reason === '其他原因';
