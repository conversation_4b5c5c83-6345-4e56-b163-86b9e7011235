/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import {
  createGroup,
  extractData,
  createListener,
  getPage,
  setStorage,
  noop,
  randomCode,
} from '@base/utils/utils';
import {
  addressKeys,
  addressKeysMap,
  getErrorAddressIndex,
  receiveStorageKey,
  sendStorageKey,
} from '@/components/_pages/address/_utils';
import { addEcode } from '@/components/_pages/ecode/_utils';
import request from '@base/utils/request';
import rules from '@base/utils/rules';
import isPlainObject from 'lodash/isPlainObject';
import isArray from 'lodash/isArray';
import merge from 'lodash/merge';
import apis from '@/utils/apis';
import { defaultFormData } from '@/components/_pages/order/_utils/order.edit';
import { markOrderPay, payOrder } from '@/components/_pages/order/_utils/order.detail';
import { cloneOrder } from './order.action';

Taro.realnameChecked = {};
export const updateOrderArrivePayPrice = (arrive_pay, order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/minpost/order/setOrderArrivePrice',
      toastSuccess: false,
      toastLoading: false,
      toastError: false,
      data: {
        order_id: order_id,
        arrive_pay,
      },
      onThen(res) {
        resolve(res);
      },
    });
  });
};
export const fillOrderArrivePayPrice = ({ action, data, order_id }) => {
  return new Promise((resolve, reject) => {
    const { is_arrive_pay, arrive_pay } = data;
    if (action.includes('print') && is_arrive_pay && !arrive_pay) {
      Taro.kbModal({
        template: [
          {
            tag: 'at-input',
            placeholder: '填写到付金额',
            value: '',
            circle: true,
            border: false,
            name: 'arrive_pay',
          },
          {
            className: 'kb-color__grey kb-size__base kb-spacing-md-t',
            value: '注：与驿站/快递员联系实际金额',
          },
        ],
        onConfirm: (e) => {
          const { data: { arrive_pay } = {} } = e;
          if (arrive_pay) {
            updateOrderArrivePayPrice(arrive_pay, order_id).then(({ code, msg }) => {
              if (code != 0) {
                Taro.kbToast({
                  text: msg,
                });
                reject();
              } else {
                resolve();
              }
            });
            return false;
          }
          Taro.kbToast({
            text: '请填写到付金额',
          });
          return true;
        },
      });
    } else {
      resolve();
    }
  });
};

export const globalOrderIDs = 'globalOrderIDs';

// 订单编辑地址信息列表
export const addressList = [
  {
    key: 'send',
    placeholder: ['从哪寄件？'],
    color: 'brand',
    tag: '寄',
    selector: true,
  },
  {
    key: 'receive',
    placeholder: ['寄去哪儿？'],
    color: 'green',
    tag: '收',
  },
];

// 中文状态转化对应
export const getOrderState = (name) => {
  const list = [
    { name: '已下单', key: 'wait', cancel: true },
    { name: '已取消', key: 'cancel' },
    { name: '已完成', key: 'done' },
  ];
  return {
    name,
    key: 'other',
    cancel: false,
    realname: true,
    ...list.find((item) => item.name === name),
  };
};

/**
 * @description 格式化地址
 * @description 默认 name=>send_name; reverse=true时,send_name=>name;
 */
export const formatAddress = (
  data = {},
  key,
  { reverse = false, keys = addressKeys, replace = {} } = {},
) => {
  let has = false;
  let res = {
    data: extractData(
      data,
      keys.map((item) => {
        let from = item;
        let to = `${key}_${item}`;
        if (reverse) {
          [to, from] = [from, to];
        }
        if (data[to] && !has) {
          has = true;
        }
        // 替换key
        const str = replace[item];
        if (str) {
          from = from.replace(item, str);
        }
        return [from, to];
      }),
    ),
    has,
  };
  return res;
};

// 检查联系方式组
export const checkContactGroup = (value, { rule, msg } = rules.contact) => {
  const list = `${value}`.split(/\s+|[,，;]+/g).filter((item) => !!item);
  let errorIndex = -1;
  for (let i = 0, len = list.length; i < len; i++) {
    if (!rule.test(list[i])) {
      errorIndex = i;
      break;
    }
  }
  if (errorIndex >= 0) {
    return msg;
  }
};
// 更新定制表单内容
export const fixDynamicFormsData = (data = {}, TemplateForm = null) => {
  // 设置标准 isShow:是否展示,locked:是否锁住,value:自定义值,placeholder:自定义提示,label:自定义label
  // 设置的数据可以为基本数据类型
  let dynamicForm = isPlainObject(TemplateForm)
    ? { ...TemplateForm }
    : process.env.MODE_ENV === 'wkd'
    ? {
        goods_name: { isShow: true }, //物品类型
        goods_weight: { isShow: true }, //物品重量
        goods_remark: { isShow: true }, //物品备注
        card: { isShow: false }, //权益次卡
        appointmentTime: { isShow: false }, //上门时间
        product_code: { isShow: false }, //服务类型
        volume: { isShow: false }, //体积
        service: { isShow: false }, //增值服务
        extra: { isShow: false }, //展示额外需求
        carType: { isShow: false }, //车型选择
      }
    : {
        goods_name: { isShow: true },
        goods_weight: { isShow: true },
        goods_remark: { isShow: true },
        brand: { isShow: true },
        card: { isShow: true },
      };

  dynamicForm = merge({}, dynamicForm, data);
  return dynamicForm;
};

export const freshList = [
  { label: '生鲜', key: 'sx' },
  { label: '水果', key: 'sg' },
  { label: '蔬菜', key: 'sc' },
  { label: '肉类', key: 'rl' },
  { label: '海鲜', key: 'hx' },
  { label: '干货', key: 'gh' },
];
export const packagesList = ['日用品', '数码产品', '衣物', '食物', '文件', '鞋靴'];
export const defaultGood = '日用品';
export const isFresh = (value) => {
  return value &&
    freshList.find((i) => {
      return i.label === value;
    })
    ? true
    : false;
};
export const serviceKeys = ['keep_account', 'cost_value'];
export const serviceConfirmKeys = ['keep_account', 'rate'];
export const brandKeys = ['brand', 'delivery_type', 'quotation', 'payTypes'];
export const brandInfoKeys = ['brand', 'delivery_type', 'quotationList', 'payTypes'];
export const reserve_time = ['reserve_start_time', 'reserve_end_time'];
export const goodsKeys = ['goods_name', 'goods_weight', 'volume', 'package_note', 'package_pics'];

// 生成表单
const unRequiredKeys = [
  'company',
  'service',
  ...goodsKeys,
  'save',
  'province_confidence',
  'city_confidence',
  'district_confidence',
];
const noStorageKey = ['quotation'];
const prefixs = {
  send: '发件人',
  receive: '收件人',
};
export function getFormItem({
  keys,
  data,
  form = {},
  prefix = '',
  clean = true,
  merge = {}, // 合入的表单配置项
}) {
  const formData = { ...data };
  keys.map((key) => {
    const formKey = prefix ? `${prefix}_${key}` : key;
    const data = {
      value: formData[formKey] || '',
      clean,
      storage: !noStorageKey.includes(formKey),
      required: !unRequiredKeys.includes(key),
      ...merge[formKey],
    };
    const addressKeyValue = addressKeysMap[key];
    if (addressKeyValue) {
      data.tag = (prefixs[prefix] || '') + addressKeyValue;
    }
    if (key === 'mobile') {
      data.customMsg = data.tag + '格式不正确';
      data.reg = 'contact';
      data.validator = checkContactGroup;
    }
    form[formKey] = data;
  });
  return form;
}
// 获取表单配置 action = order:创建订单；address:地址编辑
export const getForm = ({
  list = addressList,
  keys = addressKeys,
  action = 'order',
  data,
} = {}) => {
  const form = {};
  list.map((item) => {
    const prefix = item.key;
    getFormItem({
      keys,
      data,
      form,
      prefix,
      clean: action === 'address' ? true : prefix !== 'send',
    });
  });
  return form;
};

// 缓存实名状态
const cacheRealnameStatus = (key, data) => {
  let key_ = `${key}`;
  if (data) {
    const keys = Object.keys(Taro.realnameChecked);
    if (keys.length >= 5) {
      delete Taro.realnameChecked[keys[0]];
    }
    Taro.realnameChecked[key_] = data;
  } else {
    return Taro.realnameChecked.hasOwnProperty(key_) ? Taro.realnameChecked[key_] : null;
  }
};

function getApiUrlAndDataRealnameStatus(phone, needInfo, action) {
  let url;
  let data;
  let userkey = 'user_id';
  if (process.env.MODE_ENV === 'wkd') {
    if (phone) {
      url = '/v1/user/getRns';
      data = { mobile: phone };
    } else {
      url = '/v1/user/getRealnameStatus';
    }
  } else {
    // url =
    //   action !== 'self'
    //     ? `/api/weixin/mini/realname/auth/${needInfo ? 'realNameInfo' : 'getIdCardId'}`
    //     : '/api/weixin/mini/user/Bind/getUserRealnameStatus';
    url = action !== 'self' && !needInfo ? '/api/Realname/getRns' : '';
    data = { mobile: phone };
  }
  return {
    url,
    data,
    userkey,
  };
}

// 获取实名认证状态
export const getRealnameStatus = (phone, { needInfo = false, action = '', ...api } = {}) => {
  return new Promise((resolve, reject) => {
    // 去除结尾的连接符号
    let nphone = `${phone || ''}`.trim().replace(/[,，;\s]$/, '');
    if (process.env.MODE_ENV !== 'wkd') {
      let cacheData = null;
      if (action !== 'self' && (!nphone || !!checkContactGroup(nphone))) {
        reject(new Error('请输入手机号'));
        return;
      }
      cacheData = cacheRealnameStatus(nphone);
      if (cacheData && ((needInfo && cacheData.info) || !needInfo)) {
        resolve(cacheData);
        return;
      }
    } else {
      if (nphone && !!checkContactGroup(nphone)) {
        // 传入手机号，且手机号不合法跳过
        return;
      }
    }

    request({
      toastLoading: false,
      ...api,
      ...getApiUrlAndDataRealnameStatus(nphone, needInfo, action),
      mastLogin: false,
      onThen: ({ data }) => {
        console.log('data', data);
        let realnamed = data.status === '已认证';
        let info = null;
        if (action === 'self') {
          const { realname_status = realnamed ? 1 : 0, ...rest } = data;
          realnamed = realname_status === 1;
          info = { ...rest };
        } else if (isPlainObject(data)) {
          const { ...rest } = data;
          if (realnamed) {
            info = rest;
          }
        } else {
          realnamed = data == 1;
        }
        const result = {
          realnamed,
          info,
        };
        console.log('result', result);
        resolve(result);
        if (process.env.MODE_ENV !== 'wkd') {
          cacheRealnameStatus(nphone, result);
        }
      },
    });
  });
};

// 设置实名认证状态
export const setRealnameStatus = (key, data) => cacheRealnameStatus(key, data);

// 更新当前状态
export const updatePrintStatus = (data, page) => {
  const { activePrintType } = page;
  if (activePrintType) {
    data.status = activePrintType === 'print' ? '已打印' : '已完成';
    data.activePrintType = activePrintType;
    page.activePrintType = '';
  }
};

/**
 * 获取底单照片
 * @param { string } order_id 订单ID
 * @returns { Promise<string> }
 *  */
const getProofImage = (order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Order/prePrintOrder',
      nonceKey: 'order_id',
      toastLoading: true,
      toastError: false,
      toastSuccess: false,
      data: {
        order_id,
      },
    })
      .then((res) => {
        const { code, data = {} } = res;
        if (code == 0 && data.certificate_path) {
          resolve(data.certificate_path);
        } else {
          resolve('');
        }
      })
      .catch(() => {
        resolve('');
      });
  });
};

const viewProofImg = async ({ img, order_id, brand, waybill }) => {
  let path = img;
  if (!path) {
    path = await getProofImage(order_id);
  }
  console.log('path', path);
  if (!path) {
    Taro.kbToast({
      text: '暂无底单',
    });
    return;
  }

  Taro.navigator({
    url: 'order/voucher',
    options: {
      pic: path,
      brand,
      waybillno: waybill,
    },
  });
};

const batchMark = (order_ids) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Pay/batchMark',
      nonceKey: 'order_id',
      toastLoading: true,
      toastError: false,
      toastSuccess: false,
      data: {
        order_ids,
      },
    })
      .then((res) => {
        const { code, data = {} } = res;
        if (code == 0 && data) {
          resolve(data);
        } else {
          resolve('');
        }
      })
      .catch(() => {
        resolve('');
      });
  });
};

export const showCancelCabinetOrderModal = ({ order_id } = {}) => {
  return new Promise((resolve, reject) => {
    Taro.kbModal({
      top: false,
      title: '温馨提示',
      content: '您还没有选择格口开柜，将包裹放入柜中是否确认取消寄件?',
      cancelText: '取消订单',
      confirmText: '继续寄件',
      onConfirm: () => {
        resolve('confirm');
      },
      onCancel: (ev) => {
        if (ev === 'button') {
          confirmOrCancelOrder('cancel_order', {
            order_number: [order_id],
            reason: '用户主动取消微快递特惠寄快递柜订单',
          })
            .then((res) => {
              formatConfirmResult(res, 1, () => {
                resolve('cancel');
              });
            })
            .catch(reject);
        }
      },
    });
  });
};

// 订单操作
export const orderAction = (opts = {}, page = getPage(-1)) => {
  return new Promise((resolve, reject) => {
    const { action, data = page.state.data, count = 1, api, source } = opts;
    const {
      order_number,
      brand,
      reason = '',
      certificate_path,
      waybill,
      money,
      cabinet_record = {},
    } = data || {};
    switch (action) {
      case 'clone': // 再来一单
        cloneOrder(data, { api, count, action, source }, page).catch(reject);
        break;
      case 'cancel': // 取消订单
        confirmOrCancelOrder('cancel_order', { order_number, reason })
          .then((res) => {
            formatConfirmResult(res, 1, resolve);
          })
          .catch(reject);
        break;
      case 'pay': // 支付订单
        payOrder({ order_id: order_number })
          .then(resolve)
          .catch((e) => {
            const { status } = e;
            if (status) {
              Taro.kbToast({
                text: status,
              });
            }
            reject(e);
          });
        break;
      case 'payMark': // 标记支付
        markOrderPay({ order_id: order_number, money }).then(resolve).catch(reject);
        break;
      case 'confirm': // 确认订单
        Taro.navigator({
          url: 'order/confirm',
          key: 'routerParamsChange',
          options: {
            orders: [
              {
                order_id: order_number,
                brand: brand,
              },
            ],
          },
        });
        break;
      case 'print': // 打印面单
        if (brand == 'cnsd') {
          Taro.kbToast({
            text: '该品牌暂不支持打印',
          });
        } else {
          getPrintCode([order_number]);
        }
        break;
      case 'share': // 分享账单
        checkBeforeShareOrder(order_number).then((res) => {
          if (res) {
            Taro.kbToast({
              text: '推送成功',
            });
          } else {
            resolve({ hasPushed: res });
          }
        });
        break;
      case 'certificate': // 查看底单
        viewProofImg({
          order_id: order_number,
          img: certificate_path,
          waybill,
          brand,
        });
        break;
      case 'mark_order':
        batchMark(data)
          .then((res) => resolve(res))
          .catch(reject);
        break;
      case 'cancel_cabinet_order': // 取消快递柜订单
        showCancelCabinetOrderModal({
          order_id: data.order_id,
        }).then((action) => {
          if (action === 'cancel') {
            resolve();
          } else {
            reject();
          }
        });
        break;
      case 'open_cabinet': // 快递柜开柜
        console.log('open_cabinet==>data', data);

        Taro.navigator({
          url: 'kdg/express',
          options: {
            source: 'order',
            order_id: data.order_number,
            cabinet_id: cabinet_record.cabinet_id,
            pickupCode: cabinet_record.pickup_code,
            idFlag: `${cabinet_record.cabinet_code}${data.user_id}`,
            device_id: cabinet_record.device_id,
            brand: data.brand,
          },
        });
        break;
      case 'remind_order':
        Taro.navigator({
          url: 'order/remind',
          options: {
            order_id: data.order_number,
          },
        });
        break;
      default:
        break;
    }
  });
};

// 检查页面是否挂载地址编辑组件ref
function checkHasAddressRef(page) {
  return page.addressEditRef && page.addressEditRef.current;
}

// 设置地址信息缓存
function setAddressStorage(type, data) {
  switch (type) {
    case 'send':
      setStorage({
        key: sendStorageKey,
        data: data[0],
      });
      break;
    case 'receive':
      setStorage({
        key: receiveStorageKey,
        data,
      });
      break;

    default:
      break;
  }
}

// 设置收件人列表并寄件
export function createByAddressEdit(item, data) {
  const { type: key, action } = item;
  const dataItem = data[0] || {};
  console.log('key', item);
  if (process.env.MODE_ENV === 'wkd') {
    if (key === 'ecode-save') {
      //保存快递码
      addEcode(data[0], {
        toastLoading: false,
        toastSuccess: '添加成功！',
      });
      return;
    }
    // 微掌柜邀请下单或优惠寄
    if (action !== 'edit') {
      const page = getPage(-1);
      switch (key) {
        case 'receive':
          if (checkHasAddressRef(page)) {
            page.addressEditRef.current.getReceiveStorageListDebounce('reload', data);
          }
          break;
        case 'fix':
          Taro.navigator({
            url: 'address/edit',
            options: {
              ...dataItem,
              org: 'send',
              action: 'fix',
              fixFor: key,
            },
          });
          break;

        default:
          break;
      }

      return;
    }
  }

  if (item.typeSuffix == 'modify') {
    // 设置全局数据，以便在批量设置页获取
    Taro.kbSetGlobalData(receiveStorageKey, data);
    const current = getPage(-1);
    createListener('addressBatch', ({ list, sendAndReceive }) => {
      if (sendAndReceive) {
        // 同时更新寄收件人
        current.addressEditRef.current.updateSendAndReceive(sendAndReceive);
        return;
      }
      current.addressEditRef.current.updateFormDataByReceiveList(list);
    });
    Taro.navigator({
      url: 'address/batch',
      options: {
        action: 'edit',
      },
    });
    return;
  }

  Taro.navigator({
    url: 'order/edit',
    target: 'tab',
    onArrived: (page) => {
      if (checkHasAddressRef(page)) {
        // 地址编辑组件ref已挂载
        switch (key) {
          case 'send':
            setAddressStorage(key, data);
            page.addressEditRef.current.getDefaultAddress();
            break;
          case 'receive':
            function setAddressFn(rData) {
              let ndata = data;
              if (rData && rData.length > 1) {
                ndata = rData.concat(data);
              }
              setAddressStorage(key, ndata);
              page.addressEditRef.current.getReceiveStorageListDebounce('reload', ndata);
            }
            page.addressEditRef.current
              .checkReceiveStorageList()
              .then(setAddressFn)
              .catch(setAddressFn);
            if (process.env.MODE_ENV === 'wkd') {
              const { notice } = dataItem;
              if (notice) {
                // 快递码包含寄件提醒
                Taro.kbModal({
                  content: [
                    '如果给我寄快递，请注意：',
                    { text: notice, className: 'kb-color__black' },
                  ],
                  confirmText: '知道了',
                });
              }
            }
            break;
          case 'fix': // 完善地址
            Taro.navigator({
              url: 'address/edit',
              options: {
                ...dataItem,
                org: 'send',
                action: 'fix',
              },
            });
            break;
          default:
            break;
        }
      }
    },
  });
}

// 批量下单
const batchActionRef = {
  current: null,
  orderIds: [],
};

// 清空创建订单
export function cleanBatchSubmitOrderStatus() {
  // 忽略之前的打印数据，重新开始新的打印
  batchActionRef.current = null;
  batchActionRef.orderIds = [];
}

export function batchSubmitOrder(data, page) {
  // 触发请求
  return new Promise((resolve, reject) => {
    const { ignore = false, receiveList, relation_id, extra_info, order_info } = data;

    const { index, errMsg } = getErrorAddressIndex(receiveList);
    if (index >= 0) {
      // 有错误
      reject(new Error(errMsg));
      return;
    }
    if (ignore) {
      cleanBatchSubmitOrderStatus();
    }
    const size = 5; // 分组大小
    const { ...rest } = order_info;
    const sendInfo = formatAddress(formatAddress(rest, 'send').data, 'f', {
      reverse: true,
      replace: {
        mobile: 'phone',
        address: 'detail',
        district: 'county',
      },
    }).data;
    const goodsInfo = extractData(rest, goodsKeys);
    const brandInfo = extractData(rest, brandKeys);
    const serviceInfo = extractData(rest, [...serviceKeys, 'rate']);
    const groups = createGroup(receiveList, size);
    const triggerSubmit = (opts) => {
      const { index = 0, batch_id = '' } = opts || {};
      const groupItem = groups[index];
      if (!groupItem) {
        // 所有分组处理完毕：关闭loading，并清空提交状态
        Taro.kbToast(
          {
            text: '订单创建完成',
          },
          page,
        );
        const { orderIds } = batchActionRef;
        resolve({
          code: 0,
          data: {
            total: orderIds.length,
            order_id: orderIds,
          },
        });
        cleanBatchSubmitOrderStatus();
        return;
      }
      const { goods_name } = goodsInfo;
      const { product_type, reserve_time, brand } = brandInfo;
      // 收件人信息格式化

      let lockGroup = [['is_arrive_pay', 'arrive_pay'], 'collection'];

      const depthFind = (key, group) =>
        group.find((i) => {
          if (typeof i === 'string') {
            return i === key;
          }
          return i.includes(key);
        });

      const to_msg = groupItem.map((item) => {
        let lock = [];
        serviceConfirmKeys.forEach((key) => {
          const { service: info } = item.extraInfo || {};
          if (depthFind(key, lockGroup) && info && info[key]) {
            lock = lockGroup.filter((i) => {
              if (typeof i === 'string') {
                return i !== key;
              }
              return !i.includes(key);
            });
          }
        });
        return {
          ...extractData(
            { ...goodsInfo, ...item.extraInfo },
            [
              [
                'goods_name',
                (data) => {
                  if (data['goods_name'] === defaultFormData.goods_name) {
                    return goods_name;
                  } else {
                    return data.goods_name;
                  }
                },
              ],
              'goods_remark',
              'goods_weight',
              'package_pics',
              ...serviceConfirmKeys.map((key) => {
                return [
                  key,
                  (data) => {
                    let value = '';
                    if (data['service']) {
                      value =
                        data['service'][key] || (depthFind(key, lock) ? '' : serviceInfo[key]);
                    } else {
                      value = serviceInfo[key];
                    }
                    return value;
                  },
                ];
              }),
            ],
            goodsInfo,
          ),
          ...extractData(
            item,
            addressKeys.map((item) => (item === 'mobile' ? ['phone', 'mobile'] : item)),
          ),
        };
      });
      // 开始id
      const start_id = 1 + index * size;
      request(
        {
          url: '/order/BatchOrder/batchOrder',
          toastLoading: false,
          directTriggerThen: true,
          data: {
            ...sendInfo,
            relation_id,
            extra_info,
            brand,
            info: goods_name,
            to_msg,
            start_id,
            batch_id,
            product_type,
            reserve_time,
          },
          onThen: ({ code, msg, data }) => {
            if (code == 0) {
              const { batch_id, order_id } = data || {};
              batchActionRef.current = {
                batch_id,
                index: 1 + index,
              };
              if (isArray(order_id)) {
                batchActionRef.orderIds.push(...order_id);
              }
              // 继续提交
              triggerSubmit(batchActionRef.current);
            } else {
              let toastData = { text: msg };
              if (code == '10086') {
                // 地址错误
                groupItem.map((item) => {
                  item.error = 'whatever';
                });
                // 更新表单状态
                page.addressEditRef.current.updateFormDataByReceiveList();
                Taro.kbModal(
                  {
                    content: msg,
                    confirmText: '去修改',
                    closeOnClickOverlay: false,
                    onConfirm: () => {
                      page.addressEditRef.current.handleBatch({
                        action: 'correct',
                        index: start_id - 1,
                      });
                    },
                  },
                  page,
                );
                toastData = { isOpened: false };
              }
              Taro.kbToast(toastData, page);
            }
          },
        },
        page,
      );
    };
    Taro.kbToast(
      {
        status: 'loading',
      },
      page,
    );
    // 提交订单
    triggerSubmit(batchActionRef.current);
  });
}

export function wkd_batchSubmitOrder(data, page) {
  // 触发请求
  return new Promise((resolve, reject) => {
    const {
      reqData,
      receiveList,
      relationInfo: { type: relationType },
    } = data;
    let addrList = receiveList.map((item) => {
      let oItem = {};
      if (item.extraInfo) {
        const { goods_name, goods_weight, goods_remark, service } = item.extraInfo || {};
        const { keep_account, cost_value, collection, arrive_pay } = service || {};
        goods_name && (item.product = goods_name);
        goods_weight && (item.weight = goods_weight);
        goods_remark && (item.note = goods_remark);
        item.decVal = keep_account; //声明物品价值
        item.proPrice = cost_value; //保价
        item.collection_amount = collection; //代收货款
        item.to_pay_amount = arrive_pay; //到付
      }
      oItem = extractData(item, [
        ['shipping_name', 'name'],
        ['shipping_mobile', 'mobile'],
        ['shipping_province', 'province'],
        ['shipping_city', 'city'],
        ['shipping_district', 'district'],
        ['shipping_address', 'address'],
        ['package_info', 'product'],
        ['package_weight', 'weight'],
        ['package_note', 'note'],
        'decVal',
        'proPrice',
        'collection_amount',
        'to_pay_amount',
      ]);
      return oItem;
    });
    console.log('addrList', addrList);
    const triggerSubmit = () => {
      request(
        {
          url: '/g_order_core/v2/mina/Order/batchSend',
          directTriggerThen: true,
          data: reqData,
          formatRequest: (req) => {
            req.type = relationType == 'brand' ? 'online' : relationType;
            req.shipper_list = {
              shipper_name: req.shipper_name,
              shipper_mobile: req.shipper_mobile,
              shipper_province: req.shipper_province,
              shipper_city: req.shipper_city,
              shipper_district: req.shipper_district,
              shipper_address: req.shipper_address,
            };
            req.shipping_list = addrList;
            return req;
          },
          onThen: ({ code, msg, data }, req) => {
            if (code == 0) {
              let oData = {};
              if (data.status) {
                oData.ordersNum = req.shipping_list.length;
              }
              oData = {
                ...data,
                ...oData,
              };
              console.log('oData', oData);
              resolve({ code, msg, data: oData });
            } else {
              Taro.kbToast({
                text: msg,
              });
              reject({ code, msg, data });
            }
          },
        },
        page,
      );
    };
    // 提交订单
    triggerSubmit();
  });
}

export { addressKeys };

// 创建优惠券
export const createCustomCoupon = (params) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/Coupon/createCustomCoupon',
      data: { ...params },
      toastLoading: false,
      directTriggerThen: true,
      onThen({ data, code, msg }) {
        if (code == 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
};

//春节活动日期判断
function isDuringDate(beginDateStr, endDateStr) {
  var curDate = new Date(),
    beginDate = new Date(beginDateStr),
    endDate = new Date(endDateStr);
  if (curDate >= beginDate && curDate <= endDate) {
    return true;
  }
  return false;
}
export const isSpringFestival = isDuringDate('2022/1/25', '2022/2/7');

/**
 * @title 标记成员📌
 * @param { string } id 团员ID
 * @param { string } order_number 订单ID
 *  */
export const markMember = (id, order_number) => {
  return new Promise((resolve, reject) => {
    request({
      url: apis['order.list.mark'],
      toastSuccess: '标记成功',
      toastError: true,
      data: {
        label_user_id: id,
        order_number,
      },
    })
      .then((res) => {
        if (res.code == 0) {
          resolve();
        } else {
          reject();
        }
      })
      .catch((e) => {
        reject(e);
      });
  });
};

/**
 * @title 确认或取消订单
 * @param { string } action  cancel_order | confirm_order
 * @param { Array } order_number 订单ID
 *  */
export const confirmOrCancelOrder = (action, { order_number = [], ...rest }) => {
  return new Promise(async (resolve, reject) => {
    const isCancel = action === 'cancel_order';
    request({
      url: apis[`order.${isCancel ? 'cancel' : 'confirm'}`],
      toastLoading: true,
      data: {
        order_ids: order_number,
        ...rest,
      },
    })
      .then((res) => {
        const { code, data, msg } = res;
        if (code == 0) {
          const { success = [], error = [] } = data;
          resolve({
            success,
            error,
          });
        } else {
          reject(msg);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 * @title 确认或取消订单后的结果展示优化
 * @param { string } res
 * @param { number } total 订单总数
 *  */
export const formatConfirmResult = ({ success = [], error = [] }, total, onConfirm = noop) => {
  const errNum = error.length;
  const successNum = success.length || (total - errNum <= 0 ? 0 : total - errNum);
  const errInfo = error.map((val) => `${val.order_id}：${val.error_msg}`);
  if (total == 1) {
    Taro.kbToast({
      text: errNum ? `失败：${errInfo[0]}` : '成功',
      onClose: () => {
        if (errNum > 0) {
          return;
        }
        onConfirm();
      },
    });
    return;
  }
  Taro.kbModal({
    closable: false,
    closeOnClickOverlay: false,
    content: [`成功：${successNum} 单, 失败：${errNum} 单`, '失败原因：', ...errInfo],
    onConfirm,
  });
};

/**
 * 获取打印参数，唤起标签打王小程序
 *  */
export const getPrintCode = (order_numbers = []) => {
  const random = randomCode();
  request({
    url: '/api/Order/printOrder',
    toastError: true,
    data: {
      random,
      order_ids: order_numbers.join(','),
    },
  })
    .then((res) => {
      if (res.code == 0) {
        Taro.navigator({
          appId: 'wx5c9beefc805e6c2f',
          url: `pages/quanji/index?random=${random}`,
          target: 'half',
          // envVersion: 'trial', // 唤起体验版
        });
      }
    })
    .catch();
};

/**
 * 分享账单前，检查是否已推送过
 *  */
export const checkBeforeShareOrder = (order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Pay/pushWaitPayNotice',
      toastError: false,
      toastSuccess: false,
      data: {
        order_id,
      },
    })
      .then((res) => {
        if (res.code == 0) {
          resolve(true);
        } else {
          resolve(false);
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};
