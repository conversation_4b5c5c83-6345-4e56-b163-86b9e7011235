/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { extractData } from '@base/utils/utils';

export const tcjsAddressKeys = [
  'name',
  'mobile',
  'province',
  'city',
  'district',
  'address',
  'door',
  'longitude',
  'latitude',
];

export const tcjsAddressList = [
  {
    key: 'send',
    placeholder: ['从哪寄？'],
    color: 'green',
    tag: '寄',
  },
  {
    key: 'receive',
    placeholder: ['寄去哪儿？'],
    color: 'blue',
    tag: '收',
  },
];

export const mergeDataToFull = ({ data, prefix = '' }) => {
  const obj = {};
  for (let key in data) {
    obj[`${prefix ? prefix + '_' : ''}${key}`] = data[key];
  }
  return obj;
};

export const extractDataFromFull = ({ data, prefix = '' }) => {
  const obj = {};
  for (let key in data) {
    const [org1, org2] = key.split('_');
    if (prefix == org1) {
      obj[org2] = data[key];
    }
  }
  return obj;
};

export const getTcjsItemConfig = ({ brand }) => {
  const config = {
    dada: {
      goods_name: { isShow: true },
      goods_weight: { isShow: true },
      goods_remark: {
        isShow: true,
        maxLength: 40,
        placeholder: '选填，不超过40字',
      },
      appointmentTime: { isShow: true },
      service: { isShow: true },
    },
    ss: {
      goods_name: { isShow: true },
      goods_weight: { isShow: true },
      goods_remark: {
        isShow: true,
        maxLength: 40,
        placeholder: '选填，不超过40字',
      },
      appointmentTime: { isShow: true },
      service: { isShow: true },
    },
    huolala: {
      goods_remark: {
        isShow: true,
        only: true,
        maxLength: 40,
        placeholder: '选填，不超过40字',
      },
      appointmentTime: { isShow: true },
      extra: { isShow: true }, //展示额外需求
      carType: { isShow: true }, //车型选择
    },
  };
  const itemConfig = config[brand] || {};
  return itemConfig;
};

//转换标准微快递地址信息
export function transferTcjsAddress(data = {}, action = 'all') {
  let arr = [
    ['shipper_province', 'send_province'],
    ['shipper_city', 'send_city'],
    ['shipper_district', 'send_district'],
    ['shipper_address', 'send_address'],
    ['shipper_house_num', 'send_door'],
    ['send_lng', 'send_longitude'],
    ['send_lat', 'send_latitude'],

    ['receiver_province', 'receive_province'],
    ['receiver_city', 'receive_city'],
    ['receiver_district', 'receive_district'],
    ['receiver_address', 'receive_address'],
    ['receiver_house_num', 'receive_door'],
    ['receiver_lng', 'receive_longitude'],
    ['receiver_lat', 'receive_latitude'],
  ];
  if (action == 'all') {
    arr = arr.concat([
      ['shipper_name', 'send_name'],
      ['shipper_mobile', 'send_mobile'],
      ['receiver_name', 'receive_name'],
      ['receiver_phone', 'receive_mobile'],
    ]);
  }
  return extractData(data, arr);
}
