/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getCurrentUser, reportAnalytics } from '@base/utils/utils';
import request from '@base/utils/request';
import { login } from '@/utils/qy';
import { refreshControl, REFRESH_KEY_CREDIT } from '@/utils/refresh-control';
import isUndefined from 'lodash/isUndefined';

const WxCreditAppId = 'wxd8f3793ea3b935b8';

/**
 * 先享后付指微信支付分、支付宝芝麻分等产品的服务
 */
export const CreditConfig = {
  text: '微信支付分',
  icon: 'wxpay',
  colorClass: 'kb-color__green',
};

/**
 * 检查是否开通先享后付
 * @param {boolean} refresh 刷新
 * @param {boolean} isAdmin 区分团员团长
 */
export const checkCreditService = ({ refresh, isAdmin }) => {
  return new Promise((resolve) => {
    let isOpenCredit = Taro.kbGetGlobalData('isOpenCredit');
    const ForceRefresh = refreshControl(REFRESH_KEY_CREDIT, 'check') || refresh;
    if (!isUndefined(isOpenCredit) && !ForceRefresh) {
      resolve(isOpenCredit);
      return;
    }
    login()
      .then(({ code }) => {
        request({
          url: isAdmin
            ? '/api/Regiment/agreementServiceStatus'
            : '/api/Online/queryAfterServiceStatus',
          toastLoading: false,
          data: {
            code,
          },
          onThen: (res) => {
            isOpenCredit = res.code == 0 ? res.data == 1 : false;
            Taro.kbSetGlobalData('isOpenCredit', isOpenCredit);
            resolve(isOpenCredit);
          },
        });
      })
      .catch((err) => console.log(err));
  });
};

/**
 * 开通先享后付
 */
export const openCreditService = (data) => {
  const { source = '', balance, money } = data || {};
  reportAnalytics({
    key: 'wxpay_score',
    options: `点击开通-${source}`,
  });

  // 开通授权
  let url = getCurrentUser('regiment')
    ? '/api/Regiment/openAgreementService'
    : '/api/Online/openAfterServiceStatus';
  let reqData = {};
  // 团长单独开通服务
  // if (source == 'auth') {
  //   url = '/api/Regiment/openAgreementService';
  // }

  if (source == 'bind') {
    // 开通服务并交运费抵用金
    url = '/api/Regiment/openAgreementService';
    reqData = {
      is_deposit: 1,
    };
  }
  if (source == 'autoCharge') {
    // 开通自动充值
    url = '/api/Regiment/openAutoRechargeService';
    reqData = {
      balance,
      money,
    };
  }
  if (source == 'experience') {
    url = '/api/Regiment/openAgreementService';
    reqData = {
      is_experience: 1,
    };
  }

  console.log('openCreditService___', source, url, reqData);

  return new Promise((resolve, reject) => {
    refreshControl(REFRESH_KEY_CREDIT);
    request({
      url,
      data: reqData,
      toastError: true,
      onThen: ({ code, data }) => {
        if (code == 0) {
          const { miniprogram_path: path, extraData } = data || {};
          const appId = WxCreditAppId;
          if (path) {
            Taro.navigator({
              url: path,
              appId,
              extraData,
              report: {
                key: 'wxpay_score',
                options: '调用微信跳转接口',
              },
            })
              .then(() => {
                resolve({ appId });
              })
              .catch(reject);
          } else {
            resolve();
          }
        }
      },
    });
  });
};

/**
 * 检查是否从支付分开通页面返回（废弃）
 */
export const IsBackFromCredit = () => {
  return new Promise((resolve, reject) => {
    const { scene, referrerInfo } = Taro.getLaunchOptionsSync();
    if (scene == 1038 && referrerInfo && referrerInfo.appId == 'wxd8f3793ea3b935b8') {
      resolve();
    } else {
      reject();
    }
  });
};

// 获取信用支付信息描述
export function getCreditInfo(serviceStatus) {
  const isCustom = getCurrentUser('custom');
  if (serviceStatus) {
    return {
      image: `https://cdn-img.kuaidihelp.com/qj/miniapp/wechPay.png`,
      qa: [
        ...(isCustom
          ? {}
          : {
              q: '开通后可用于什么服务呢？',
              a: getCurrentUser('custom')
                ? '答: 开通后，用户在下单时无需任何费用扣款，在订单成功揽收后再根据实际快递价格进行微信支付扣款，为用户提供简答便捷的支付方式。'
                : '答: 创建一个支付分订单用于来替代缴纳200元运费抵用金，运费抵用金是平台用以规范用户寄件的预充值费用，保证平台与用户能够更好地享受快递服务。',
            }),
        {
          q: '怎么样关闭授权的支付分呢？',
          a: '答: 如果要关闭支付分授权【微信-我-服务-钱包-支付分-点击右上角-关闭微信支付分】进行解除授权；',
        },
      ],
    };
  }
  return {
    image: `https://cdn-img.kuaidihelp.com/qj/miniapp/wechPay.png`,
    qa: isCustom
      ? [
          {
            q: '微信支付分是什么？安不安全？',
            a: '答：支付分是微信官方推出的一种对个人的身份特质、支付行为、使用历史等情况的综合计算分值，旨在为用户提供更简单便捷的生活方式。官方服务，请放心使用。',
          },
          {
            q: '开通后有什么好处？',
            a: '开通后，寄件无需支付给快递员，微快递特惠寄平台将以货物实际揽收重量进行扣款，保障您的资金安全及寄件利益。',
          },
        ]
      : [
          {
            q: '微信支付分是什么？安不安全？',
            a: '答：支付分是微信官方推出的一种对个人的身份特质、支付行为、使用历史等情况的综合计算分值，旨在为用户提供更简单便捷的生活方式。官方服务，请放心使用。',
          },
          // {
          //   q: '开通后有什么好处？',
          //   a: '①开通先寄后付并自动绑定一个支付分订单后，可用于押金保证，您在使用微快递特惠寄服务时无需进行200元的运费抵用金充值。②钱包余额不足时，可通过支付分进行自动充值金额，避免因余额不足导致客户无法下单造成的损失。',
          // },
        ],
  };
}
