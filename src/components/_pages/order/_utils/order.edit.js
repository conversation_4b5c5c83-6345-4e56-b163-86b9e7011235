/* eslint-disable no-param-reassign */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import {
  extractData,
  createGroup,
  getCurrentUser,
  removeStorageSync,
  setStorage,
  getStorageSync,
  getPage,
  checkIsUserInfoComplete,
} from '@base/utils/utils';
import request from '@base/utils/request';
import {
  checkCreditService,
  openCreditService,
} from '@/components/_pages/order/_utils/order.credit-pay';
import { requestPayment } from '@/utils/qy';
import { getLaunchParams } from '@base/utils/navigator';
import { scanAction } from '@/utils/scan';
import { loginStorageKey } from '@/utils/config';
import { createFeeListExtend, createShowPrice } from '../fee-list/_utils';
import { bindInviteRelation } from '../../team/_utils';
import { formatAddress } from '@/components/_pages/order/_utils';
import { checkDataComplete } from '@/components/_pages/address/_utils/index';
import isArray from 'lodash/isArray';
import numeral from 'numeral';

/**
 *
 * @description 清空订单编辑表单信息
 * @param {*} keys
 * @param {*} replaceData
 */
export const cleanOrderEditFormInfo = (keys, replaceData = {}) => {
  const cleanDataMap = {};
  keys.map((item) => {
    cleanDataMap[item] = {
      clean: true,
      ...replaceData[item],
    };
  });
  return cleanDataMap;
};

/**
 * @description 检测支付分开通情况
 */
export const handleCheckCreditService = function ({ isAdmin } = {}) {
  checkCreditService({ isAdmin }).then((isOpenCredit) => {
    this.setState(
      {
        isOpenCredit,
      },
      () => {
        if (isOpenCredit && this.CreditFlag) {
          this.CreditFlag = false;
          this.handleSubmitForm();
        }
      },
    );
  });
};

/**
 * @description 执行开通支付分逻辑
 */
export const handleOpenCredit = function () {
  openCreditService().then(() => {
    this.CreditFlag = true;
  });
};

export const storageFormKey = 'order.edit.form';

export const storageDhFormKey = 'order.edit.form.dh';

export const defaultFormData = {
  goods_name: '日用品',
};

export const getUserRegimentConfig = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Team/userRegimentConfig',
      onThen: (res) => {
        const { code, data } = res || {};
        if (code === 0) {
          resolve(data);
        } else {
          reject();
        }
      },
    });
  });
};

let realNameInfo = null;
/**
 * 获取团长实名信息
 *  */
export const checkAuthRealName = () => {
  return new Promise((resolve) => {
    if (realNameInfo) {
      const { status } = realNameInfo;
      if (status == 1) {
        resolve(realNameInfo);
        return;
      }
    }
    request({
      url: '/api/Regiment/regimentRealnameStatus',
      toastLoading: false,
    })
      .then((res) => {
        const { code, data = {} } = res || {};
        if (code == 0) {
          const value = {
            name: data.id_card_name,
            id_card_number: data.id_card_number,
            status: data.regiment_realname_status,
            is_show: data.is_show == 1,
          };
          realNameInfo = value;
          resolve(value);
        } else {
          resolve({});
        }
      })
      .catch(() => {
        resolve({});
      });
  });
};

/**
 * 支付服务费
 * @param orderData 下单数据
 *  */
export const payServiceFee = (orderData = {}) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Pay/payServiceChargeSign',
      toastError: true,
      toastLoading: true,
      data: orderData,
    })
      .then((res = {}) => {
        const { code, data = {} } = res;
        const { sign = {}, order_id } = data;
        if (code == 0) {
          requestPayment(sign)
            .then(() => {
              resolve(order_id);
            })
            .catch(() => {
              reject({ status: '支付失败' });
            });
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * 查询是否下单成功
 *  */
export const checkOrderSubmitStatus = (order_id) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Online/getCallbackCreateOrderStatus',
      data: {
        order_id,
      },
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    }).then((res) => {
      const { code, msg } = res;
      if (code == 0 || code == 4000) {
        let status = code == 0 ? 1 : 0;
        resolve({
          status,
          msg,
        });
      } else {
        resolve({
          status: 2, // 失败
          msg,
        });
      }
    });
  });
};

/**
 * 轮询检查是否下单成功
 *
 * @param {number} order_number 单号
 * @param {number} isAdmin 区分团员团长
 *  */
export const loopCheckOrderSubmitStatus = (order_number) => {
  return new Promise(async (resolve, reject) => {
    let time = 0;
    const { status, msg } = (await checkOrderSubmitStatus(order_number)) || {};
    if (status) {
      if (status == 1) {
        resolve(status);
        return;
      }
      if (status == 2) {
        reject(msg);
        return;
      }
    }

    const loop = (params) => {
      let timer = setTimeout(() => {
        checkOrderSubmitStatus(params).then(({ status, msg }) => {
          if (status == 1) {
            time = 0;
            resolve(status);
          } else if (status == 0) {
            if (time > 6) {
              reject();
              clearTimeout(timer);
              return;
            }
            time += 1;
            loop(params);
          } else if (status == 2) {
            reject(msg);
            clearTimeout(timer);
          }
        });
      }, 1500);
    };
    loop(order_number);
  });
};

/**
 * 获取团员服务费
 *  */
export const getMemberServiceCharge = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/Team/getMemberServiceCharge',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        if (res.code == 0) {
          resolve(res.data);
        } else {
          resolve(0);
        }
      })
      .catch(() => {
        resolve(0);
      });
  });
};

/**
 * 京东服务费提示文案
 *  */
export const checkServicesFee = (address = {}) => {
  const info = {
    text: '',
    shouldPay: true,
  };
  return new Promise((resolve) => {
    request({
      url: '/api/Pay/checkPayServiceCharge',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
      data: {
        shipping_province: address.shipping_province,
        shipper_province: address.shipper_province,
      },
    })
      .then((res) => {
        const { code, data } = res || {};
        const { is_pay, desc } = data || {};
        if (code == 0) {
          info.shouldPay = is_pay || is_pay == 1;
          info.text = desc;
          resolve(info);
        } else {
          resolve(info);
        }
      })
      .catch(() => {
        resolve(info);
      });
  });
};

/**
 * 批量提交订单
 */
const formatBatchOrderData = (receiveList = [], sendInfo = {}) => {
  const { claiming_value, warrant_price, package_info, package_weight, place_volume } = sendInfo;
  let list = [];
  if (receiveList && receiveList.length > 0) {
    list = receiveList.map((item) => {
      let oItem = {};
      const { goods_name, goods_weight, package_note, service, volume = {} } = item.extraInfo || {};
      const { keep_account, cost_value } = service || {};
      item.package_info = goods_name || package_info;
      item.package_weight = goods_weight || package_weight;
      item.package_note = package_note || sendInfo.package_note;
      item.place_volume = volume.volume || place_volume || 0;
      item.claiming_value = keep_account || claiming_value; //声明物品价值
      item.warrant_price = cost_value || warrant_price; //保价

      oItem = extractData({ ...sendInfo, ...item }, [
        ['shipping_name', 'name'],
        ['shipping_mobile', 'mobile'],
        ['shipping_province', 'province'],
        ['shipping_city', 'city'],
        ['shipping_district', 'district'],
        ['shipping_address', 'address'],
        'shipper_province',
        'shipper_city',
        'shipper_district',
        'shipper_address',
        'shipper_name',
        'shipper_mobile',
        'shipper_default',
        'shipping_default',
        'package_info',
        'package_weight',
        'package_note',
        'place_volume',
        'claiming_value',
        'warrant_price',
      ]);
      return oItem;
    });
  }
  return list;
};

export function batchSubmitOrder(data) {
  // 触发请求
  return new Promise((resolve, reject) => {
    const { reqData = {} } = data;
    let addrList = formatBatchOrderData(this.receiveList, reqData);
    const MAX_NUM = 2000;
    const groups = createGroup(addrList, MAX_NUM);
    const groupLength = groups.length;
    let index = 0;
    const triggerSubmit = (index = 0, extraReqData = {}) => {
      const list = groups[index];
      request(
        {
          url: '/api/BatchOrder/batchSubmit',
          directTriggerThen: true,
          toastSuccess: false,
          toastLoading: '正在提交订单数据，请您耐心等待...',
          data: {
            ...reqData,
            ...extraReqData,
          },
          formatRequest: (req) => {
            req.addressList = list;
            const params = extractData(req, [
              'reserve_start_time',
              'reserve_end_time',
              'brand',
              'delivery_type',
              'arrivePay',
              'source',
              'addressList',
              'isDiscount',
              'claiming_value',
              'warrant_price',
            ]);
            // 恶意下单拦截-校验通过
            if (req.order_interception) {
              params.order_interception = req.order_interception;
            }
            return params;
          },
          onThen: ({ code, msg, data }, req) => {
            // console.log('批量下单===>请求参数===>', req);
            const { async_create_order, batch_number } = data || {};
            if (code == 0) {
              console.log('groupLength', groupLength, index + 1);
              if (groupLength == index + 1) {
                // 订单上传完成
                if (async_create_order == 1) {
                  // 异步模式
                  this.checkAsyncBatchStatus(data);
                  resolve({ code, msg, data });
                } else {
                  // 同步模式
                  let oData = {
                    ordersNum: req.addressList.length,
                    ...data,
                  };

                  resolve({ code, msg, data: oData });
                }
              } else {
                // 订单上传中
                index++;
                triggerSubmit(index, { batch_number, multi_batch: 1 });
              }
            } else if (code === BAD_ORDER_INTERCEPT_CODE) {
              if (this.handleBadOrderIntercept) {
                this.handleBadOrderIntercept('open');
              }
            } else {
              Taro.kbToast({
                text: msg,
              });
              reject({ code, msg, data });
            }
          },
        },
        this,
      );
    };
    // 提交订单
    triggerSubmit(index, { multi_batch: groupLength > 1 ? 1 : 0 });
  });
}

/**
 * @description 生成价格明细，展示原价或折扣价
 * 总体逻辑：当成本价大于原价，不展示原价
 * 价格明细展示逻辑：
 * 1、团长角色
 *      月结：根据团长配置的priceShowType来配置（1：展示原价，2：展示成本价，3、同时展示原价与成本价）
 *      到付|现付：展示原价（京东展示成本价）
 * 2、团员角色
 *      月结：展示成本价与原价
 *      到付|现付：展示原价（京东展示成本价与原价）
 * priceShowType
 * 1 仅展示原价
 * 2 仅显示成本价
 * 3 同时展示成本价和原价
 *  */
export const createFeeDetail = ({
  payType,
  quotation,
  costValue,
  isAdmin,
  priceShowType,
  payDiscount,
  source,
  receiveList = [],
}) => {
  // 价格
  const { originFee: originPrice, discountFee: costPrice } = createShowPrice({
    originKey: 'original_price',
    discountKey: 'price',
    quotation,
    isAdmin,
    payType,
    priceShowType,
    payDiscount,
  });
  // 续重价
  const { originFee: original_s_fee, discountFee: s_fee } = createShowPrice({
    originKey: 'original_s_fee',
    discountKey: 's_fee',
    quotation,
    isAdmin,
    payType,
    priceShowType,
    payDiscount,
  });

  const { feeList, total_costPrice, total_originPrice } = createFeeListExtend({
    quotation,
    costValue,
    priceShowType,
    payType,
    isAdmin,
    payDiscount,
    costPrice,
    originPrice,
    source,
    receiveList,
  });

  return {
    costPrice: costPrice ? numeral(costPrice).format('0.00') : 0,
    originPrice: originPrice ? numeral(originPrice).format('0.00') : 0,
    s_fee: s_fee ? numeral(s_fee).format('0.00') : 0,
    original_s_fee: original_s_fee ? numeral(original_s_fee).format('0.00') : 0,
    payFee: quotation.price, // 成本价（团长端显示）
    feeList,
    total_costPrice,
    total_originPrice,
  };
};

/**
 * 德邦线下8折渠道
 *  */
// 折扣值
export const dpOfflineDiscountRate = 9;
// 获取德邦线下8折渠道
export const checkDpOfflineOrder = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/Online/checkDpOfflineOrder',
      toastLoading: false,
      toastError: false,
      toastSuccess: false,
    })
      .then((res) => {
        const { data = {} } = res || {};
        if (res.code == 0) {
          resolve(data);
        } else {
          resolve({});
        }
      })
      .catch(() => {
        resolve({});
      });
  });
};

/**
 * 初始支付类型
 *  */
export const initPayTypesOptions = ({ source } = {}) => {
  const isCustom = getCurrentUser('custom');
  const wxCreditPay = {
    label: '货物揽收后通过授权微信支付分或钱包余额自动划扣至平台',
    title: isCustom ? '先寄后付' : '付款给平台(先寄后付)',
    value: 0,
  };
  const offlinePay = {
    label: `寄件人线下向揽收快递员付款`,
    title: '付款给快递员',
    value: 2,
  };
  const arrivePay = {
    label: `快递签收时，收方当面付款给快递员`,
    title: '收方付',
    value: 1,
  };
  let list = [wxCreditPay, offlinePay];
  if (source === 'big_package') {
    list = [wxCreditPay, offlinePay, arrivePay];
  } else if (source === 'arrive_pay_order') {
    list = [arrivePay];
  }
  return list;
};

/**
 * @description 获取订单编辑页面的启动参数
 */
export const getEditPageLunchParams = function (page, opts) {
  return new Promise((resolve) => {
    const lunchParams = getLaunchParams(page, opts);
    scanAction()
      .then((res = {}) => {
        resolve({ ...res, ...lunchParams });
      })
      .catch(() => {
        resolve(lunchParams);
      });
  });
};
/**
 * @description 团员注册
 */
export const registerUser = function ({ bindId, invite_type, user_type }) {
  return new Promise((resolve) => {
    bindInviteRelation(decodeURIComponent(bindId), invite_type, user_type)
      .then((data) => {
        removeStorageSync(loginStorageKey);
        Taro.kbLogin(true);
        resolve(data);
      })
      .catch(resolve);
  });
};

export const checkAddressComplete = (data) => {
  if (!data) return false;
  return (
    checkDataComplete(formatAddress(data, 'send').data).complete &&
    checkDataComplete(formatAddress(data, 'receive').data).complete
  );
};

/**
 * 获取某个品牌报价单情况
 * @param {*} brand
 * @param {*} list
 * @param {*} delivery_type
 * @returns
 */
export const getCurQuotationItem = (brand, list = [], delivery_type) => {
  if (!brand || !list || list.length <= 0) {
    return null;
  }
  return list.find((i) =>
    delivery_type && i.delivery_type
      ? i.brand === brand && i.delivery_type == delivery_type
      : i.brand === brand,
  );
};

// 格式化解析图片，统一为数组
export function formatParseImage(parseImg) {
  return parseImg
    ? isArray(parseImg)
      ? parseImg.map((item) => ({
          ...item,
          style: `width:${item.width}px;height:${item.height}px;max-width:100%;`,
        }))
      : [parseImg]
    : null;
}
export function previewParseImage(current, urls) {
  Taro.previewImage({
    current: current.img || current,
    urls: isArray(urls) ? urls.map((item) => item.img) : [urls],
  });
}

export function setAndPreviewParseImage(that, key, data) {
  switch (key) {
    case 'img':
      const { parseImg } = data || {};
      that.setState({ parseImg: formatParseImage(parseImg) });
      break;
    case 'preview':
      previewParseImage(data, that.state.parseImg);
      break;
  }
}

export const orderEditAgreementKey = 'orderEditAgreementKey';

export const setOrderEditAgreement = (agree) => {
  setStorage({
    key: orderEditAgreementKey,
    data: agree,
  });
};

export const getOrderEditAgreement = () => {
  const res = getStorageSync(orderEditAgreementKey);
  return res && res.data ? true : false;
};

export const createDynamicForms = (oBrandItem) => {
  if (oBrandItem) {
    const { weightLimitMin, weightLimitMax, dynamicForms = {} } = oBrandItem;
    return {
      ...dynamicForms,
      weightLimitMin,
      weightLimitMax,
    };
  }
};

export const BAD_ORDER_INTERCEPT_CODE = 202410151009;

export function handleBadOrderIntercept(key) {
  console.log('handleBadOrderIntercept', key);
  switch (key) {
    case 'open': {
      this.setState({
        badOrderInterceptIsOpen: Math.random() + 1,
      });
      break;
    }
    case 'complete': {
      this.formIns.submit({
        order_interception: true,
      });
      break;
    }
    case 'close': {
      this.setState({
        submitting: false,
      });
      break;
    }
  }
}

// 检查是否加收费(https://tower.im/teams/258300/todos/109436/)
export const checkIsAddFee = (params = {}) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Online/submitPreDetection',
      toastLoading: false,
      toastError: true,
      data: params,
      onThen: ({ code, data }) => {
        resolve(code == 0 && data && data.message ? data : false);
      },
    });
  });
};

export const showAddFeeModal = (opt) => {
  const { title, message } = opt || {};
  return new Promise((resolve) => {
    Taro.kbModal({
      rootClass: 'kb-addFee-modal',
      top3: {
        text: title,
      },
      closable: false,
      cancelButtonProps: {
        circle: true,
        type: 'secondary',
      },
      content: message,
      cancelText: '我再想想',
      confirmText: '确认下单',
      onConfirm: () => {
        resolve();
      },
    });
  });
};

export const getBrandOrderEditTips = (opt) => {
  const { brandInfo, goods, oBrandItem } = opt || {};
  const { brand } = brandInfo || {};
  const { goods_weight, volume } = goods || {};
  const { brand_weight } = oBrandItem || {};
  let tips = '';
  const weight_tips_map = {
    zt: '中通限重50KG，订单超重可能被拒绝揽收，请知悉。',
    jt: '极兔限重50KG，订单超重无法下单，请知悉。',
  };
  const volume_tips_map = {
    zt: '中通单边不超1.5m，订单超长可能被拒绝揽收，请知悉。',
  };
  if (weight_tips_map[brand] && (goods_weight > 50 || brand_weight > 50)) {
    tips = weight_tips_map[brand];
  } else if (
    volume_tips_map[brand] &&
    volume &&
    volume.checked &&
    (volume.width > 150 || volume.length > 150 || volume.height > 150)
  ) {
    tips = volume_tips_map[brand];
  }
  return tips;
};

// 注册后，拦截部分操作，让用户编辑用户信息
export const interceptAndEditUserInfo = () => {
  const page = getPage();
  const { registerUserAndNeedInfoLock } = page;
  const { userInfo } = Taro.kbLoginData || {};
  const isFenxiao = getCurrentUser('custom-f');
  const isUserInfoComplete = checkIsUserInfoComplete(userInfo);
  if (isFenxiao && registerUserAndNeedInfoLock && !isUserInfoComplete) {
    page.setState({
      openAuthUserInfoModal: Math.random() + 1,
    });
    page.registerUserAndNeedInfoLock = false;
    return true;
  }
  page.registerUserAndNeedInfoLock = false;
  return false;
};
