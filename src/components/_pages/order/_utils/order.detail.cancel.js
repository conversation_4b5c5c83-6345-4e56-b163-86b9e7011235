/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

/**
 * 获取中通批量订单
 *  */
export const getZtoBatchOrders = (batch_number) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Order/batchNumberOrderList',
      toastLoading: true,
      toastError: true,
      toastSuccess: false,
      data: { batch_number },
    })
      .then((res) => {
        const { code, data } = res;
        if (code == 0 && data && data.length > 0 && data.length <= 3) {
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

export const cancelReasonList = [
  {
    title: '客户相关',
    key: 'customer',
    child: [
      {
        id: '1',
        value: '下单信息有误，需要修改',
      },
      {
        id: '2',
        value: '计划有变，不需要寄了',
      },
      {
        id: '3',
        value: '换个时间再寄',
      },
      {
        id: '4',
        value: '去服务点自寄',
      },
    ],
  },
  {
    title: '快递员/服务相关',
    key: 'serve',
    child: [
      {
        id: '5',
        value: '送达时间达不到我的要求',
      },
      {
        id: '6',
        value: '没有包装材料',
      },
      {
        id: '7',
        value: '说我的东西不能寄',
      },
      {
        id: '8',
        value: '运费太贵了',
      },
      {
        id: '9',
        value: '快递员未及时取件',
      },
      {
        id: '10',
        value: '快递员不上门',
      },
      {
        id: '11',
        value: '快递员服务态度差',
      },
    ],
  },
];
