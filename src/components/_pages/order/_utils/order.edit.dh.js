/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { debounce } from '@base/utils/utils';
import { getQuotation } from '@/components/_pages/order/_utils/qj.index';
import { createDHServiceDesc } from '../edit/dh/service/_utils/desc';

export const serviceIntroductionMap = [
  {
    label: '包装费',
    value: 'bzf',
    url: 'https://m.kuaidihelp.com/f/packingFee',
  },
  {
    label: '回单费',
    value: 'back_sign_bill',
  },
  {
    label: '上楼费',
    value: 'pickup_way',
  },
  {
    label: '取货爬楼费',
    value: 'pickup_way',
  },
  {
    label: '派货爬楼费',
    value: 'pickup_way',
  },
  {
    label: '特殊区域费用',
    value: 'teshuquyu_fee',
  },
  {
    label: '更改单手续费',
    value: 'ggdsxf',
  },
  {
    label: '拆托服务费',
    value: 'ctfwf',
  },
  {
    label: '收配送区域加收费',
    value: 'spsqyjsf',
  },
  {
    label: '超长费',
    value: 'ccf',
  },
  {
    label: '超重费',
    value: 'czf',
  },
  {
    label: '超区费',
    value: 'cqf',
  },
  {
    label: '拆木架费',
    value: 'cmjf',
  },
  {
    label: '多次派送费',
    value: 'dcpsf',
  },
  {
    label: '等通知派送服务费',
    value: 'dtzpsfwf',
  },
  {
    label: '等通知服务费',
    value: 'dtzfwf',
  },
  {
    label: '修改地址费',
    value: 'xgdzf',
  },
  {
    label: '修改服务费',
    value: 'xgfwf',
  },
  {
    label: '空跑费',
    value: 'kpf',
  },
  {
    label: '夜间取货费',
    value: 'yjqhf',
  },
  {
    label: '入仓费',
    value: 'rcf',
  },
];

export const getIntroductionConfig = (name, brand) => {
  const item = serviceIntroductionMap.find((i) => i.label.includes(name) || name.includes(i.label));
  if (item && item.label) {
    if (item.url) {
      let url = item.url;
      if (item.label.includes('包装费')) {
        url = `${item.url}${item.url.includes('?') ? '&' : '?'}brand=${brand}`;
      }
      return {
        ...item,
        url,
      };
    }
    // 特殊逻辑
    if (
      (brand == 'htky' && ['pickup_way', 'teshuquyu_fee'].includes(item.value)) ||
      item.value === 'yjqhf' ||
      item.value === 'rcf'
    ) {
      return item;
    }
    // 配置的服务介绍存在
    const oDHServiceDesc = createDHServiceDesc({ brand }) || {};
    const arr = oDHServiceDesc[item.value] || [];
    if (arr && arr.length > 0) {
      return item;
    }
  }
  return null;
};

export const formatServiceData = (quotationData) => {
  const { service_price = [] } = quotationData || {};
  const _service_price = [...(service_price || [])];
  // 揽收超区费
  const lscqFeeIndex = _service_price.findIndex((i) => i.service_code === 'lanshouchaoqu');
  let lscqItem = {};
  if (lscqFeeIndex > -1) {
    lscqItem = _service_price[lscqFeeIndex];
  }
  // 派送超区费
  const pscqFeeIndex = _service_price.findIndex((i) => i.service_code === 'cqf_fee');
  let pscqItem = {};
  if (pscqFeeIndex > -1) {
    pscqItem = _service_price[pscqFeeIndex];
  }

  // 保价费
  const insuranceFeeIndex = _service_price.findIndex((i) => i.service_code === 'insuranceFee');
  let insuranceItem = {};
  if (insuranceFeeIndex > -1) {
    insuranceItem = _service_price[insuranceFeeIndex];
  }

  // 增值服务总计
  let service_total_fee = 0;
  if (_service_price && _service_price.length > 0) {
    _service_price.map((item) => {
      service_total_fee = service_total_fee + item.amount * 1 || 0;
    });
    service_total_fee = service_total_fee.toFixed(2);
  }

  return {
    lscqItem,
    pscqItem,
    insuranceItem,
    service_price: _service_price,
    service_total_fee,
  };
};

export const getProPrice = (opt = {}) => {
  const { brand } = opt || {};
  return new Promise((resolve) => {
    const loader = debounce(
      () => {
        getQuotation(opt, {
          toastLoading: true,
          quickTriggerThen: true,
        }).then((list) => {
          if (list && list.length > 0) {
            const quotationData = list.find((i) => i.brand === brand);
            const { insuranceItem } = formatServiceData(quotationData);
            const val = (insuranceItem && insuranceItem.amount * 1) || 0;
            if (quotationData.unavailable_msg) {
              Taro.kbToast({
                text: quotationData.unavailable_msg,
              });
            } else if (val <= 0) {
              Taro.kbToast({
                text: '暂未获得保价金额',
              });
            }
            const proPrice = (insuranceItem && insuranceItem.amount * 1) || 0;
            resolve(proPrice);
          } else {
            resolve(0);
          }
        });
      },
      1500,
      { leading: true, trailing: true },
    );
    loader();
  });
};
