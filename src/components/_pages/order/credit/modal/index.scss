

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-curtain {
  &--container {
    &-label {
      color: $color-black-1;
      font-size: $font-size-xl;
      text-align: center;
    }
    &-icon {
      width: 26px;
      height: 26px;
    }
    &-content {
      width: auto !important;
      background: #f9f9f9;
      border-radius: $border-radius-lg;
    }
    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

.kb-color__grey-9 {
  color: #f9f9f9;
}
