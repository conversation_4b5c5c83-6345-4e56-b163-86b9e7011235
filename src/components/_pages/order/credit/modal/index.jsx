/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import KbCurtainModal from '@/components/_pages/order/curtain-modal';
import wxpayIcon from '@/assets/icon/payment.png';
import { AtIcon } from 'taro-ui';
import { getCurrentUser } from '@base/utils/utils';
import './index.scss';

const contentMap = [
  { label: '高效寄件', icon: 'send' },
  { label: '自动扣费', icon: 'cost' },
  { label: '减少接触', icon: 'security' },
];

const modalHeadConfig = () => {
  const isCustom = getCurrentUser('custom');
  let desc = '由于您的团长设置，下单时您需要授权支付分，否则无法完成下单!';
  if (isCustom) {
    desc = '为了给您优质的下单体验，下单时您需要授权微信支付分，否则无法完成下单！';
  }
  return {
    headColor: 'blur-linear',
    tag: '微信官方服务',
    title: '微信支付分',
    desc,
    icon: 'dunpai',
  };
};

function Index(props) {
  const { isOpened, onConfirm, onClose } = props || {};
  const handleConfirm = () => {
    onConfirm();
  };
  const handleClose = () => {
    onClose();
  };
  return (
    <KbCurtainModal
      confirmText='立即授权'
      headColor='blur-linear'
      isOpened={isOpened}
      onConfirm={handleConfirm}
      onClose={handleClose}
      head={modalHeadConfig()}
    >
      <View className='kb-curtain--container'>
        <View className='kb-curtain--container-label kb-spacing-lg-t kb-size__bold'>
          微信支付分优势?
        </View>
        <View className='kb-color__grey at-row at-row__align--center kb-spacing-lg'>
          <Image className='kb-curtain--container-icon' src={wxpayIcon} />
          <View className='kb-size__sm kb-spacing-sm-l'>
            微信支付分 | 450分及以上享 <Text className='kb-color__green'>【先寄后付】</Text>
            及以下特权
          </View>
        </View>
        <View className='kb-spacing-lg kb-curtain--container-content at-row'>
          {contentMap.map((item) => {
            return (
              <View className='at-col at-col-4 kb-curtain--container-item' key={item.icon}>
                <View className='kb-margin-md kb-icon__around kb-icon__around--small kb-icon__around--brand-lighter'>
                  <AtIcon
                    prefixClass='kb-icon'
                    className='kb-icon-size__lg kb-color__grey-9'
                    value={item.icon}
                  />
                </View>
                <View className='kb-color__grey-1 kb-size__base'>{item.label} </View>
              </View>
            );
          })}
        </View>
      </View>
    </KbCurtainModal>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
