

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { isSpringFestival } from '@/components/_pages/order/_utils';
import KbCreditLabel from '@/components/_pages/order/credit/credit-label';
import { openCreditService } from '@/components/_pages/order/_utils/order.credit-pay';

function Index(props) {
  const { open = false } = props;
  const handleOpenCredit = () => {
    openCreditService();
  };
  return (
    <View>
      {open ? (
        <Fragment>
          <View className='at-row at-row__justify--center at-row__align--center kb-size__sm'>
            <KbCreditLabel mode='mini' msg='微信支付分|信用优享' />
          </View>
        </Fragment>
      ) : (
        <View
          className='at-row at-row__justify--center at-row__align--center kb-size__sm'
          onClick={handleOpenCredit}
        >
          开通“
          <Fragment>
            <AtIcon
              prefixClass='kb-icon'
              value='wxpayflag'
              className='kb-icon-size__base kb-color__green kb-margin-xs-b'
            />
            微信支付分
          </Fragment>
          ”享{isSpringFestival ? '春节寄件不打烊' : '专属优惠价'}
          <Text className='kb-color__brand kb-margin-md-l'>去开通&gt;</Text>
        </View>
      )}
    </View>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
