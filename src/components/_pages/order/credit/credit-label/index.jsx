

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import './index.scss';

function Index(props) {
  const { msg } = props || {};

  return (
    <Fragment>
      <View className='kb-credit-label'>
        <AtIcon
          prefixClass='kb-icon'
          value='wxpayflag'
          className='kb-icon-size__base kb-color__green'
        />
        <Text className='kb-icon__text--ml'>{msg || '微信支付分|450分及以上信用优享'}</Text>
      </View>
    </Fragment>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
