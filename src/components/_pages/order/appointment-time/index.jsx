/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbPicker from '@base/components/picker';
import request from '@base/utils/request';
import { noop, debounce } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
import isFunction from 'lodash/isFunction';
import { getCurQuotationItem } from '@/components/_pages/order/_utils/order.edit';
import { getFirstTime } from './_utils';
import './index.scss';

function formatNumber(n) {
  //补0
  let normal = n.toString();
  return normal[1] ? normal : '0' + normal;
}

class Index extends Taro.Component {
  static defaultProps = {
    relationInfo: {}, //下单对象
    address: {}, //地址信息
    extraInfo: {}, //额外信息
    current: [],
    columns: [],
    onChange: noop,
    isOpenCredit: false,
  };

  constructor() {
    super(...arguments);
    this.state = {
      cValue: [],
      times: [],
    };
    this.pickerRef = '';
    this.triggerUpdate = debounce(this.triggerUpdate, 300, {
      trailing: true,
    });
  }

  componentWillReceiveProps(nextProps) {
    const { brand } = this.props;
    const { brand: nextBrand } = nextProps;
    if (brand != nextBrand) {
    }
  }

  // 保存picker的ref
  onRef = (ref) => {
    this.pickerRef = ref;
  };

  onShowPicker() {
    this.initTimeData((times) => {
      if (times.length > 0) {
        this.pickerRef.show();
      } else {
        Taro.kbToast({
          text: '暂无可选期望上门时间',
        });
      }
    });
  }

  onConfirm = (e) => {
    const { result, value, obj } = e;
    let arr = [];
    if (isArray(value)) {
      arr = value.length > 1 ? [...value, result] : [...value, '', result];
    }
    if (isString(value) || !value) {
      arr = [obj.value, '', obj.label];
    }
    this.setTimes(arr);
  };

  initTimeData(then, { toastError = true, fData = {} } = {}) {
    const oData = { ...this.props, ...(fData || {}) };
    // console.log('预约组件==>原始数据', oData, this.props, fData);
    const { brand, quotationList, formatRequest } = oData;
    const { order_type } = getCurQuotationItem(brand, quotationList) || {};
    let params = {};
    if (formatRequest && isFunction(formatRequest)) {
      params = formatRequest(oData) || {};
    }
    const reqData = {
      brand,
      order_type,
      ...params,
    };
    const { delivery_type } = this.props.brandInfo || {};
    if (delivery_type) {
      reqData.delivery_type = delivery_type;
    }
    // console.log('预约组件==>请求数据', reqData);
    Promise.resolve(this.queryTime({ reqData, toastError })).then(then);
  }

  setTimes([oDate, oClock, result]) {
    let reserve_start_time, reserve_end_time, expectedTime;
    if (oDate.endsWith('h')) {
      reserve_start_time = reserve_end_time = oDate.substring(-1);
      expectedTime = result;
    } else if (!oClock || oClock == '-') {
      const firstTime = getFirstTime(this.state.times);
      reserve_start_time = reserve_end_time = '1h';
      expectedTime = (firstTime && firstTime.label) || '立即取件';
    } else {
      let [oClock0, oClock1] = oClock.split('-');
      reserve_start_time = `${oDate} ${oClock0}`;
      reserve_end_time = `${oDate} ${oClock1}`;
      expectedTime = result;
    }
    this.triggerUpdate({
      reserve_start_time,
      reserve_end_time,
      expectedTime,
    });
  }

  triggerUpdate({ reserve_start_time, reserve_end_time, expectedTime }) {
    this.props.onChange({
      reserve_start_time,
      reserve_end_time,
      expectedTime,
    });
  }

  queryTime({ url = '/api/PickupTime/getPickupTime', reqData = {}, toastError = true }) {
    return new Promise((resolve, reject) => {
      request({
        url,
        data: reqData,
        formatResponse: (res) => {
          if (
            res.code == 0 &&
            res.data &&
            res.data.pickupSliceTimeDTOList &&
            res.data.pickupSliceTimeDTOList.length > 0
          ) {
            let arr = [];
            res.data.pickupSliceTimeDTOList.map((item) => {
              let linkedValueArr = [];
              if (item.pickupSliceTimeList && item.pickupSliceTimeList.length > 0) {
                item.pickupSliceTimeList.map((iitem) => {
                  iitem.startTime = iitem.startTime.substr(0, 5);
                  iitem.endTime = iitem.endTime.substr(0, 5);
                  linkedValueArr.push({
                    label: iitem.name,
                    value: `${iitem.startTime}-${iitem.endTime}`,
                  });
                });
              }
              arr.push({
                label: this.formatDate(item.dateKey, true)[0],
                value: item.dateKey,
                children: linkedValueArr,
              });
            });
            res.times = arr;
          }
          return res;
        },
        onThen: ({ code, times }) => {
          if (code == 0 && times) {
            this.setState(
              {
                times,
              },
              () => {
                resolve(times);
              },
            );
          } else {
            if (toastError) {
              Taro.kbToast({
                text: '暂无可选期望上门时间',
              });
            }
            reject();
          }
        },
      });
    });
  }

  getConfirmTime() {
    return new Promise((resolve) => {
      const { columns } = this.props;
      this.setState(
        {
          times: columns,
        },
        () => {
          resolve(columns);
        },
      );
    });
  }

  isFullData(data = {}) {
    for (let key in data) {
      if (!data[key]) {
        return false;
      }
    }
    return true;
  }

  formatDate(date, isspace) {
    var nowDate = new Date(),
      nTs,
      fTs,
      aday = 24 * 3600 * 1000,
      reg = /\-/g,
      fDateArr;
    let cur = date ? date : nowDate.getTime();
    cur = new Date(cur);
    var year = cur.getFullYear(),
      month = cur.getMonth() + 1,
      day = cur.getDate(),
      hour = cur.getHours(),
      minute = cur.getMinutes(),
      fDate = [year, month, day].map(formatNumber).join('-'),
      fTime = [hour, minute].map(formatNumber).join(':'),
      ny = nowDate.getFullYear(),
      nm = nowDate.getMonth() + 1,
      nd = nowDate.getDate(),
      nDate = [ny, nm, nd].map(formatNumber).join('-');
    fTs = Date.parse(fDate.replace(reg, '/'));
    nTs = Date.parse(nDate.replace(reg, '/'));
    if (isspace == 'ts') {
      return cur ? fTs : nTs;
    }
    if (fTs == nTs) {
      fDate = '今天';
    } else if (nTs - fTs >= aday && nTs - fTs < 2 * aday) {
      fDate = '昨天';
    } else if (fTs - nTs > 0 && fTs - nTs <= aday) {
      fDate = '明天';
    } else if (fTs - nTs > aday && fTs - nTs <= 2 * aday) {
      fDate = '后天';
    } else {
      fDateArr = ny == year ? [month, day] : [year, month, day];
      fDate = fDateArr.map(formatNumber).join('-');
    }
    return isspace ? [fDate, fTime] : fDate + ' ' + fTime;
  }

  render() {
    const { times, cValue } = this.state;
    const isSingleCol = times && times[0] && !times[0].children;
    return (
      <View>
        <KbPicker
          mode={isSingleCol ? 'selector' : 'linkage'}
          options={times}
          value={cValue}
          level={isSingleCol ? 1 : 2}
          onConfirm={this.onConfirm}
          onRef={this.onRef}
        />
      </View>
    );
  }
}

export default Index;
