/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const defaultTime = {
  label: '立即取件',
  value: '1h',
  start: '1h',
  end: '1h',
}

export const getFirstTime = (times) => {
  if (times && times.length > 0) {
    const item = times[0];
    const item2 = item.children && item.children[0] || {}
    const [start, end] = item2 && item2.value ? item2.value.split('-') : [];
    if (!start || start.includes('h')) {
      return {
        label: item2.label,
        start,
        end,
      }
    }
    return {
      label: `${item.label}${item2.label}`,
      start: `${item.value} ${start}`,
      end: `${item.value} ${end}`,
    }
  }
}
