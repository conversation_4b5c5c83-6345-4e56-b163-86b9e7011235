

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.coupon {
  background: $color-white;
  border-radius: $border-radius-md;
  &-head {
    width: 100%;
    height: 20px;
    background: linear-gradient(to right, #ff5567, #ffe284);
    border-radius: 10px 10px 0 0;
  }
  &-body {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-lg;
    &::before,
    &::after {
      position: absolute;
      top: 50%;
      display: block;
      width: 30px;
      height: 30px;
      background-color: #f2f2f2;
      border-radius: 50%;
      content: '';
    }
    &::before {
      left: -15px;
    }
    &::after {
      right: -15px;
    }
    &-price {
      color: #ff5567;
      &_unit {
        font-size: $font-size-base;
      }
      &_num {
        font-size: 42px;
      }
    }
    &-explain {
      flex: 1;
      padding-left: $spacing-h-md;
      color: $color-grey-1;
      font-size: $font-size-base;
    }
  }
}
