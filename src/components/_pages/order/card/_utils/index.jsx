/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const apply_brand_type_map = {
  brand: '快递公司',
  big_package: '经济货运',
};

export const apply_user_map = {
  user: '团员',
  regiment: '团长',
  league: '加盟商',
};

export const createApplyUserMsg = (apply_user) => {
  if (apply_user) {
    return apply_user
      .split(',')
      .map((item) => {
        return apply_user_map[item];
      })
      .join('、');
  }
  return '';
};
