/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text, Image } from '@tarojs/components';
import classNames from 'classnames';
import { openCreditService } from '../../_utils/order.credit-pay';
import { apply_brand_type_map, createApplyUserMsg } from '../_utils';
import './index.scss';

const KbCouponCardListItem = (props) => {
  const {
    item,
    onExpand = () => {},
    onUse = () => {},
    disabled = false,
    isOpenCredit = false,
  } = props;
  const { expanded, coupon_info = {} } = item || {};
  const { coupon_name, rate, rate_type, min_freight, pay_method, apply_user, apply_brand_type } =
    coupon_info || {};
  const applyUserMsg = createApplyUserMsg(apply_user);
  const apply_brand_type_msg = apply_brand_type_map[apply_brand_type] || '';
  const isNeedCredit = pay_method == 2;
  const wxCredit_disabled = !isOpenCredit && isNeedCredit;

  const itemCls = classNames('kb-coupon-item', {
    'kb-coupon-item--disabled': disabled,
    'kb-coupon-item--disabled-wxCredit': wxCredit_disabled,
  });
  const detailIconCls = classNames('detail-icon', {
    'detail-icon2': expanded,
  });
  return (
    <View className={itemCls}>
      {wxCredit_disabled && (
        <View className='wxCredit_row' onClick={openCreditService}>
          <View>不可用原因：开通微信支付分后，可享用抵扣券</View>
          <View className='wxCredit_row_action'>
            去开通
            <Text className='kb-icon kb-icon-arrow kb-color__white kb-icon-size__sm' />
          </View>
        </View>
      )}
      <View className='kb-coupon-item--content'>
        {item.coupon_num > 0 ? (
          <View className='kb-coupon-item--num'>{item.coupon_num}张</View>
        ) : null}
        <View className='kb-coupon-item--price'>
          <View className='kb-coupon-item--priceBox'>
            <Text className='val'>{rate}</Text>
            <Text className='unit'>{rate_type === 'discount' ? '折' : '元'}</Text>
          </View>
          {rate_type != 'discount' && min_freight > 0 ? (
            <View className='kb-coupon-item--minPrice'>
              (满{min_freight}元减{rate}元)
            </View>
          ) : null}
        </View>
        <View className='kb-coupon-item--hat' />
        <View className='kb-coupon-item--body'>
          <View className='kb-coupon-item--main'>
            <View className='title'>{coupon_name ? coupon_name : ''}</View>
            <View className='date'>有效期至 {item.expiration_time}</View>
            {isNeedCredit ? <View className='credit'>微信支付分|寄件可用</View> : null}
            <View className='detail' onClick={() => onExpand(item)} hoverClass='kb-hover-opacity'>
              使用详情
              <View className={detailIconCls}>
                <Text className='kb-icon kb-icon-arrow' />
              </View>
            </View>
          </View>
          <View className='kb-coupon-item--option'>
            {disabled ? (
              <View className='icon_overdue'>
                <Image
                  className='img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/qj/miniapp/custom-coupon/icon_overdue.png?v=01'
                />
              </View>
            ) : (
              <View className='btn' onClick={() => onUse(item)} hoverClass='kb-hover-opacity'>
                立即使用
              </View>
            )}
          </View>
        </View>
      </View>
      {expanded && (
        <View className='kb-coupon-item--extra'>
          {apply_brand_type_msg && applyUserMsg ? (
            <View className='kb-coupon-item--source'>
              <View>限指定团队:</View>
              <View>
                {' '}
                &quot;{apply_brand_type_msg}&quot; {applyUserMsg}身份寄件使用
              </View>
            </View>
          ) : null}
          {/* {item.coupon_info.note ? (
            <View className='kb-coupon-item--source'>
              <View>优惠券来源: </View>
              <View className='kb-color__red'>{item.coupon_info.note}</View>
            </View>
          ) : null} */}
          {item.coupon_info.desc.length ? (
            <View className='kb-coupon-item--desc'>
              <View>更多说明</View>
              {item.coupon_info.desc.map((descItem) => (
                <View key={descItem}>{descItem}</View>
              ))}
            </View>
          ) : null}
        </View>
      )}
    </View>
  );
};

KbCouponCardListItem.defaultProps = {};

KbCouponCardListItem.options = {
  addGlobalClass: true,
};

export default KbCouponCardListItem;
