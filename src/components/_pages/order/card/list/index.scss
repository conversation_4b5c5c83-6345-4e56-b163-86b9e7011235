/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-coupon {
  $color-disabled: #999;
  &-list {
    padding-bottom: 20px;
  }
  &-item {
    margin: $spacing-v-lg;
    &--disabled {
      filter: grayscale(100%);
      &-wxCredit {
        padding: 0 $spacing-h-sm $spacing-h-sm;
        background: #f8a3a3;
        border-radius: 16px;
        .wxCredit {
          &_row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: $spacing-h-md 0;
            color: $color-white;
            font-size: $font-size-base;
            &_action {
              display: flex;
              align-items: center;
            }
          }
        }
        .kb-coupon-item {
          &--num {
            background: $color-disabled !important;
          }
          &--priceBox,
          &--minPrice,
          &--main,
          &--main .detail {
            color: $color-disabled !important;
          }
          &--option {
            .btn {
              color: $color-disabled !important;
              border-color: $color-disabled !important;
            }
          }
          &--hat {
            display: none;
          }
        }
      }
    }
    &--content {
      position: relative;
      display: flex;
      align-items: center;
      min-height: 160rpx;
      background: #ffffff;
      border-radius: 16px;
    }
    &--num {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 68px;
      height: 34px;
      color: #ffffff;
      font-weight: bold;
      font-size: 22px;
      line-height: 1;
      background: $color-red;
      border-radius: 10px 0px 10px 0px;
    }
    &--price {
      position: relative;
      width: 148px;
      padding-right: 10px;
      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        height: 128px;
        border-right: $width-base dashed #dcdee0;
        transform: translateY(-50%);
        content: '';
      }
    }
    &--priceBox {
      position: relative;
      display: flex;
      align-items: baseline;
      justify-content: center;
      color: $color-red;
      font-size: 32px;
      .val {
        font-weight: bold;
        font-size: 60px;
      }
      .unit {
        margin-left: 5px;
        font-size: 22px;
      }
    }
    &--minPrice {
      color: $color-red;
      font-size: 20px;
      text-align: center;
    }
    &--hat {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 158px;
      width: $width-base;
      &::before,
      &::after {
        position: absolute;
        width: 20px;
        height: 20px;
        background: #f7f8fa;
        border-radius: 50%;
        content: '';
      }
      &::before {
        top: 0;
        transform: translate(-50%, -50%);
      }
      &::after {
        bottom: 0;
        transform: translate(-50%, 50%);
      }
    }
    &--body {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding: 0 24px;
    }
    &--main {
      flex: 1;
      padding: 20px 0;
      .title {
        font-weight: bold;
        font-size: 28px;
        line-height: 40px;
      }
      .date {
        margin-top: 5px;
        font-size: 22px;
        line-height: 34px;
      }
      .credit {
        margin-top: 5px;
        font-size: 22px;
        line-height: 34px;
      }
      .detail {
        display: flex;
        align-items: center;
        margin-top: 10px;
        color: #969799;
        font-size: 22px;
        &-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 26px;
          height: 26px;
          margin-left: 5px;
          color: #646566;
          background: #f0f2f6;
          border-radius: 50%;
          .kb-icon {
            font-size: 14px;
            transform: rotate(90deg);
          }
          &2 {
            .kb-icon {
              transform: rotate(-90deg);
            }
          }
        }
      }
    }
    &--option {
      text-align: center;
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 136px;
        height: 42px;
        color: $color-red;
        font-weight: bold;
        font-size: 24px;
        border: $width-base solid $color-red;
        border-radius: 42px;
      }
      .num {
        margin-top: 10px;
        color: #646566;
        font-size: 20px;
      }
      .icon_overdue {
        width: 106px;
        height: 106px;
        .img {
          width: 100%;
          height: 100%;
        }
      }
    }
    &--extra {
      margin-top: $spacing-v-md;
      padding: 20px 20px 0;
      background: #ffffff;
      border-radius: 0px 0px 16px 16px;
    }
    &--source {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44px;
      margin-bottom: 10px;
      padding: 0 16px;
      color: #646566;
      font-size: 22px;
      background: rgba(227, 76, 88, 0.05);
      border-radius: 4px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    &--desc {
      padding: 24px 0;
      color: #969799;
      font-size: 22px;
      line-height: 36px;
    }
    .kb-color__red {
      color: #e34c58;
    }
  }
}
