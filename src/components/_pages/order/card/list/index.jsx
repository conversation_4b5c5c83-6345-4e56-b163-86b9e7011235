/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import KbLongList from '@base/components/long-list';
import dayjs from 'dayjs';
import KbCouponCardListItem from './item';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class KbCouponCardList extends Component {
  static options = {
    addGlobalClass: true,
  };

  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
    this.listData = {
      pageKey: 'page_num',
      api: {
        url: '/api/Coupon/getAllReceiveCoupon',
        data: {
          page_size: 30,
          pageSize: 30,
        },
        formatResponse: ({ data }) => {
          const list = Array.isArray(data)
            ? data.filter((item) => {
                const { type } = this.props;
                const isExpired = dayjs().isAfter(dayjs(item.expiration_time));
                return type === 'invalid' ? isExpired : !isExpired;
              })
            : [];
          if (list.length) {
            return {
              code: 0,
              data: {
                list,
              },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  handleExpand(item) {
    const _list = [...this.state.list];
    const index = _list.findIndex((val) => val.id === item.id);
    _list[index].expanded = !_list[index].expanded;
    this.setState({
      list: _list,
    });
  }

  handleUse(item) {
    console.log(item);
    Taro.navigator({
      url: 'order/edit',
      options: {
        orderType: item.coupon_info.apply_brand_type === 'big_package' ? 'big_package' : 'edit',
      },
    });
  }

  render() {
    const { list = [] } = this.state;
    const { active, type, isOpenCredit } = this.props;

    const disabled = type === 'invalid';
    return (
      <KbLongList data={this.listData} active={active}>
        <View className='kb-coupon-list'>
          {list.length > 0 &&
            list.map((item) => {
              return (
                <KbCouponCardListItem
                  key={item}
                  item={item}
                  disabled={disabled}
                  isOpenCredit={isOpenCredit}
                  onExpand={this.handleExpand.bind(this)}
                  onUse={this.handleUse.bind(this)}
                />
              );
            })}
        </View>
      </KbLongList>
    );
  }
}

export default KbCouponCardList;
