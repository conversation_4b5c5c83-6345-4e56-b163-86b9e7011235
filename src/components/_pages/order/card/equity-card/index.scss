

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.card {
  padding: $spacing-h-md;
  color: $color-white;
  background: #70c6db;
  border-radius: $border-radius-md;
  &-hd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: $font-size-lg;
      &_name {
        max-width: 420px;
        margin-right: $spacing-h-sm;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &_price {
      font-size: 0;
      &_danwei {
        margin-top: 5px;
        font-size: $font-size-base;
      }
      &_num {
        font-size: 40px;
      }
    }
  }
  &-bd {
    font-size: $font-size-base;
    &-container {
      display: flex;
      justify-content: space-between;
      padding-top: $spacing-h-md;
      border-top: 1px dashed #fff;
    }
  }
  &-disabled {
    background: $color-grey-4;
  }
}
