/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef, useState } from '@tarojs/taro';
import isObject from 'lodash/isObject';
import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import isEqual from 'lodash/isEqual';
import isArray from 'lodash/isArray';
import { formatAddress, getQuotation, transferAddress } from '../_utils/qj.index';
import { checkDataComplete } from '../../address/_utils';

export const servicesKeyMap = {
  isDelivery: 'collection',
  isDesPay: 'arrive_pay',
  isDecVal: 'keep_account',
  isProPrice: 'pro_price',
};
export const serviceMap = {
  cost_value: {
    key: 'cost_value',
    title: '保价费用',
  },
  keep_account: {
    key: 'keep_account',
    title: '声明价值',
    confirmText: '声明价值',
    inputConfig: {
      label: '声明物品价值',
    },
    switch: {
      label: '保价费率',
      key: 'rate_checked',
    },
    required: true,
  },
  pro_price: {
    key: 'pro_price',
    title: '保价服务',
    confirmText: '保价费',
    inputConfig: {
      label: '声明物品价值',
    },
    switch: {
      label: '保价费率',
      key: 'rate_checked',
    },
    required: false,
    customKey: 'keep_account',
  },
  collection: {
    key: 'collection',
    title: '代收货款',
    confirmText: '代收金额',
    inputConfig: {
      label: '代收货款',
    },
    required: false,
  },
  arrive_pay: {
    key: 'arrive_pay',
    title: '到付运费',
    confirmText: '到付金额',
    inputConfig: {
      label: '到付运费',
    },
    switch: {
      cInput: true,
      label: '是否到付金额',
      key: 'is_arrive_pay',
    },
    required: false,
  },
};
export const getServiceList = (keys = []) => {
  return Object.keys(serviceMap)
    .filter((key) => keys.includes(key))
    .map((i) => serviceMap[i]);
};
export const getServiceListActivity = (data) => {
  const otherConfig = {
    is_collection: {
      title: '代收金额',
    },
    is_arrive_pay: {
      title: '到收货款',
    },
  };
  const activityService = {
    ...serviceMap,
    ...otherConfig,
  };
  const valid = Object.keys(data).filter((key) => {
    return (
      (otherConfig[key] ? !data[key.replace('is_', '')] && !!data[key] : data[key]) &&
      data[key] !== 0 &&
      activityService[key]
    );
  });

  return valid.map((key) => {
    return {
      ...activityService[key],
      value: otherConfig[key] ? '' : data[key],
    };
  });
};

export const DHServiceTipsMap = {
  bt: [
    {
      label: '保价费计算规则',
      list: [
        '1、保费=声明价值*费率，最低0元/票，费率1‰',
        '2、下单默认基础保费：0元/票； 用户下单购买报价，大于1000元时保价费用为物品价值的1‰ 费率收取。 ',
        '3、声明价值超过30000不予保价!',
      ],
    },
    {
      label: '以下物品不提供保价服务',
      list: [
        '1、精密设备或仪器（精密设备的判断条件是：',
        'A.对运输有特殊的防震动、防倾斜、防尘要求；',
        'B.受轻微碰擦即无法使用的机器设备配件；',
        'C.受国内维修能力所限，设备受损后无法在国内修复；',
        'D.单件货物价值超过人民币200万元；满足上述中任意一条的标的即定义为精密设备或仪器）；',
        '2、散装货物、无包装货物，二手货物；农副产品、军用物品',
        '3、动植物、鲜活物、冷藏货物；',
        '4、各种压缩气体、易燃易爆物品（易燃、易爆的危险品详见《危险货物品名表》GB12268中列明的货物。）；',
        '5、易损品：包括但不限于平板玻璃、纯玻璃制品、仪表、亚克力板（有机玻璃）、含玻璃的家具（玻璃面积超过家具最大面面积30 %）、大理石类物品、陶瓷制品、亚克力卫浴制品、显示器等，如需承保上述部分货品，需与保险人进行提前报备并且得到保险人允许；',
        '6、字画、工艺品类（如：各种铁艺物品、铜艺物品、花艺物品、骨雕物品、木雕物品、石雕物品、竹雕物品、炭雕物品、牙雕物品、膏雕物品、树脂类工艺品等）',
        '7、艺术品、金银、珠宝、钻石、文物古玩、文玩核桃、玉器、玉雕工艺品及其它玉石制品；',
        '8、卷烟、烟草（包括叶、梗、末、杆）、烟草制品；',
        '9、大型港口机械设备、大型铸件、风力发电相关设备及配件、太阳能相关设备及配件（如铝片、电池板等）；汽车整车；',
        '10、钢琴及其他乐器、红木家具（用紫檀木，花梨木、香枝木、黑酸枝、红酸枝、乌木、条纹乌木、鸡翅木制成的家具），如需承保上述部分货品，需与保险人进行提前报备并且得到保险人允许。',
        '11、武器弹药、现金、支票、票据、单证、有价证券、信用证、 护照；',
        '12、任何违反国家法律法规的货物；',
        '13、由托运人故意或过失导致的损失。',
      ],
    },
  ],
  sxjd: [
    {
      label: '保价费计算规则',
      list: [
        '1、保费=声明价值*费率，最低3元/票，费率3‰',
        '2、下单默认基础保费：3元/票； 用户下单购买报价，大于1500元时保价费用为物品价值的3‰ 费率收取。',
        '3、声明价值超过30000不予保价!',
        '指运输企业与托运人共同确定的以托运人声明货物价值为基础的一种特殊运输方式，保价就是托运人向承运人声明其托运货物的实际价值（声明的货物价值不得超过货物本身实际价值），按保价运输的货物，托运人除缴纳运输费用外，按照规定缴纳一定的保价费用。',
      ],
    },
  ],
  'sxjd-jl': [
    {
      label: '保价费计算规则',
      list: [
        '1、普通物品： ',
        '0-40kg：最低一票3元，费率0.002元',
        '40-70kg：最低一票4元，费率0.002元 ',
        '70-130kg：最低一票5元，费率0.002元 ',
        '130-500kg：最低一票6元，费率0.002元 ',
        '500-1000kg：最低一票8元，费率0.002元 ',
        '1000-3000kg：最低一票12元，费率0.002元 ',
        '3000-5000kg：最低一票16元，费率0.002元 ',
        '5000-10000kg：最低一票20元，费率0.002元 ',
        '10000kg以上：最低一票28元，费率0.002元 .',
        '2、易碎物品： ',
        '0-40kg：最低一票8元，费率0.006元 ',
        '40-70kg：最低一票8元，费率0.006元 ',
        '70-130kg：最低一票8元，费率0.006元 ',
        '130-500kg：最低一票12元，费率0.006元 ',
        '500-1000kg：最低一票16元，费率0.006元 ',
        '1000-3000kg：最低一票22元，费率0.006元 ',
        '3000-5000kg：最低一票30元，费率0.006元 ',
        '5000-10000kg：最低一票38元，费率0.006元 ',
        '10000kg以上：最低一票50元，费率0.006元 ',
        '指运输企业与托运人共同确定的以托运人声明货物价值为基 础的一种特殊运输方式，保价就是托运人向承运人声明其托 运货物的实际价值（声明的货物价值不得超过货物本身实际 价值），按保价运输的货物，托运人除缴纳运输费用外，按 照规定缴纳一定的报价费用。',
      ],
    },
  ],
  htky: [
    {
      label: '保价费计算规则',
      list: [
        '1、保价金额2000以内免保价费，超过2000的按0.2%费率计保价费，上限3万。',
        '2、物品价值超3万，按3万保价',
        '3、若未保价，货运赔付不超过2000元，快递赔付不超过500元。',
      ],
    },
  ],
  ky: [
    {
      label: '保价费计算规则',
      list: [
        '1、保费 = 声明价值 * 费率 （下单默认基础保费：2元/票，保价500元；）',
        '2.用户自主单购买保价：小于等于500元，保价2元。大于500元时保价费用为物品价值的4‰ 费率收取。',
        '3、声明价值超过30000不予保价!',
      ],
    },
    {
      label: '以下物品不提供保价服务',
      list: [
        '1、价值不易核实的物品，如古玩字画、纪念币、翡翠原石、观赏石等;',
        '2、易损或不易包装的物品，如玉雕、工艺品等',
        '3、其他运输风险较大的物品，以快递员与您核实确认的信息为准。',
      ],
    },
    {
      label: '理赔规则',
      list: [
        '1、已保价的货物，由快递公司原因造成货物丢失或全部损毁、灭失(包装材料破损除外)的，按照保价的声明价值进行赔付；内件短少或部分损毁的，按照货物的声明价值和损失的比例赔付。',
        '2、未保价的货物，由于快递公司原因造成货物丢失或破损(包装材料破损除外)的，按照货物实际损失赔偿，但每单最高额不超过该单货物运费的5倍。',
        '3、因下列原因造成的托寄物毁损灭失、延误派送、无法派送的，快递公司不承担赔偿责任:',
        '1）因不可抗力因素造成的。不可抗力是指无法预测、无法控制或无法避免的客观因素或意外事件，包括但不限于地震、火灾、雪灾、暴风雨、大雾等恶劣天气：罢工、恐怖事件、意外交通事故、法规政策的修改、政府、司法机关的行为、决定或命令, 抢劫、飞车抢劫等暴力犯罪。',
        '2）因托寄物的自然性质、内在缺陷或合理损耗造成的。',
        '3）非快递公司过错造成的，包括但不限于您没有如实申报托寄物资料，您交付的托寄物属于法律法规明文定及本合同约定禁止运输或限制运输的，收货人迟延受领托寄物等情形。',
        '4、如果您就托寄物购买了保险，因发生保险事故导致托寄物毁损灭失的，保险公司已经向您承担或许诺承担保险理赔责任后，在此范围内不再承担赔偿责任。',
      ],
    },
  ],
};

export const serviceTips = (brand) => {
  if (DHServiceTipsMap[brand]) {
    return DHServiceTipsMap[brand];
  }
  if (brand == 'cngg') {
    return [
      {
        label:
          '货物破损：未按照约定为下单用户的寄递物品提供存储、保管的服务，从而导致用户利益受损，包括但不限于因包裹破损导致货品破损、物流包装破损',
        list: [
          '1、如能提供有效货值证明的，按有效货值的20%赔偿，最高不超过300元；无法提供有效货值证明的，按10倍运费赔付，最高不超过300元；',
          '2、如货品外包装破损且货品本身破损，同时影响商品主要功能的，按照货品丢失条款进行赔偿。',
        ],
      },
      {
        label:
          '货物丢失：未按服务承诺保管包裹，使包裹出现丢失的情况，包括但不限于因包裹破损导致货品少件、包裹整体丢失。',
        list: [
          '1、如能提供有效货值证明的，按有效货值赔付，最高不超过600元；无法提供有效货值证明的，按10倍运费赔付，最高不超过600元；',
          '2、货品少件的物品损失，智能综合运力或智能综合运力指定的物流服务商按最多10倍物流服务费赔偿，最高不超过600元。',
        ],
      },
    ];
  }
  if (brand === 'zt') {
    return [
      {
        label: '保价费用计算规则:',
        list: [
          '物品价值在0~1000元时，保价费用为1元，物品价值在1000~2000 元时，保价费用为2元，大于2000元时保价费用为物品价值的3‰， 单票最高保价金额为3万元。如产品选择不保价，快件发生丢失破损 少件，将按照订单运费进行赔付，单票赔付最高金额为1000元。',
        ],
      },
      {
        label: '理赔标准',
        list: [
          '保价快件，按照实际价值损失赔偿，但最高不超过申报价值（投保 金额）；请按照货物价值足额投保，投保上限单票为3万元。 ',
          '（1）超额投保：投保金额＞货物价值，超额部分不予赔付。理赔金 额=定损金额≤货物价值 ',
          '（2）不足额投保：投保金额＜货物价值，理赔金额=定损金额*投保 比例=定损金额*（投保金额/货物价值）',
        ],
      },
      {
        label: '以下物品不提供保价服务:',
        list: [
          '1、价值不易核实的物品，如古玩字画、纪念币、翡翠原石、观赏石等;',
          '2、易损或不易包装的物品，如玉雕、工艺品等',
          '3、其他运输风险较大的物品，以快递员与您核实确认的信息为准。',
          {
            value: '《中通禁保物品类目》>>',
            navigator: () => {
              Taro.navigator({
                url: 'https://upload.kuaidihelp.com/admin/floorPageFile/1744355024.html',
                target: 'webview',
              });
            },
            isLink: true,
          },
        ],
      },
      {
        label: '理赔规则： ',
        list: [
          `1、已保价的货物，由快递公司原因造成货物丢失或全部损毁、灭失(包装材料破损除外)的，按照保价的声明价值进行赔付;内件短少或部分损毁的，按照货物的声明价值和损失的比例赔付。`,
          '2、未保价的货物，由于快递公司原因造成货物丢失或破损(包装材料破损除外)的，按照货物实际损失赔偿，但每单最高额不超过该单货物运费的5倍。',
          '3、因下列原因造成的托寄物毁损灭失、延误派送、无法派送的，快递公司不承担赔偿责任:',
          '（1）因不可抗力因素造成的。不可抗力是指无法预测、无法控制或无法避免的客观因素或意外事件，包括但不限于地震、火灾、雪灾、暴风雨、大雾等恶劣天气;罢工、恐怖事件、意外交通事故、法规政策的修改、政府、司法机关的行为、决定或命令;抢劫、飞车抢劫等暴力犯罪。',
          '（2）因托寄物的自然性质、内在缺陷或合理损耗造成的。',
          '（3）非快递公司过错造成的，包括但不限于您没有如实申报托寄物资料，您交付的托寄物属于法律法规明文定及本合同约定禁止运输或限制运输的，收货人迟延受领托寄物等情形。',
          '（4）如果您就托寄物购买了保险，因发生保险事故导致托寄物毁损灭失的，保险公司已经向您承担或许诺承担保险理赔责任后，在此范围内不再承担赔偿责任。',
        ],
      },
    ];
  }
  return [
    {
      label: '保价费用计算规则:',
      list: [
        brand == 'dp'
          ? 'dp_table'
          : brand == 'ems'
          ? '国内特快专递邮件声明价值在300元（含）以内的，保价费按1元收取；声明价值超过300元的部分，按照0.5%费率收取保价费（保价费最低收取1元人民币）。'
          : brand == 'jt'
          ? '保价金额3000以内（含）免保价费，超过3000的按0.6%费率计保价费，上限3万，超过3万按3万保价；'
          : '物品价值在0-500元的，保价费用为1元，501-1000元，保价费用为2元，大于1000元时保价费用为物品价值的5‰，保价费用四向上取整精确到元，最低1元。声明价值超过20000不予保价!',
      ],
    },
    {
      label: '以下物品不提供保价服务:',
      list: [
        '1、价值不易核实的物品，如古玩字画、纪念币、翡翠原石、观赏石等;',
        '2、易损或不易包装的物品，如玉雕、工艺品等',
        '3、其他运输风险较大的物品，以快递员与您核实确认的信息为准。',
      ],
    },
    {
      label: '理赔规则： ',
      list: [
        `1、已保价的货物，由快递公司原因造成货物丢失或全部损毁、灭失(包装材料破损除外)的，按照保价的声明价值进行赔付;内件短少或部分损毁的，按照货物的声明价值和损失的比例赔付。`,
        '2、未保价的货物，由于快递公司原因造成货物丢失或破损(包装材料破损除外)的，按照货物实际损失赔偿，但每单最高额不超过该单货物运费的5倍。',
        '3、因下列原因造成的托寄物毁损灭失、延误派送、无法派送的，快递公司不承担赔偿责任:',
        '（1）因不可抗力因素造成的。不可抗力是指无法预测、无法控制或无法避免的客观因素或意外事件，包括但不限于地震、火灾、雪灾、暴风雨、大雾等恶劣天气;罢工、恐怖事件、意外交通事故、法规政策的修改、政府、司法机关的行为、决定或命令;抢劫、飞车抢劫等暴力犯罪。',
        '（2）因托寄物的自然性质、内在缺陷或合理损耗造成的。',
        '（3）非快递公司过错造成的，包括但不限于您没有如实申报托寄物资料，您交付的托寄物属于法律法规明文定及本合同约定禁止运输或限制运输的，收货人迟延受领托寄物等情形。',
        '（4）如果您就托寄物购买了保险，因发生保险事故导致托寄物毁损灭失的，保险公司已经向您承担或许诺承担保险理赔责任后，在此范围内不再承担赔偿责任。',
      ],
    },
  ];
};

export const getFeeMax = (brand) => {
  return brand === 'zt' || brand === 'jt' ? 30000 : 20000;
};

export const remarkMaps = [
  { label: '带文件夹', key: '1' },
  { label: '带防水袋', key: '2' },
  { label: '带纸箱', key: '3' },
  { label: '要爬楼', key: '4' },
  { label: '上门请联系', key: '5' },
];
// 驿站增值服务配置
export const servicesConfig = {
  courierId: null,
  cost: null, // 保价价费率
  proPriceStart: null, // 保价起点
  proPriceEnd: null, // 保价上限
  isDelivery: null, // 是否开启代收货款0.关闭，1.开启
  isDesPay: null, // 是否开启到付，0.关闭1.开启
  isDecVal: null, // 是否声明价值：0.关闭，1.开启
  isProPrice: null, // 是否开启保价：0.关闭,1.开启
  isToDoor: null, // 是否支持上门取件0.关闭,1.开启
  allowReserveTime: '', //是否支持上门取件预约时间
  isFresh: null, // 是否支持生鲜：0.关闭,1.开启,
  isDeclared: null, //是否开启自定义的声明
  reserve_start_time: '', //营业开始时间
  reserve_end_time: '', //营业结束时间
};

// 是否触发表单锁定为自定义内容
export const isFormLocked = (dynamicForms, update) => {
  let updateData = {};
  if (!dynamicForms) return;
  Object.keys(dynamicForms).forEach((i) => {
    if (dynamicForms[i].value) {
      updateData[i] = dynamicForms[i].value;
    }
  });
  if (Object.keys(updateData).length) {
    update({ ...updateData });
    return updateData;
  }
  return false;
};

export const calculate = (num, cost) => parseFloat(((num * 1 * (cost * 1)) / 100).toFixed(2));
// 获取自定义表单配置中的有效值
export const getValidData = (formData) => {
  const data = {};
  formData &&
    Object.keys(formData).forEach((key) => {
      const value = formData[key].value;
      if (value) {
        data[key] = value;
      }
    });
  return data;
};
const verifyTipMaps = {
  'collection.required': '代收货款为必填项',
  'arrive_pay.required': '到付金额为必填项',
};
const verifyKeys = ['required'];
export const getDynamicVerifyOpt = (data) => {
  let verify = {};
  // 取出为验证字段的内容，并填充验证未通过时的提示语
  data &&
    Object.keys(data).map((key) => {
      const item = data[key];
      verifyKeys.forEach((verifyKey) => {
        if (item.hasOwnProperty(verifyKey)) {
          !verify[key] && (verify[key] = {});
          verify[key][verifyKey] = item[verifyKey];
          verify[key][`${key}.${verifyKey}`] = verifyTipMaps[`${key}.${verifyKey}`];
        }
      });
    });
  return verify;
};
export const verifyDynamicForm = (data, verifyOpt = {}) => {
  let res = { code: 0, msg: '' };
  verifyOpt &&
    Object.keys(verifyOpt).forEach((key) => {
      if (verifyOpt[key].required && !data[key]) {
        res.code = '1001';
        res.msg = verifyOpt[key][`${key}.required`];
      }
    });

  return res;
};
// 组装form值的展示
const fixFromValueView = (key, value) => {
  let node = {
    type: 'text',
    text: '',
    attrs: {
      style: 'margin-right:3px',
    },
  };
  switch (key) {
    case 'goods_weight':
      node.text = value + 'kg ';
      break;
    case 'cost_value':
      node.text = `保价费${value}元 `;
      break;
    case 'keep_account':
      node.text = `声明价值${value}元 `;
      break;
    case 'collection':
      node.text = `代收货款${value}元 `;
      break;
    case 'arrive_pay':
      node.text = `到付金额${value}元 `;
      break;
    case 'is_arrive_pay':
      node.text = '到付金额';
      break;
    case 'package_images':
      value[0] &&
        (node = {
          type: 'node',
          name: 'img',
          attrs: {
            style: 'width:20px;height:15px;margin-left:3px;',
            src: value[0],
          },
        });
      break;
    default:
      node.text = value + ' ';
      break;
  }

  return node;
};
// 获取值表单值展示
export const getFormValueView = (data) => {
  const showKeys = [
    'goods_weight',
    'goods_name',
    'cost_value',
    'keep_account',
    'collection',
    'arrive_pay',
    'goods_remark',
    'is_arrive_pay',
    'package_images',
  ];
  if (isObject(data)) {
    let keys = Object.keys(data);
    const exclusion = {
      is_arrive_pay: 'arrive_pay',
    };
    const customVerify = (key) => {
      const verify = { package_images: (data) => data && data.length };
      return verify[key] ? verify[key](data[key]) : true;
    };
    const packageImageIndex = keys.findIndex((key) => key === 'package_images');
    if (packageImageIndex) {
      const [packageImages] = keys.splice(packageImageIndex, 1);
      packageImages && keys.unshift(packageImages);
    }

    const children = keys
      .filter((key) => {
        if (!(data[exclusion[key]] && data[key]) && customVerify(key)) {
          return key;
        }
      })
      .filter((key) => showKeys.includes(key) && !!data[key])
      .map((key) => fixFromValueView(key, data[key]));
    return children.length
      ? [
          {
            name: 'div',
            attrs: {
              style: `display: flex;width:100%;
              ${children.length <= 2 ? 'justify-content: center;' : ''}
          align-items: center;overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;`,
            },
            children,
          },
        ]
      : [];
  }
  return [];
};

export const memoObject = (data = {}, change = {}) => {
  let modify = null;
  Object.keys(data).forEach((key) => {
    if (data[key] !== change[key] && key in change) {
      modify = {
        ...modify,
        [key]: change[key],
      };
    }
  });
  return modify;
};

export const brandsMap = {
  sf: {
    brand: 'sf',
    common: '1',
    cp_code: 'SF',
    is_express: '1',
    logo_link: 'http://img.kuaidihelp.com/brand_logo/icon_sf.png',
    name: '顺丰速运',
    pinyin: 'shunfeng',
    short: 'sf',
    short_name: '顺丰',
    tel: '95338',
    url: 'http://www.sf-express.com',
  },
  jd: {
    brand: 'jd',
    common: '1',
    cp_code: null,
    is_express: '1',
    logo_link: 'https://img.kuaidihelp.com/brand_logo/icon_jd.png?time=1572938220',
    name: '京东快递',
    pinyin: 'jingdong',
    short: 'jd',
    short_name: '京东',
    tel: '950616',
    url: 'http://jd-ex.jd.com/#',
  },
  dp: {
    brand: 'dp',
    common: '0',
    cp_code: null,
    is_express: '1',
    logo_link: 'http://img.kuaidihelp.com/brand_logo/icon_dp.png',
    name: '德邦快递',
    pinyin: 'debang',
    short: 'dp',
    short_name: '德邦',
    tel: '95353',
    url: 'https://www.deppon.com',
  },
  yt: {
    brand: 'yt',
    short: 'yt',
    pinyin: 'yuantong',
    name: '圆通速递',
    short_name: '圆通',
    tel: '95554',
    common: '1',
    url: 'http://www.yto.net.cn/',
    logo_link: 'http://img.kuaidihelp.com/brand_logo/icon_yt.png',
    cp_code: null,
    is_express: '1',
  },
  sto: {
    brand: 'sto',
    short: 'sto',
    pinyin: 'shentong',
    name: '申通快递',
    short_name: '申通',
    tel: '95543',
    common: '1',
    url: 'http://www.sto.cn',
    logo_link: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png?t=20230316',
    cp_code: null,
    is_express: '1',
  },
  yd: {
    brand: 'yd',
    short: 'yd',
    pinyin: 'yunda',
    name: '韵达快递',
    short_name: '韵达',
    tel: '95546',
    common: '1',
    url: 'http://www.yundaex.com',
    logo_link: 'http://img.kuaidihelp.com/brand_logo/icon_yd.png',
    cp_code: null,
    is_express: '1',
  },
  jt: {
    brand: 'jt',
    pinyin: 'jitu',
    name: '极兔速递',
    short_name: '极兔',
    tel: '956025',
    common: '0',
    officially_link: '',
    logo_link: 'https://img.kuaidihelp.com/brand_logo/icon_jt.png',
    is_express: '1',
  },
};

/**
 * 品牌分类
 *  */
export const sortBrands = (brands = []) => {
  let sort_arr = [];
  brands.forEach((item) => {
    const { sort_info, brand, disable } = item || {};
    const { key } = sort_info || {};
    if (disable == 0) {
      if (sort_arr.length == 0) {
        sort_arr.push({
          ...sort_info,
          brands: [brand],
          items: [item],
        });
      } else {
        let res = sort_arr.some((value) => {
          if (value.key == key) {
            value.brands.push(brand);
            value.items.push(item);
            return true;
          }
        });
        if (!res) {
          sort_arr.push({
            ...sort_info,
            brands: [brand],
            items: [item],
          });
        }
      }
    }
  });
  return sort_arr;
};

/**
 * 兼容朋友圈分享单页面,品牌列表
 *  */
export const fakeBrand = [
  {
    brand: 'sf',
    hot: 1,
    message: '预约寄件 快速上门',
    type: 'fxj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 1,
    payTypes: [0, 2, 1],
    rate: 8.5,
    rate_type: 'rate',
    sort_info: { key: 1, title: '品质时效', sub_title: '58%的人选择' },
    disable: 0,
  },
  {
    brand: 'jd',
    hot: 1,
    message: '优质时效',
    type: 'fxj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 0,
    payTypes: [0, 2, 1],
    rate: 7.5,
    rate_type: 'rate',
    sort_info: { key: 1, title: '品质时效', sub_title: '58%的人选择' },
    disable: 0,
  },
  {
    brand: 'dp',
    hot: 1,
    message: '大件特惠超值寄',
    type: 'djj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 0,
    payTypes: [0, 2, 1],
    rate: 7.3,
    rate_type: 'rate',
    sort_info: { key: 1, title: '品质时效', sub_title: '58%的人选择' },
    disable: 0,
  },
  {
    brand: 'yd',
    hot: 1,
    message: '极速取件 贴心服务',
    type: 'djj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 0,
    payTypes: [0, 2, 1],
    rate: 0.5,
    rate_type: 'append',
    sort_info: { key: 2, title: '经济实惠', sub_title: '42%的人选择' },
    disable: 0,
  },
  {
    brand: 'yt',
    hot: 1,
    message: '一键下单 快速取件',
    type: 'djj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 0,
    payTypes: [0, 2, 1],
    rate: 0.5,
    rate_type: 'append',
    sort_info: { key: 2, title: '经济实惠', sub_title: '42%的人选择' },
    disable: 0,
  },
  {
    brand: 'sto',
    hot: 1,
    message: '安心下单 闪电送达',
    type: 'djj',
    pay: '3',
    cutPayDesc: '在快递员上门揽收后',
    weightLimitMax: '',
    weightLimitMin: '',
    isYjkd: 1,
    arrivePay: 0,
    payTypes: [0, 2, 1],
    rate: 0.5,
    rate_type: 'append',
    sort_info: { key: 2, title: '经济实惠', sub_title: '42%的人选择' },
    disable: 0,
  },
];
/**
 * 兼容朋友圈分享单页面,报价单
 *  */
export const fakeQuotationList = [
  {
    price: '9.6',
    discount_price: '12.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '12.00',
    s_fee: '2.00',
    s_total_fee: '0.00',
    original_price: '12.00',
    original_f_fee: 12,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'sf',
  },
  {
    price: '9',
    discount_price: '12.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '12.00',
    s_fee: '2.00',
    s_total_fee: '0.00',
    original_price: '12.00',
    original_f_fee: 12,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'jd',
  },
  {
    price: '7.3',
    discount_price: '10.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '10.00',
    s_fee: '2.00',
    s_total_fee: '0.00',
    original_price: '10.00',
    original_f_fee: 10,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'dp',
  },
  {
    price: '5.70',
    discount_price: '10.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '5.70',
    s_fee: '1.8',
    s_total_fee: '0.00',
    original_price: '10.00',
    original_f_fee: 10,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'yd',
  },
  {
    price: '6.30',
    discount_price: '10.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '6.50',
    s_fee: '1.9',
    s_total_fee: '0.00',
    original_price: '10.00',
    original_f_fee: 10,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'yt',
  },
  {
    price: '5.20',
    discount_price: '10.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '6.00',
    s_fee: '1.55',
    s_total_fee: '0.00',
    original_price: '10.00',
    original_f_fee: 10,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'sto',
  },
  {
    price: '7.30',
    discount_price: '10.00',
    discount_total_amount: 0,
    f_kg: 1,
    s_kg: '0',
    f_fee: '7.30',
    s_fee: '3.7',
    s_total_fee: '0.00',
    original_price: '10.00',
    original_f_fee: 10,
    original_s_fee: 2,
    original_s_total_fee: '0.00',
    discount_list: [],
    commission: '',
    brand: 'jt',
  },
];

/**
 * 计算保价费用
 * @param brand 品牌
 * @param keepFee 声明价格
 *  */
export const calculateCostFee = ({ brand, keepFee = 0 }) => {
  let cost_value = '';
  if (brand == 'dp') {
    if (keepFee > 0 && keepFee <= 300) {
      cost_value = 1;
    } else if (keepFee > 300) {
      cost_value = Math.ceil(keepFee * 0.004);
    }
  } else if (brand === 'zt') {
    if (keepFee > 0 && keepFee <= 1000) {
      cost_value = 1;
    } else if (keepFee > 1000 && keepFee <= 2000) {
      cost_value = 2;
    } else {
      cost_value = Math.ceil(keepFee * 0.003);
    }
  } else if (brand === 'jt') {
    if (keepFee > 0 && keepFee <= 3000) {
      cost_value = 0;
    } else if (keepFee > 3000) {
      cost_value = Math.ceil(keepFee * 0.006);
    }
  } else {
    if (keepFee > 0 && keepFee <= 500) {
      cost_value = 1;
    } else if (keepFee >= 501 && keepFee <= 1000) {
      cost_value = 2;
    } else if (keepFee > 1000) {
      cost_value = Math.ceil(keepFee * 0.005);
    }
  }
  return cost_value;
};

/**
 * 检查中通下单是否满足批量条件
 * @returns {Promise<boolean>} true:通过，false:不通过
 *  */
export const checkZtOrder = (params) => {
  return new Promise((resolve) => {
    request({
      url: '/api/Online/batchNumberOrderCount',
      toastLoading: false,
      toastError: true,
      toastSuccess: false,
      data: params,
    })
      .then((res) => {
        const { data } = res;
        const { count = 0 } = data;
        resolve(count);
      })
      .catch(() => {
        resolve(0);
      });
  });
};

// 获取下单品牌列表
export const getOrderBrandList = (opt) => {
  const { source = '' } = opt || {};
  // console.log('获取下单品牌列表.source', source);
  return new Promise((resolve) => {
    request(
      {
        url: '/api/Online/getBrands',
        toastLoading: false,
        loadingStatusKey: 'loading',
        data: { source },
        onThen: ({ code, data }) => {
          if (code == 0 && data && data.length) {
            const brands = data.filter((v) => !v.disable);
            resolve(brands);
          } else {
            resolve([]);
          }
        },
      },
      this,
    );
  });
};

// 检查下单地址是否完整
export const checkAddressComplete = (data) => {
  if (!data) return false;
  const address = transferAddress(data);
  return (
    checkDataComplete(formatAddress(address, 'shipper').data).complete &&
    checkDataComplete(formatAddress(address, 'shipping').data).complete
  );
};

export const useLoadQuotation = (opt) => {
  const {
    source,
    weight,
    address,
    delivery_type,
    payType,
    isBatch,
    volume,
    receiveList,
    payDiscount,
    cabinetInfo,
    reserve_start_time,
    reserve_end_time,
    back_sign_bill,
    package_service,
    pickup_way,
    floor,
    coupon,
  } = opt || {};

  const quotationRef = useRef({});
  const [loading, setLoading] = useState(true);
  const [list, setList] = useState([]);

  const triggerSet = (v = []) => {
    setList(v);
  };

  quotationRef.current.getQuotation = debounce(
    (params) => {
      console.log('参数===>', { ...params });
      if (checkAddressComplete(address)) {
        // 获取报价单
        setLoading(true);
        getQuotation(params)
          .then(async (quotationList) => {
            if (isArray(quotationList)) {
              const brandList = await getOrderBrandList({ source });
              const list = quotationList.map((item, index) => {
                // 整合快递品牌配置
                const oBrandBaseConfig = isArray(brandList)
                  ? brandList.find((i) => i.brand === item.brand)
                  : {};
                // 唯一标识
                item.id = `${item.brand}-${index}`;
                // 产品类型
                const { product_types: { offline = [], online = [] } = {}, message } =
                  oBrandBaseConfig || {};
                const productTypes = payType == 0 ? online : offline;
                const curProductType = productTypes.find((i) => i.value == item.delivery_type);
                const _message = message ? message : curProductType ? curProductType.mssage : '';
                return {
                  ...oBrandBaseConfig,
                  ...item,
                  productTypes,
                  message: _message,
                };
              });
              // 修正快宝推荐品牌排序问题
              // 快宝推荐品牌排到最实惠后面
              const recommendIndex = list.findIndex((i) => i.is_recommend == 1);
              const cheapIndex = list.findIndex((i) => i.is_cheap == 1);
              if (recommendIndex > -1 && cheapIndex > -1 && recommendIndex != cheapIndex) {
                const recommendItem = list[recommendIndex];
                list.splice(recommendIndex, 1);
                list.splice(cheapIndex + 1, 0, recommendItem);
              }
              triggerSet(list);
            } else {
              triggerSet([]);
            }
          })
          .catch(() => {
            triggerSet([]);
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        triggerSet([]);
      }
    },
    1000,
    { leading: false, trailing: true },
  );

  useEffect(() => {
    const reqData = {
      source,
      weight,
      address,
      delivery_type,
      payType,
      isBatch,
      volume,
      receiveList,
      payDiscount,
      cabinetInfo,
      reserve_start_time,
      reserve_end_time,
      back_sign_bill,
      package_service,
      pickup_way,
      floor,
      coupon,
    };
    if (reqData.source === 'big_package' && (!weight || weight < 30)) {
      reqData.weight = 30;
    }

    // 优化时间参数的比较逻辑，避免频繁触发
    const lastData = quotationRef.current.lastData;
    if (lastData) {
      // 检查除时间外的其他参数是否有变化
      const { reserve_start_time: lastStartTime, reserve_end_time: lastEndTime, ...lastOtherData } = lastData;
      const { reserve_start_time: currentStartTime, reserve_end_time: currentEndTime, ...currentOtherData } = reqData;

      // 如果其他参数没有变化，只有时间参数变化，则使用更严格的比较
      if (isEqual(currentOtherData, lastOtherData)) {
        // 时间参数相同则直接返回，避免重复请求
        if (lastStartTime === currentStartTime && lastEndTime === currentEndTime) {
          return;
        }
        // 如果时间参数都是默认值（1h），也避免重复请求
        if ((lastStartTime === '1h' && currentStartTime === '1h') &&
            (lastEndTime === '1h' && currentEndTime === '1h')) {
          return;
        }
      }
    }

    // 完全相同的数据不重复请求
    if (isEqual(reqData, lastData)) return;

    quotationRef.current.lastData = { ...reqData };
    quotationRef.current.getQuotation({ ...reqData });
  }, [
    source,
    weight,
    address,
    delivery_type,
    payType,
    isBatch,
    volume,
    receiveList,
    payDiscount,
    cabinetInfo,
    reserve_start_time,
    reserve_end_time,
    back_sign_bill,
    package_service,
    pickup_way,
    floor,
    coupon,
  ]);

  return {
    loading,
    list,
  };
};

/**
 * 获取当前选中的品牌对应的默认支付方式
 * @param {*} payTypes 支付方式列表 0支付给平台、1到付、2付款
 * @param {*} payType
 */
export const getDefaultPayType = (payTypes = [], payType) => {
  if (payTypes && payTypes.length > 0) {
    const isSupport = payTypes.findIndex((i) => i == payType) > -1;
    return isSupport ? payType : payTypes[0] || 0;
  }
  return payType || 0;
};

/**
 * 获取当前选中的品牌对应的默认产品类型
 * @param {*} oBrandItem
 * @param {*} payType
 * @returns null|{label:'标快',value:'offer'}
 */
export const getDefaultProductType = (oBrandItem = {}, payType = 0, cur_delivery_type) => {
  const {
    payTypes = [],
    product_types: { offline = [], online = [] } = {},
    delivery_type_name,
    delivery_type,
  } = oBrandItem || {};
  const _payType = getDefaultPayType(payTypes, payType);
  const productTypes = _payType == 0 ? online : offline;
  if (delivery_type_name) {
    return { value: delivery_type };
  }
  if (Array.isArray(productTypes) && productTypes.find((item) => item.value == cur_delivery_type)) {
    return { value: cur_delivery_type };
  }
  return productTypes && productTypes.length > 0 ? productTypes[0] : null;
};

export const getBrandItem = (brand = '', list = []) => {
  return list && list.length > 0 ? list.find((i) => i.brand == brand) : null;
};
