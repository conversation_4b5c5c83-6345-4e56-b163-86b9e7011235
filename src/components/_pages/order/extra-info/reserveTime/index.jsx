/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment, createRef } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { reserve_time, getFormItem } from '@/components/_pages/order/_utils';
import Form from '@base/utils/form';
import { debounce, noop } from '@base/utils/utils';
import {
  checkAddressComplete,
  getCurQuotationItem,
} from '@/components/_pages/order/_utils/order.edit';
import { defaultTime, getFirstTime } from '@/components/_pages/order/appointment-time/_utils';
import KbAppointmentTime from '@/components/_pages/order/appointment-time';
import isEqual from 'lodash/isEqual';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    data: {},
    onChange: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.state = {
      timerCheckedActivity: '立即取件',
    };
    this.appointmentTimeRef = createRef();
    this.handleCleanForm = debounce(this.handleCleanForm, 500, {
      leading: false,
      trailing: true,
    });
  }
  componentDidMount() {
    this.createForm(() => {
      this.handleCleanForm();
    });
  }

  componentWillReceiveProps(nextPops) {
    const { clean, brand, quotationList, brandInfo = {} } = nextPops;
    const {
      clean: prevClean,
      brand: prevBrand,
      quotationList: prevQuotationList,
      brandInfo: prevBrandInfo = {},
    } = this.props;
    const { order_type } = getCurQuotationItem(brand, quotationList) || {};
    const { preOrder_type } = getCurQuotationItem(brand, prevQuotationList) || {};
    if (clean != prevClean) {
      this.handleCleanForm();
    }
    if (brandInfo.delivery_type != prevBrandInfo.delivery_type) {
      this.handleCleanForm();
    }
    if (brand != prevBrand || order_type != preOrder_type) {
      if (!isEqual({ brand, order_type }, this.loadReqData)) {
        this.loadReqData = {
          brand,
          order_type,
        };
        this.handleCleanForm();
      }
    }
  }
  handleChange = (data) => {
    const { expectedTime: timerCheckedActivity, ...rest } = data;
    this.setState({ timerCheckedActivity });
    this.formIns.update(rest);
  };
  handleCleanForm() {
    console.log('执行handleCleanForm')
    if (!this.props.brand) return;
    if (
      this.appointmentTimeRef &&
      this.appointmentTimeRef.current &&
      this.appointmentTimeRef.current.initTimeData
    ) {
      this.appointmentTimeRef.current.initTimeData(
        (times) => {
          // console.log('handleCleanForm设置数据')
          const firstTime = getFirstTime(times);
          const time = firstTime || defaultTime;
          this.setState({
            timerCheckedActivity: time.label,
          });
          this.formIns.update({
            reserve_start_time: time.start,
            reserve_end_time: time.end,
          });
        },
        { toastError: false, fData: this.props },
      );
    }
  }
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (cb) => {
    const form = getFormItem({
      keys: [...reserve_time],
      data: {
        reserve_end_time: '1h',
        reserve_start_time: '1h',
      },
    });
    // const { storageKey } = this.props;
    this.formIns = new Form(
      {
        form,
        // storageKey,
        onUpdate: (data) => {
          const { onChange } = this.props;
          onChange(data.nextData, data.eventType);
        },
        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };

  handleOpenTimer = () => {
    const { isNeedAddress, address } = this.props;
    if (isNeedAddress && !checkAddressComplete(address)) {
      Taro.kbToast({
        text: '请完善地址信息后再选择',
      });
      return;
    }
    this.appointmentTimeRef.current.onShowPicker();
  };

  render() {
    const { timerCheckedActivity } = this.state;
    const { brand, action } = this.props;
    const isModify = action === 'modify';
    return (
      <Fragment>
        {isModify ? (
          <View className='kb-color__brand' onClick={this.handleOpenTimer} hoverClass='kb-hover'>
            修改
          </View>
        ) : (
          <View className='kb-navigator--group' hoverClass='kb-hover'>
            <View className='kb-navigator--group-item' onClick={this.handleOpenTimer}>
              <View className='kb-navigator--group-item__title'>上门时间</View>
              <View className='kb-navigator--group-item__content kb-navigator'>
                {timerCheckedActivity}
              </View>
            </View>
          </View>
        )}
        <KbAppointmentTime
          {...this.props}
          brand={brand}
          ref={this.appointmentTimeRef}
          onChange={this.handleChange}
        />
      </Fragment>
    );
  }
}

export default Index;
