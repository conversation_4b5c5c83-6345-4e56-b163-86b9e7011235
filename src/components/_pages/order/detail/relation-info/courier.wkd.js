/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { AtAvatar, AtIcon } from 'taro-ui';
import { View, Text } from '@tarojs/components';
import { makePhoneCall } from '@base/utils/utils';
import { getRelationAvatar } from '@/components/_pages/order/_utils/order.detail';
import request from '@base/utils/request';
import './index.scss';

const Index = (props) => {
  const {
    orderType,
    data: { name, phone, id, type, avator_url = getRelationAvatar({ id, type, orderType }) },
    tag = 'collect',
    orderId,
    canHurry,
    onHurryUp,
  } = props;

  // 呼叫
  const handleCall = () => makePhoneCall(phone);

  // 跳转主页按钮
  const hasHome = !!phone && orderType != 'tcjs';
  const handleDetail = () => {
    Taro.navigator({
      url: `order/station`,
      options: {
        courierId: id,
        phone,
      },
    });
  };

  // 催件
  const canHurryUp = canHurry === 'true';
  const handleHurryUp = () => {
    Taro.kbModal({
      content: '提醒快递员及时取件？',
      onConfirm: () => {
        request({
          url: '/v1/GrabOrder/hurry',
          data: {
            order_id: orderId,
          },
          toastError: true,
          toastSuccess: '已通知快递员',
          quickTriggerThen: true,
          onThen: ({ code }) => {
            code == 0 && onHurryUp();
          },
        });
      },
    });
  };

  //标签
  const tagMap = {
    collect: {
      color: 'brand',
      text: '收',
    },
    dispatch: {
      color: 'green',
      text: '派',
    },
  };
  const tagObj = tagMap[tag] && orderType != 'tcjs';

  return (
    <View className='at-row at-row__align--center'>
      <AtAvatar circle size='small' image={avator_url} />
      <View className='at-col'>
        <Text className='kb-spacing-sm-l'>{name}</Text>
        {phone && <Text className='kb-spacing-sm-l'>{phone}</Text>}
        {tagObj && tagObj.text && (
          <Text className={`kb-relation-info__tag kb-relation-info__tag--${tagObj.color}`}>
            {tagObj.text}
          </Text>
        )}
      </View>
      {phone && (
        <View className='kb-relation-info__icon' hoverClass='kb-hover-opacity' onClick={handleCall}>
          <AtIcon
            prefixClass='kb-icon'
            value='phone'
            className='kb-icon-size__base kb-color__brand'
          />
        </View>
      )}
      {hasHome && (
        <View
          className='kb-relation-info__icon'
          hoverClass='kb-hover-opacity'
          onClick={handleDetail}
        >
          <AtIcon
            prefixClass='kb-icon'
            value='home'
            className='kb-icon-size__base kb-color__brand'
          />
        </View>
      )}
      {canHurryUp && (
        <View
          className='kb-relation-info__icon'
          hoverClass='kb-hover-opacity'
          onClick={handleHurryUp}
        >
          <AtIcon
            prefixClass='kb-icon'
            value='bell'
            className='kb-icon-size__base kb-color__brand'
          />
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {},
};

export default Index;
