/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { AtAvatar, AtIcon } from 'taro-ui';
import { View, Text, Button } from '@tarojs/components';
import { noop } from '@base/utils/utils';
import { fillListByCount } from '@/components/_pages/order/_utils/order.welfare';
import { envNames } from '@/utils/config';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import KbDak from './dak';
import KbCourierInfo from './courier';
import './index.scss';

const Index = (props) => {
  const {
    data: {
      can_hurry,
      brand,
      relationData,
      dispatchCourier,
      order_id,
      cancel_time,
      order_state,
      brandInfo,
      help_status: helpData,
      platform,
      source,
      cutPayDesc,
    },
    onHurryUp,
    orderType,
  } = props;
  const { type, dakId, phone, id } = relationData;

  let tips = '';
  if (orderType === 'tcjs') {
    if (order_state === 'accepted') {
      const useCarBrands = ['huolala', 'fczy'];
      tips = `${brandInfo.name}正在为您分配${
        useCarBrands.includes(brand) ? '司机' : '骑手'
      }接单...`;
    } else if (order_state === 'canceled' && cancel_time) {
      tips = `取消时间：${cancel_time}`;
    }
  }
  // 是否显示快递员、驿站信息卡片
  const isShowCard = type && (dakId || phone || id || dispatchCourier);
  // 优寄快递
  const isYjkd = platform === 'yjkd' && source === 'kbyj';
  // 助力
  const {
    help_status: helpStatus,
    fee: helpFee,
    help: helpList,
    coupon_type: helpType,
  } = helpData || {};
  fillListByCount(helpList);

  return (
    <View>
      {tips ? (
        <View className='kb-relation-info'>{tips}</View>
      ) : !!isShowCard ? (
        <View className='kb-relation-info'>
          {type === 'dak' ? (
            <KbDak data={relationData} />
          ) : (
            <Fragment>
              <KbCourierInfo
                data={relationData}
                orderType={orderType}
                orderId={order_id}
                canHurry={can_hurry}
                onHurryUp={onHurryUp}
              />
              {dispatchCourier && (
                <View className='kb-margin-md-t'>
                  <KbCourierInfo data={dispatchCourier} tag='dispatch' />
                </View>
              )}
            </Fragment>
          )}
        </View>
      ) : null}
      {isYjkd && (
        <View className='at-row at-row__align--center kb-spacing-lg-t'>
          <AtAvatar
            circle
            className='kb-avatar__middle'
            image='https://cdn-img.kuaidihelp.com/brand_logo/icon_yjkd.png?t=20230316'
          />
          <View className='kb-relation-info__yjkd'>
            <View>*无需支付运费给揽件快递员</View>
            <View>
              *{cutPayDesc}，自动从您{envNames[process.env.PLATFORM_ENV]}
              账户扣取运费
            </View>
            {process.env.PLATFORM_ENV === 'weapp' && (
              <Fragment>
                {helpFee > 0 && <View>*福利券已优惠{helpFee}元</View>}
                {helpData && helpType != '4' && (
                  <View className='at-row at-row__align--center kb-spacing-sm-t'>
                    {helpStatus < 4 && isArray(helpList) && (
                      <View
                        className={classNames('kb-relation-info__avatar', {
                          'kb-relation-info__avatar-long': helpList.length > 3,
                        })}
                      >
                        {helpList.map((item) => (
                          <View key={item.uid} className='kb-relation-info__avatar--item'>
                            <AtAvatar
                              circle
                              size='small'
                              image={
                                item.avatar ||
                                'https://cdn-img.kuaidihelp.com/wkd/miniApp/images/help-avatar.png'
                              }
                            />
                          </View>
                        ))}
                      </View>
                    )}
                    {helpStatus < 3 ? (
                      <View>
                        <Button
                          openType='share'
                          className='kb-relation-info__share'
                          dataPage='order.help'
                          dataInfo={helpData}
                          hoverClass='kb-hover-opacity'
                        >
                          <Text className='kb-color__orange kb-size__sm kb-icon__text--mr'>
                            助力火热进行中...
                          </Text>
                          <AtIcon
                            prefixClass='kb-icon'
                            value='share-circle'
                            className='kb-icon-size__sm kb-color__orange'
                          />
                        </Button>
                      </View>
                    ) : (
                      <View className='kb-color__orange kb-size__sm'>
                        {helpStatus == 3
                          ? '助力成功，本单你已享受最高优惠'
                          : helpStatus == 4
                          ? '助力失败，本单你只享受入门优惠；'
                          : ''}
                      </View>
                    )}
                  </View>
                )}
              </Fragment>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {
    brandInfo: {},
    statusInfo: {},
    relationData: {},
  },
  orderType: '',
  onHurryUp: noop,
};

export default Index;
