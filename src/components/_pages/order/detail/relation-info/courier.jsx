/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { AtAvatar, AtIcon } from 'taro-ui';
import { View, Text } from '@tarojs/components';
import { makePhoneCall } from '@base/utils/utils';
import { getRelationAvatar } from '@/components/_pages/order/_utils/order.detail';
import './index.scss';

const Index = (props) => {
  const {
    data: { name, phone, id, type, avator_url = getRelationAvatar({ id, type }), statusInfo = {} },
  } = props;

  const handleCall = () => makePhoneCall(phone);
  const handleDetail = () => {
    Taro.navigator({
      url: `order/station`,
      options: {
        courier_id: id,
      },
    });
  };

  return type === 'courier' ? (
    <Fragment>
      {statusInfo.key === 'canceled' ? (
        <View className='kb-relation-info'>期待下次为您服务</View>
      ) : (
        <Fragment>
          {id ? (
            <View className='kb-relation-info'>
              <View className='at-row at-row__align--center'>
                <AtAvatar circle size='small' image={avator_url} />
                <View className='at-col'>
                  <Text className='kb-spacing-sm-l'>{name}</Text>
                  {phone && <Text className='kb-spacing-sm-l'>{phone}</Text>}
                </View>
                {phone && (
                  <View
                    className='kb-relation-info__icon'
                    hoverClass='kb-hover-opacity'
                    onClick={handleCall}
                  >
                    <AtIcon
                      prefixClass='kb-icon'
                      value='phone'
                      className='kb-icon-size__base kb-color__brand'
                    />
                  </View>
                )}
                <View
                  className='kb-relation-info__icon'
                  hoverClass='kb-hover-opacity'
                  onClick={handleDetail}
                >
                  <AtIcon
                    prefixClass='kb-icon'
                    value='home'
                    className='kb-icon-size__base kb-color__brand'
                  />
                </View>
              </View>
            </View>
          ) : (
            <View className='kb-relation-info'>正在为您分配快递员，请保持手机通畅</View>
          )}
        </Fragment>
      )}
    </Fragment>
  ) : (
    <Fragment></Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {},
};

export default Index;
