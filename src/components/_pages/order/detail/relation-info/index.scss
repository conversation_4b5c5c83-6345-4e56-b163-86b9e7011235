

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


.kb-relation-info {
  margin-top: $spacing-v-lg;
  padding-top: $spacing-v-lg;
  text-align: left;
  border-top: $border-lightest;

  &__tag {
    margin-left: $spacing-h-sm;
    padding: 2px 6px;
    color: $color-white;
    font-size: $font-size-sm;
    background: $color-brand;
    border-radius: $border-radius-md;
    &--green {
      background: $color-green;
    }
  }

  &__icon {
    margin-left: $spacing-h-lg;
  }

  &__yjkd {
    padding-left: $spacing-h-sm;
    font-size: $font-size-sm;
    line-height: 35px;
    text-align: left;
  }

  &__share {
    height: auto;
    margin: 0;
    margin-left: $spacing-h-sm;
    padding: 0;
    line-height: 1;
    background-color: transparent !important;

    &::after {
      display: none;
    }
  }

  &__avatar {
    display: flex;
    align-items: center;

    &--item {
      padding-right: $spacing-h-sm;
    }

    &-long {
      position: relative;
      max-width: 200px;
      overflow: hidden;

      &::after {
        position: absolute;
        top: 0;
        right: 0;
        display: inline-block;
        height: 100%;
        padding: 0 $spacing-h-sm;
        background-color: $color-white;
        border-radius: 50% 0 0 50%;
        content: '...';
      }
    }

    &-long &--item {
      margin-left: -40px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.kb-clear {
  &__padding {
    padding: 0;
  }
}
