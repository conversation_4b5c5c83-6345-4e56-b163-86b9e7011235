/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { AtAvatar, AtIcon } from 'taro-ui';
import { View } from '@tarojs/components';
import { makePhoneCall } from '@base/utils/utils';
import { getRelationAvatar } from '@/components/_pages/order/_utils/order.detail';

const Index = (props) => {
  const {
    data: {
      name,
      phone,
      id,
      type,
      workday,
      start_time,
      end_time,
      concat_area,
      concat_location,
      avator_url = getRelationAvatar({ id, type }),
    },
  } = props;

  let time = `${start_time}-${end_time}`;
  if (workday) {
    time = `${workday} ${time}`;
  }

  // 呼叫
  const handleCall = () => makePhoneCall(phone);
  // 导航
  const handleGps = () => {
    const { latitude, longitude } = props.data;
    Taro.openLocation({
      name,
      address: concat_area + concat_location,
      latitude: latitude * 1,
      longitude: longitude * 1,
      scale: 18,
    });
  };

  return (
    <View className='at-row at-row__align--center'>
      <View className='kb-spacing-md-r'>
        <AtAvatar circle image={avator_url} />
      </View>
      <View className='at-col'>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View>{name}</View>
          {phone && (
            <View
              className='kb-relation-info__icon'
              hoverClass='kb-hover-opacity'
              onClick={handleCall}
            >
              <AtIcon
                prefixClass='kb-icon'
                value='phone'
                className='kb-icon-size__base kb-color__brand'
              />
            </View>
          )}
        </View>
        <View className='kb-color__grey kb-size__base'>
          <View className='kb-spacing-sm-t'>营业时间：{time}</View>
          <View
            className='kb-spacing-sm-t at-row at-row__align--center'
            hoverClass='kb-hover-opacity'
            onClick={handleGps}
          >
            <View className='at-col'>
              {concat_area}
              {concat_location}
            </View>
            <View className='kb-spacing-md-l'>
              <AtIcon
                prefixClass='kb-icon'
                value='gps'
                className='kb-icon-size__base kb-color__brand'
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {},
};

export default Index;
