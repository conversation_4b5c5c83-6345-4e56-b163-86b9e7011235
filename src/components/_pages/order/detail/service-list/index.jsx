/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { getServiceList } from '@/components/_pages/order/_utils/order.detail.service-list';
import { noop } from '@base/utils/utils';
import './index.scss';

const serviceMap = {
  pro_price: {
    title: '保价费',
  },
  dec_val: {
    title: '物品价值',
  },
  collection: {
    title: '代收金额',
  },
  arrive_pay: {
    title: '到付金额',
  },
};

const Index = (props) => {
  const { data, orderType, onChange } = props;
  const { order_id } = data;
  const [list, upList] = useState([]);

  const triggerList = (data) => {
    let keys = Object.keys(serviceMap).filter((key) => !!data[key]);
    let serviceList = [];
    keys.map((item) => {
      serviceList.push({
        key: item,
        title: serviceMap[item].title,
        value: data[item],
      });
    });
    upList(serviceList);
  };

  useEffect(() => {
    if (!order_id) return;
    if (process.env.MODE_ENV === 'wkd') {
      getServiceList(data, { orderType })
        .then((res) => {
          let oService = {};
          let oServiceMap = {
            保价服务: 'pro_price',
            声明物品价值: 'dec_val',
            代收货款: 'collection',
            到付: 'arrive_pay',
          };
          res.list.map((item) => {
            let sKey = oServiceMap[item.name];
            if (sKey) {
              oService[sKey] = item.price;
            }
          });
          triggerList(oService);
          onChange(res);
        })
        .catch((err) => console.log(err));
    } else {
      triggerList(data);
    }
  }, [order_id]);

  return list.length ? (
    <View className='at-row kb-box--item'>
      <View>增值服务</View>
      <View className='at-col at-row kb-service at-row__align--center'>
        {list.map((item) => {
          return (
            <View className='kb-service--item kb-margin-lg-l' key={item.key}>
              <Text className='kb-spacing-xs-r'>{item.title}</Text>
              <Text>¥{item.value}</Text>
            </View>
          );
        })}
      </View>
    </View>
  ) : (
    ''
  );
};
Index.defaultProps = {
  data: {},
  onChange: noop,
};
Index.options = { addGlobalClass: true };

export default Index;
