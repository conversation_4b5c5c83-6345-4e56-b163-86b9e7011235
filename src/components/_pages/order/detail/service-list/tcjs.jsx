/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useEffect, useState, Fragment } from '@tarojs/taro';
import { getServiceList } from '@/components/_pages/order/_utils/order.detail.service-list';
import { noop } from '@base/utils/utils';
import KbDownContainer from '@base/components/down-container';
import classNames from 'classnames';
import './index.scss';

const Index = (props) => {
  const { data, onChange, orderType } = props;
  const { order_id } = data;
  const [list, updateList] = useState(null);
  const [isOpened, updateIsOpened] = useState(false);

  useEffect(() => {
    if (!order_id) return;
    getServiceList(data, { orderType })
      .then((res) => {
        updateList(res.list);
        onChange(res);
      })
      .catch((err) => console.log(err));
  }, [order_id]);
  // 增值服务类名
  const serviceCls = classNames('kb-navigator kb-navigator-noborder', {
    'kb-navigator__down': !isOpened,
    'kb-navigator__up': isOpened,
  });
  const handleSwitchOpen = () => updateIsOpened(!isOpened);

  return list ? (
    <View className='kb-box kb-box__service-list'>
      <View hoverClass='kb-hover' onClick={handleSwitchOpen} className={serviceCls}>
        <View className='kb-navigator__label'>已选服务</View>
      </View>
      <KbDownContainer isOpened={isOpened}>
        <View className='kb-spacing-md-lr'>
          {list.map((item) => (
            <View
              key={item.id}
              className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-b'
            >
              <View className='kb-color__grey kb-service-list__name'>{item.name}</View>
              <View className='kb-color__red'>
                {item.price ? <Fragment>￥{item.price}</Fragment> : item.desc}
              </View>
            </View>
          ))}
        </View>
      </KbDownContainer>
    </View>
  ) : null;
};

Index.defaultProps = {
  data: {},
  orderType: '',
  onChange: noop,
};
Index.options = { addGlobalClass: true };

export default Index;
