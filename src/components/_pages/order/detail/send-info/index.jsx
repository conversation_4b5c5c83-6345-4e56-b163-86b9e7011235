/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { View, Fragment, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import './index.scss';

const SendInfo = (props) => {
  const { data = [] } = props;
  const handleShowToast = (item) => {
    if (item.reason && item.status != 1) {
      Taro.kbToast({
        text: item.reason,
      });
    }
  };

  return (
    <Fragment>
      {Array.isArray(data) && data.length ? (
        <View className='kb-sendInfo kb-margin-lg kb-spacing-lg kb-background__white'>
          <View className='kb-size__lg kb-size__bold'>短信催收记录</View>
          <View className='kb-sendInfo-box'>
            <View className='at-row kb-sendInfo-box__title'>
              <View className='at-col-4'>发送时间</View>
              <View className='at-col-4'>发送状态</View>
              <View className='at-col-4'>发送对象</View>
            </View>
            {data.map((item, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <View className='at-row kb-sendInfo-box__item' key={index}>
                <View className='at-col-4 kb-size__xs'>{item.create_time}</View>
                <View className='at-col-4'>
                  <View
                    className='at-row at-row__align--center at-row__justify--center'
                    hoverClass='kb-hover-opacity'
                    onClick={() => handleShowToast(item)}
                  >
                    <AtIcon
                      value={item.status == 1 ? 'chenggong' : 'close'}
                      className={classNames('kb-size__base', {
                        'kb-color__green': item.status == 1,
                        'kb-color__red': item.status != 1,
                      })}
                      prefixClass='kb-icon'
                    />
                    <Text className='kb-margin-sm-lr'>
                      {item.status == 1 ? '发送成功' : '发送失败'}
                    </Text>
                    {item.status != 1 && (
                      <AtIcon
                        value='question'
                        className='kb-size__sm kb-color__grey'
                        prefixClass='kb-icon'
                      />
                    )}
                  </View>
                </View>
                <View className='at-col-4'>
                  {item.type == 1
                    ? '寄件人'
                    : item.type == 2
                    ? '团员'
                    : item.type == 3
                    ? '自定义'
                    : ''}
                </View>
              </View>
            ))}
          </View>
        </View>
      ) : null}
    </Fragment>
  );
};

SendInfo.options = {
  addGlobalClass: true,
};

export default SendInfo;
