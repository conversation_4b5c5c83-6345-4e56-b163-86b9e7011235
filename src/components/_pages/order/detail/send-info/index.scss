/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-sendInfo {
  border-radius: $border-radius-xxl;
  &-box {
    box-sizing: border-box;
    margin-top: $spacing-h-md;
    color: $color-grey-2;
    font-size: $font-size-base;
    border: $width-base solid rgb(235, 237, 240);
    border-radius: $border-radius-xxl;
    &__title {
      box-sizing: border-box;
      padding: 20px 32px;
      text-align: center;
      background-color: #f7f8fa;
    }
    &__item {
      box-sizing: border-box;
      padding: 20px 32px;
      text-align: center;
    }
  }
}
