/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-detail-wrapper {
  position: relative;
  margin: $spacing-h-lg;
  padding: 0 $spacing-h-lg;
  border-radius: $border-radius-xxl;
  &__brand {
    display: flex;
    align-items: flex-start;
    .kb-brand-tag {
      display: inline-block;
      padding: 5px 10px;
      color: $color-white;
      font-size: 22px;
      background-color: $color-red;
      border-radius: 10px;
    }
  }

  &__line {
    min-height: 60px;
    font-size: $font-size-md;
    .at-row {
      width: auto;
    }
    &--label {
      width: 188px;
      font-size: $font-size-md;
    }
    &--value {
      display: flex;
      flex: 1;
      align-items: center;
      font-size: $font-size-md;
      line-height: 42px;
    }
    .kb-phone-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: 0 16px;
      background-color: #f7f8fa;
      border-radius: $border-radius-circle;
    }
    .kb-member-tag {
      padding: 7px 16px;
      background-color: #f7f8fa;
      border-radius: $border-radius-arc;
    }
  }

  .kb-spacing-48-b {
    padding-bottom: 48px;
  }
  .line-border {
    margin-top: 48px;
    border-bottom: $border-lighter;
  }

  .kb-color__click {
    color: #576b95;
  }
  .kb-color__yellow {
    color: $color-yellow;
  }
  .kb-ml {
    margin-left: $spacing-h-sm;
  }
  .kb-maxWidth {
    width: 250px;
  }
}
