/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { AtIcon, AtAvatar } from 'taro-ui';
import { makePhoneCall } from '@base/utils/utils';
import { get } from '@/actions/brands';
import { useSelector } from '@tarojs/redux';
import { useUpdate } from '@base/hooks/page';
import { setClipboardData } from '@/utils/qy';
import { getExpressTrack } from '../../_utils/order.detail';
import KbAddress from '../address-info';
import KbPartnerTag from '../../extra-info/brand/partner';
import KbModifyTime from '../../extra-info/reserveTime/modify-time';
import './index.scss';

const KbOrderStatusAndAddress = (props) => {
  const { data, onFresh } = props;

  const { brands = {} } = useSelector((state) => state.global);
  const [logicsInfo, setLogicsInfo] = useState('');
  const {
    logistic_status_txt,
    pickup,
    remark,
    order_status,
    collect_courier_name,
    collect_courier_mobile,
    user_id,
    isAdmin,
    brand,
    waybill,
    reserve_time_desc,
    pickup_code,
    is_partner_order,
    arrive_pay,
    channel_type,
    courier,
    cabinet_record = {},
    isKdgOrder,
    modify_time,
  } = data;

  const channelMap = {
    2: '快递员',
    3: '驿站',
    4: '开放平台',
  };

  const waitPay = order_status == 1;

  const showMemberDetail = (user_id) => {
    if (!user_id || !isAdmin) return;
    Taro.navigator({
      url: 'team/manage/detail',
      options: {
        user_id,
      },
    });
  };

  const formatRemark = remark && remark.length > 6 ? `${remark.substring(0, 6)}...` : remark;

  const fitBrandPhone = (brand) => {
    const { name, tel } = brands[brand] || {};
    return {
      brandName: name,
      phone: tel,
    };
  };

  const fixDeliveryType = (type) => {
    switch (type) {
      case 'offer':
        return '标快';
      case 'express':
        return '特快';
      default:
        return '';
    }
  };

  const handleCopy = (e, text, type) => {
    e.stopPropagation();
    setClipboardData(text, `${type}已复制`);
  };

  const getLogics = ({ waybill, brand, order_no, status_txt }) => {
    Taro.navigator({
      url: 'query/detail',
      options: {
        waybill,
        brand,
        order_no,
        status_txt,
      },
    });
  };

  // 获取物流信息
  const getLogicsInfo = (data) => {
    getExpressTrack(data).then((info) => {
      setLogicsInfo(info);
    });
  };

  useEffect(() => {
    if (waybill && logistic_status_txt) {
      const status = ['取消', '下单', '受理'].some((item) => logistic_status_txt.includes(item));
      if (status) {
        setLogicsInfo('');
        return;
      }
      getLogicsInfo({
        brand,
        waybill,
      });
    }
  }, [waybill, logistic_status_txt, brand]);

  // 登录状态变更
  useUpdate((loginData) => {
    if (!loginData.logined) return;
    get();
  }, []);

  const brandItem = data && data.brand && brands ? brands[data.brand] || {} : {};
  return (
    <View className='kb-detail-wrapper kb-background__white'>
      {data.order_mark === 'cngg' && <View className='kb-cngg-tag'>智能分配</View>}
      <View className='at-row at-row__align--center at-row__justify--between kb-spacing-xl-t kb-spacing-48-b'>
        <View className='kb-size__xxl kb-size__bold'>
          {logistic_status_txt}
          {isKdgOrder && !cabinet_record.into_cabinet ? `(未存柜)` : ''}
        </View>
        <View className='kb-detail-wrapper__brand'>
          <AtAvatar
            image={brandItem.logo_link}
            circle
            size='normal'
            className='kb-avatar__middle'
          />
          {(fixDeliveryType(data.delivery_type) || data.delivery_type_name) && (
            <View className='kb-brand-tag'>
              {fixDeliveryType(data.delivery_type) || data.delivery_type_name}
            </View>
          )}
        </View>
      </View>
      <View className='kb-detail-wrapper__line at-row at-row__align--start'>
        <View className='kb-detail-wrapper__line--label'>
          {fitBrandPhone(data.brand).brandName}
        </View>
        <View className='kb-detail-wrapper__line--value'>
          <View className='at-row at-row__align--start at-row__justify--between'>
            <View className='at-row at-row__align--center'>
              {data.waybill ? (
                <Fragment>
                  <View
                    hoverStopPropagation
                    hoverClass='kb-hover-opacity'
                    onClick={() =>
                      getLogics({
                        waybill: data.waybill,
                        brand: data.brand,
                        order_no: data.order_number,
                        status_txt: data.logistic_status_txt,
                      })
                    }
                  >
                    {data.waybill}
                  </View>
                  <View onClick={(e) => handleCopy(e, data.waybill, '运单号')}>
                    <AtIcon
                      prefixClass='kb-icon'
                      value='copy-text'
                      className='kb-icon-size__md kb-color__grey-3 kb-spacing-md-lr'
                    />
                  </View>
                </Fragment>
              ) : (
                '暂无单号'
              )}
            </View>
            <View
              className='kb-phone-wrapper'
              hoverClass='kb-hover-opacity'
              onClick={() => makePhoneCall(fitBrandPhone(data.brand).phone)}
            >
              <AtIcon
                prefixClass='kb-icon'
                value='phone1'
                className='kb-icon-size__md kb-color__brand'
              />
            </View>
          </View>
          <View>
            {!!is_partner_order ? (
              <KbPartnerTag
                className='kb-detail-partnerTag'
                text={is_partner_order}
                type={arrive_pay == 0 ? '3' : '2'}
              />
            ) : null}
          </View>
        </View>
      </View>
      {pickup_code && (isKdgOrder ? order_status != 0 : true) && (
        <View className='kb-detail-wrapper__line at-row at-row__align--start'>
          <View className='kb-detail-wrapper__line--label'>{isKdgOrder ? '柜子' : ''}取件码</View>
          <View className='kb-detail-wrapper__line--value'>
            {pickup_code}
            {isKdgOrder && <Text className='kb-color__red'>(快递员取件时告知对方)</Text>}
          </View>
        </View>
      )}
      {!waitPay && !['sxjd', 'htky'].includes(brand) && (
        <View className='kb-detail-wrapper__line at-row at-row__align--start'>
          <View className='kb-detail-wrapper__line--label'>预约取件</View>
          <View className='kb-detail-wrapper__line--value'>
            {reserve_time_desc || pickup}
            {!!modify_time ? (
              <View className='kb-margin-md-l'>
                <KbModifyTime data={data} onFresh={onFresh} />
              </View>
            ) : null}
          </View>
        </View>
      )}
      {channelMap[channel_type] && (
        <View className='kb-detail-wrapper__line at-row at-row__align--start'>
          <View className='kb-detail-wrapper__line--label kb-maxWidth'>
            <View className='at-row at-row__align--start'>
              <AtIcon prefixClass='kb-icon' value='star' size={14} className='kb-color__yellow' />
              <View className='kb-ml kb-color__yellow'>{channelMap[channel_type]}转单</View>
            </View>
          </View>
          <View className='kb-detail-wrapper__line--value at-row at-row__align--start at-row__justify--between'>
            <View className='kb-color__yellow'>{courier.phone}</View>
            <View className='kb-color__yellow'>{courier.name}</View>
          </View>
        </View>
      )}
      <View className='line-border' />
      <View className='kb-spacing-lg-tb'>
        {logicsInfo && (
          <View className='kb-detail-wrapper__line at-row at-row__align--start kb-margin-md-b'>
            <View className='kb-detail-wrapper__line--label kb-color__grey'>最新轨迹</View>
            <View className='kb-detail-wrapper__line--value'>
              <View>{logicsInfo}</View>
              <View
                hoverClass='kb-hover-opacity'
                onClick={() =>
                  getLogics({
                    waybill: data.waybill,
                    brand: data.brand,
                    order_no: data.order_number,
                    status_txt: data.logistic_status_txt,
                  })
                }
                className='kb-margin-sm-t kb-color__click'
              >
                查看物流详情 &gt;
              </View>
            </View>
          </View>
        )}
        {collect_courier_name && (
          <View className='kb-detail-wrapper__line at-row at-row__align--start kb-margin-md-b'>
            <View className='kb-detail-wrapper__line--label kb-color__grey'>收件快递员</View>
            <View className='kb-detail-wrapper__line--value at-row at-row__align--start at-row__justify--between'>
              <View className='at-row at-row__align--center'>
                {collect_courier_name} {collect_courier_mobile}
              </View>
              <View
                hoverClass='kb-hover-opacity'
                onClick={() => makePhoneCall(collect_courier_mobile)}
              >
                <AtIcon
                  prefixClass='kb-icon'
                  value='phone1'
                  className='kb-icon-size__sm kb-color__black'
                />
              </View>
            </View>
          </View>
        )}
        <View className='kb-margin-lg-b'>
          <KbAddress data={data} />
        </View>
        {formatRemark && (
          <View className='kb-detail-wrapper__line at-row at-row__align--center kb-margin-md-b'>
            <View className='kb-detail-wrapper__line--label kb-color__grey'>团员信息</View>
            <View className='kb-detail-wrapper__line--value'>
              <Text
                onClick={showMemberDetail.bind(null, user_id)}
                hoverClass='kb-hover-opacity'
                className='kb-member-tag kb-color__click'
              >
                {formatRemark}
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

KbOrderStatusAndAddress.defaultProps = {
  data: {},
};

KbOrderStatusAndAddress.options = {
  addGlobalClass: true,
};

export default KbOrderStatusAndAddress;
