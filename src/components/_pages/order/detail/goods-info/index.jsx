/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import qs from 'qs';
import './index.scss';

const Index = (props) => {
  const { data = {} } = props;
  const {
    list = [],
    package_note,
    package_pics = [],
    create_at,
    user_id,
    order_id,
    third_create_at,
  } = data;

  const onCheckPic = () => {
    Taro.navigator({
      url: 'order/pic',
      options: {
        order_id,
        user_id, // 用于判断能否删除图片
        pics: qs.stringify({ package_pics }),
      },
    });
  };

  const packageInfo = useMemo(() => {
    return [
      {
        title: '备注',
        key: 'package_note',
        value: package_note || '',
      },
      {
        title: '下单时间',
        key: 'create_at',
        value: create_at || '',
      },
      {
        title: '推送快递时间',
        key: 'third_create_at',
        value: third_create_at || '',
      },
    ].filter((item) => !!item.value);
  }, [package_note, third_create_at, create_at]);

  return (
    <View className='kb-goods kb-margin-lg kb-spacing-lg kb-background__white'>
      <View className='kb-goods__title kb-size__lg at-row at-row__align--center at-row__justify--between'>
        <Text>包裹信息</Text>
        <View
          className='kb-color__grey kb-size__sm'
          hoverClass='kb-hover-opacity'
          onClick={onCheckPic}
        >
          <Text>{package_pics.length > 0 ? '查看照片' : '暂无照片，去拍照'}</Text>
          <AtIcon
            prefixClass='kb-icon'
            value='arrow'
            className='kb-icon-size__sm kb-spacing-sm-l'
          />
        </View>
      </View>
      <View className='kb-goods__item--wrapper'>
        <View className='at-row at-row__align--start at-row__justify--between'>
          {list.map((val) => {
            return (
              <View className='kb-text__center kb-goods__item' key={val.icon}>
                <View className='kb-spacing-sm-b'>
                  <AtIcon prefixClass='kb-icon' value={val.icon} className='kb-icon-size__lg' />
                </View>
                <View className='kb-size__base kb-goods__item--text'>{val.text}</View>
              </View>
            );
          })}
        </View>
      </View>
      <View className='kb-goods__border-t kb-spacing-lg-t'>
        {packageInfo.map((item) => (
          <View
            key={item.key}
            className='kb-detail-wrapper__line at-row at-row__align--start kb-margin-md-b'
          >
            <View style={{ minWidth: '88px' }}>{item.title}</View>
            <Text>{item.value}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

Index.defaultProps = {
  data: {},
};
Index.options = { addGlobalClass: true };

export default Index;
