

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import { View } from '@tarojs/components';
import { AtAvatar } from 'taro-ui';
import isArray from 'lodash/isArray';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const { item: goodsList, shop_name } = data;
  const hasList = isArray(goodsList) && goodsList.length > 0;
  return hasList ? (
    <View className='kb-box__goods-list'>
      {shop_name && (
        <View className='at-row at-row__align--center kb-spacing-md-b'>
          <View>
            <AtAvatar
              className='kb-avatar__mini'
              image='https://cdn-img.kuaidihelp.com/wkd/miniApp/wzgapp.png'
            />
          </View>
          <View className='kb-spacing-md-l'>{shop_name}</View>
        </View>
      )}
      {goodsList.map((item, index) => (
        <View className='goods-list__item at-row at-row__align--center' key={`g_${index}`}>
          <View>
            <AtAvatar
              image={item.item_img || 'https://cdn-img.kuaidihelp.com/wkd/noImg.png'}
              className='kb-avatar__middle'
            />
          </View>
          <View className='kb-spacing-md-l kb-size__sm'>
            <View>{item.item_name || '暂无商品名称'}</View>
            {item.item_weight > 0 && (
              <View className='kb-spacing-sm-t kb-tag__group'>
                <Text className='kb-color__grey'>重量：</Text>
                <Text>{item.item_weight}</Text>
              </View>
            )}
          </View>
        </View>
      ))}
    </View>
  ) : null;
};

Index.defaultProps = {
  data: {},
};
Index.options = { addGlobalClass: true };

export default Index;
