/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useMemo } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { View, Text } from '@tarojs/components';
import { mergeBySpace } from '@base/utils/utils';
import { setClipboardData } from '@/utils/qy';
import { combineData, isEncryptionData } from '@/components/_pages/order/_utils/order.detail';
import isEmpty from 'lodash/isEmpty';
import './index.scss';

const Index = (props) => {
  let { data } = props;
  const { change_address = {} } = data || {};
  data = {
    ...data,
    ...change_address,
  };

  const addressList = useMemo(() => {
    const arr = [
      {
        key: 'send',
        label: '寄件信息',
      },
      {
        key: 'receive',
        label: '收件信息',
      },
    ];
    // 取件地址
    if (!isEmpty(change_address)) {
      arr.unshift({
        key: 'pickup',
        label: '取件信息',
      });
    }
    return arr;
  }, [change_address]);

  const handleCopy = (e, text, type) => {
    e.stopPropagation();
    setClipboardData(text, `${type}已复制`);
  };

  return (
    <Fragment>
      {addressList.map((item) => {
        const mobile = mergeBySpace(data[`${item.key}_mobile`], data[`${item.key}_tel`]);

        const addressInfo = {
          [`${item.key}_province`]: data[`${item.key}_province`],
          [`${item.key}_city`]: data[`${item.key}_city`],
          [`${item.key}_county`]: data[`${item.key}_county`],
          [`${item.key}_address`]: data[`${item.key}_address`],
          [`${item.key}_name`]: data[`${item.key}_name`],
          mobile,
        };
        const { userInfo: { is_admin } = {} } = Taro.kbLoginData || {};
        return (
          <View key={item.key} className=''>
            <View className='kb-detail-wrapper__line at-row at-row__align--start'>
              <View className='kb-detail-wrapper__line--label kb-color__grey'>{item.label}</View>
              <View className='kb-detail-wrapper__line--value at-row at-row__align--center at-row__justify--between'>
                <View>
                  {data.change_shipper_address_order == 1 &&
                    item.key === 'send' &&
                    is_admin == 1 && (
                      <Text className='kb-spacing-xs-lr kb-margin-md-r kb-size__sm kb-background__green kb-color__green'>
                        已替换
                      </Text>
                    )}
                  <Text className='kb-margin-xs-r'>{data[`${item.key}_name`]}</Text>
                  <Text className='kb-margin-xs-r'>{mobile}</Text>
                  {data[`${item.key}_province`]}
                  {data[`${item.key}_city`]}
                  {data[`${item.key}_county`]}
                  {data[`${item.key}_address`]}
                </View>
                <View>
                  {!isEncryptionData(addressInfo) && (
                    <View
                      hoverClass='kb-hover-opacity'
                      className='kb-margin-xs-b kb-color__brand'
                      onClick={(e) => handleCopy(e, combineData(addressInfo), item.label)}
                    >
                      <AtIcon
                        prefixClass='kb-icon'
                        value='copy-text'
                        className='kb-icon-size__md kb-color__grey kb-spacing-sm-l'
                      />
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        );
      })}
    </Fragment>
  );
};

Index.defaultProps = {
  data: {},
  border: false,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
