/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-payInfo {
  border-radius: $border-radius-xxl;

  &__title {
    margin-bottom: $spacing-v-lg;
    line-height: 46px;
    .at-row {
      width: auto;
    }
  }

  &__tips {
    margin-bottom: $spacing-v-md;
    padding: 16px;
    color: #ed6a0c;
    font-size: 20px;
    background: #fffbe8;
    border-radius: 4px;
  }

  &__checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: $border-radius-circle;
  }

  &__pay-status {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__priceSource {
    color: #ffb5b5;
  }
}

.kb-background-color {
  &__red {
    background-color: $color-red-1;
  }

  &__yellow {
    background-color: #ffc34a;
  }

  &__green {
    background-color: #3cdb7c;
  }
}

// .kb-direction {
//   &__down {
//     transform: rotate(0);
//   }

//   &__up {
//     transform: rotate(-180deg);
//   }
// }

.kb-color__click {
  color: #576b95;
}

.kb-check__relate {
  position: relative;
  &::before {
    position: absolute;
    top: 50%;
    width: 80%;
    border-bottom: $width-base solid #ebedf0;
    transform: translate(-120%, -50%);
    content: '';
  }
  &::after {
    position: absolute;
    top: 50%;
    width: 80%;
    border-bottom: $width-base solid #ebedf0;
    transform: translate(16%, -50%);
    content: '';
  }
}

.kb-fee-table {
  margin-bottom: $spacing-h-lg;
  overflow: hidden;
  border: $width-base solid rgb(235, 237, 240);
  border-radius: $border-radius-xxl;
  &__row {
    font-size: $font-size-base;
    &--grey {
      background-color: #f7f8fa;
    }
    &--col {
      padding: 20px 32px;
    }
  }
  .border-l {
    border-left: $width-base solid #dcdee0;
  }
}
