/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getCurrentUser, noop } from '@base/utils/utils';
import { Block, Text, View } from '@tarojs/components';
import Taro, { Fragment, useMemo, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import numeral from 'numeral';
import { AtIcon } from 'taro-ui';
import { formatNumeral } from '../../_utils/order.detail';
import { serviceIntroductionMap } from '../../_utils/order.edit.dh';
import './index.scss';
import PayInfoValDiff from './payInfoValDiff';

const Index = (props) => {
  const { data = {}, statusInfos, isAdmin } = props;
  const {
    pay_status_info = {},
    pay_method,
    settlement_price_details = [],
    f_kg,
    pay_status,
    pay_type,
    order_fee_first = {},
    freightBase,
    brand,
    arrive_pay,
    brand_type,
  } = data;
  // console.log('初次结算费用order_fee_first', order_fee_first)
  const { order_status } = statusInfos || {};
  const isCustomF = getCurrentUser('custom-f');

  const [spreadStatus, setSpreadStatus] = useState(false);

  const handleNavigator = (key) => {
    Taro.navigateToDocument(key);
  };
  const toPackingFee = () => {
    Taro.navigator({
      url: 'https://m.kuaidihelp.com/f/packingFee',
      target: 'webview',
    });
  };

  // 支付分，月结
  const onlinePay = ['1', '2'].includes(`${pay_method}`);
  const showPageInfo = !!(onlinePay && order_status != 0);
  const showPayStatusInfo = !!(pay_status_info.key != 0);

  const isShowPage = showPageInfo || showPayStatusInfo;

  const onNavigator = () => {
    const { waybill, order_number, brand } = data;
    Taro.navigator({
      url: 'pages/user/account/record/index',
      options: {
        brand,
        order_id: order_number,
        waybill,
        order_mark: data.order_mark,
      },
    });
  };

  const isUserSend =
    !isCustomF && data.is_admin == 1 && (data.pay_method == 2 || data.pay_method == 1);

  const priceColumns = useMemo(() => {
    const showReferencePrice = isAdmin && pay_status != 2;

    // https://tower.im/teams/258300/todos/110206/
    let settlement_volume = data.settlement_volume || '';
    let settlement_volume_unit = 'cm³';
    if (settlement_volume > 100000) {
      settlement_volume = numeral(Math.ceil(`${(settlement_volume / 1000000) * 100}`) / 100).format(
        '0.00',
      );
      settlement_volume_unit = 'm³';
    }

    return [
      {
        title: '已支付',
        key: 'pay_price',
        value: formatNumeral(data.pay_price, '', '--'),
        visible: !!data.pay_price,
        payType: {
          icon: pay_type == 'weixin' ? 'wx_pay' : pay_type == 'offline' ? 'cash' : 'wxpayflag',
          className: pay_type == 'offline' ? 'kb-color__orange' : 'kb-color__green',
        },
      },
      {
        title: showReferencePrice ? '参考收费价格' : '总费用',
        key: 'wait_pay_price',
        value: data.wait_pay_price || '',
        visible: !!data.wait_pay_price,
      },
      {
        title: '结算重量',
        key: 'charging_weight',
        value: data.charging_weight || '',
        visible: !!data.charging_weight && spreadStatus,
        unit: 'kg',
      },
      {
        title: '结算体积',
        key: 'settlement_volume',
        value: settlement_volume || '',
        visible: !!data.settlement_volume && spreadStatus,
        unit: settlement_volume_unit,
      },
      {
        title: '收益',
        key: 'regiment_profit',
        value: isUserSend ? '0.00' : data.regiment_profit || '',
        visible: (!!data.regiment_profit && isAdmin && onlinePay && spreadStatus) || isUserSend,
        ...(isUserSend
          ? {
              payType: {
                icon: 'question',
                className: 'kb-color__grey',
              },
            }
          : {}),
      },
      {
        title: data.welfare_type,
        key: 'welfare_money',
        value: data.welfare_money || '',
        visible: !!data.welfare_money,
      },
    ].filter((item) => !!item.visible);
  }, [data, isAdmin, onlinePay, pay_status, pay_type, spreadStatus, isUserSend]);

  const showFreightBaseTips = () => {
    Taro.kbToast({
      text: `您的订单发生了产品类型的变更，无法享受优惠价格，具体变更信息请咨询揽收快递员`,
    });
  };

  const handleFreightClick = () => {
    if (freightBase == 'fee') {
      showFreightBaseTips();
    } else {
      Taro.kbToast({
        text: `首重${data.f_fee}元/${f_kg > 1 ? f_kg : ''}KG，续重${data.s_fee}元/KG`,
      });
    }
  };

  const handleClickBar = (item) => {
    if (isUserSend) {
      Taro.kbToast({
        text: `自主寄件订单，不返佣金`,
      });
      return;
    }
    if (item.key === 'pay_price' && freightBase == 'fee') {
      showFreightBaseTips();
    }
  };

  const handleServiceClick = ({ value }) => {
    Taro.navigator({
      url: 'order/edit/service/dh/desc',
      options: {
        type: value,
        brand,
      },
    });
  };

  //待补款
  const showDiff = order_fee_first && !isEmpty(order_fee_first);
  return (
    <Block>
      {isShowPage ? (
        <View className='kb-payInfo kb-margin-lg kb-spacing-lg kb-background__white'>
          <View className='kb-payInfo__title'>
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View className='at-row at-row__align--center'>
                <Text className='kb-size__lg kb-size__bold'>费用信息</Text>
                <View onClick={() => handleNavigator('price_rule')} hoverClass='kb-hover-opacity'>
                  <Text className='kb-size__base kb-color__grey kb-margin-md-l'>
                    按{data.calculate_price_type == 2 ? '体积' : '重量'}计费
                  </Text>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-icon-size__xs kb-color__grey kb-margin-sm-l'
                  />
                </View>
              </View>
              {showPageInfo && (
                <View className='kb-payInfo__pay-status'>
                  <View
                    className={classNames([
                      'kb-payInfo__checkbox',
                      `kb-color__${pay_status_info.color}`,
                      `kb-background-color__${pay_status_info.color}`,
                    ])}
                  >
                    <AtIcon
                      prefixClass='kb-icon'
                      value='right'
                      className='kb-icon-size__xs kb-color__white'
                    />
                  </View>
                  <Text className='kb-size__md kb-spacing-sm-lr'>{pay_status_info.text}</Text>
                </View>
              )}
            </View>
          </View>

          {brand_type === 'big_package' && arrive_pay == 0 && (
            <View className='kb-payInfo__tips'>
              运费与增值服务合计的实际费用，将优先微信支付分扣款请留意微信扣款消息。若金额大于500元，会分成多个账单扣除相应运费。
            </View>
          )}

          <View className='kb-fee-table'>
            {priceColumns.map((item, index) => {
              const { key, title, value, payType, unit } = item;
              return (
                <View
                  key={item.key}
                  className={classNames(
                    'kb-fee-table__row at-row at-row__align--center at-row__justify--between',
                    {
                      'kb-fee-table__row--grey': index % 2 != 1,
                    },
                  )}
                >
                  <View className='at-col at-col-6'>
                    <View
                      className='kb-fee-table__row--col'
                      onClick={() => handleClickBar(item)}
                      hoverClass='kb-hover'
                    >
                      {title}
                      {payType && (
                        <AtIcon
                          prefixClass='kb-icon'
                          value={payType.icon}
                          className={`kb-icon-size__sm ${payType.className}`}
                        />
                      )}
                      {key == 'pay_price' && freightBase === 'fee' && (
                        <AtIcon
                          prefixClass='kb-icon'
                          value='info-circle'
                          className='kb-icon-size__base kb-color__red'
                        />
                      )}
                    </View>
                  </View>
                  <View className='at-col at-col-6 border-l'>
                    <View className='at-row'>
                      <View className='kb-color__grey kb-spacing-md-l'>
                        {!unit && <Text className='kb-size__sm kb-margin-xs-r'>¥</Text>}
                        <Text className='kb-size__lg'>
                          {value}
                          {unit || ''}
                        </Text>
                        {order_status == 0 && key == 'pay_price' && onlinePay && (
                          <Text className='kb-payInfo__priceSource'>资金已原路退回</Text>
                        )}
                      </View>
                      {key != 'pay_price' && showDiff ? (
                        <View>
                          <PayInfoValDiff
                            sKey={key}
                            data={data}
                            data_first={order_fee_first}
                            showDiffVal={key == 'wait_pay_price' || key == 'regiment_profit'}
                            unit={unit || '元'}
                          />
                        </View>
                      ) : null}
                    </View>
                  </View>
                </View>
              );
            })}
          </View>

          {showPayStatusInfo && spreadStatus && (
            <View className='kb-fee-table'>
              <View className='kb-fee-table__row at-row at-row__align--center at-row__justify--between kb-fee-table__row--grey'>
                <View className='at-col at-col-6'>
                  <View
                    className='kb-fee-table__row--col'
                    hoverClass='kb-hover'
                    onClick={onlinePay ? handleFreightClick : noop}
                  >
                    运费
                    {onlinePay && (
                      <AtIcon
                        prefixClass='kb-icon'
                        value='info-circle'
                        className='kb-icon-size__sm kb-color__grey'
                      />
                    )}
                  </View>
                </View>
                <View className='at-col at-col-6 border-l'>
                  <View className='kb-spacing-md-l kb-color__grey'>
                    <Text className='kb-size__sm kb-margin-xs-r'>¥</Text>
                    <Text className='kb-size__lg'>{data.freight}</Text>
                    {showDiff && (
                      <Fragment>
                        <PayInfoValDiff
                          sKey='freight'
                          data={data}
                          data_first={order_fee_first}
                          unit='元'
                        />
                      </Fragment>
                    )}
                  </View>
                </View>
              </View>
              {settlement_price_details.map((val, index) => {
                const oSettlement_price_details = {};
                if (settlement_price_details && settlement_price_details.length > 0) {
                  settlement_price_details.map((i) => {
                    oSettlement_price_details[i.name] = i.fee;
                  });
                }
                const oSettlement_price_details_first = {};
                const { settlement_price_details: settlement_price_details_first } =
                  order_fee_first || {};
                if (settlement_price_details_first && settlement_price_details_first.length > 0) {
                  settlement_price_details_first.map((i) => {
                    oSettlement_price_details_first[i.name] = i.fee;
                  });
                }
                const showQuestion = serviceIntroductionMap.find(
                  (i) => i.label.includes(val.name) || val.name.includes(i.label),
                );
                return (
                  <View
                    key={val.id}
                    className={classNames(
                      'kb-fee-table__row at-row at-row__align--center at-row__justify--between',
                      {
                        'kb-fee-table__row--grey': index % 2 == 1,
                      },
                    )}
                  >
                    <View className='at-col at-col-6'>
                      <View className='kb-fee-table__row--col'>
                        {val.name}
                        {showQuestion && (
                          <AtIcon
                            prefixClass='kb-icon'
                            value='question'
                            className='kb-icon-size__sm kb-color__grey'
                            hoverClass='kb-hover'
                            onClick={handleServiceClick.bind(this, showQuestion)}
                          />
                        )}
                      </View>
                    </View>
                    <View className='at-col at-col-6 border-l'>
                      <View className='kb-spacing-md-l kb-color__grey'>
                        {val.showPackageFee && (
                          <Text
                            className='kb-size__sm kb-spacing-sm-r kb-color__click'
                            onClick={toPackingFee}
                          >
                            收费标准 |
                          </Text>
                        )}
                        <Text className='kb-size__sm kb-margin-xs-r'>¥</Text>
                        <Text className='kb-size__lg'>{val.fee}</Text>
                        {showDiff && (
                          <Fragment>
                            <PayInfoValDiff
                              sKey={val.name}
                              data={oSettlement_price_details}
                              data_first={oSettlement_price_details_first}
                              unit='元'
                            />
                          </Fragment>
                        )}
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          {showPayStatusInfo && (
            <View
              className={classNames([
                'at-row ',
                'at-row__justify--center',
                'at-row__align--center',
                'kb-size__base',
                'kb-color__greyer',
                'kb-spacing-sm-tb',
              ])}
              onClick={setSpreadStatus.bind(null, !spreadStatus)}
            >
              <Text className='kb-spacing-md-r'>{spreadStatus ? '收起' : '展开'}</Text>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className={classNames([
                  'kb-size__sm ',
                  `kb-icon__direction-${spreadStatus ? 'up' : 'down'}`,
                ])}
              />
            </View>
          )}
        </View>
      ) : null}
      {isAdmin && data.waybill && (
        <View
          className='at-row at-row__align--center at-row__justify--center kb-color__greyer kb-size__sm'
          onClick={onNavigator}
        >
          <View
            hoverClass='kb-hover-opacity'
            className='kb-check__relate kb-spacing-md kb-color__click'
          >
            查看此单的关联交易
          </View>
        </View>
      )}
    </Block>
  );
};

Index.defaultProps = {
  data: {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
