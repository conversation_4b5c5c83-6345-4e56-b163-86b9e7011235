

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import { AtIcon, AtTag } from 'taro-ui';
import { Text } from '@tarojs/components';

const Index = (props) => {
  const { data = {} } = props;

  return (
    data.status && (
      <AtTag active type='primary' className={`kb-tag__flip kb-tag__flip-${data.color}`}>
        <AtIcon prefixClass='kb-icon' value='person-right' className='kb-icon-size__base' />
        <Text className='kb-icon__text--ml'>{data.status}</Text>
      </AtTag>
    )
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {},
};

export default Index;
