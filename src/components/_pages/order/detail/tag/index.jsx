/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtTag } from 'taro-ui';
import KbRealName from './realname';
import KbCountDownClock from '@base/components/count-down-clock';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  return (
    <View className='kb-order-detail__tag'>
      {data.order_state == 'wait_pay' && data.waitPayEndTime && data.currentTime && (
        <AtTag active type='primary' className='kb-tag__flip kb-tag__flip-orange' size='small'>
          <Text>剩余：</Text>
          <KbCountDownClock start={data.currentTime * 1000} end={data.waitPayEndTime * 1000} />
        </AtTag>
      )}
      {data.order_finish_code && (
        <AtTag active type='primary' className='kb-tag__flip kb-tag__flip-orange' size='small'>
          <Text className='kb-color__red'>收货码：{data.order_finish_code}</Text>
        </AtTag>
      )}
      {data.help_status && data.help_status.coupon_type == '4' && (
        <View className='kb-order-detail__tag-coupon'>退货券减免</View>
      )}
      {data.realnameInfo && <KbRealName data={data.realnameInfo} />}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: { realnameInfo: {} },
};

export default Index;
