

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import { View } from '@tarojs/components';
import { AtAvatar, AtTag } from 'taro-ui';
import './door.scss';

const Index = (props) => {
  const { data } = props;
  return data.platform === 'yjkd' ? (
    <View className='kb-box kb-yj-info__door'>
      <View>
        <AtAvatar circle image='https://cdn-img.kuaidihelp.com/brand_logo/icon_yjkd.png?t=20230316' />
      </View>
      <View className='kb-spacing-md-l'>
        <View>智能匹配优质快递品牌为您服务</View>
        <View className='kb-spacing-sm-t kb-tag__group'>
          <AtTag type='primary' className='kb-tag__orange' size='small' active>
            准时上门
          </AtTag>
          <AtTag type='primary' className='kb-tag__orange' size='small' active>
            服务优质
          </AtTag>
        </View>
      </View>
    </View>
  ) : null;
};

Index.defaultProps = {
  data: {},
};
Index.options = { addGlobalClass: true };

export default Index;
