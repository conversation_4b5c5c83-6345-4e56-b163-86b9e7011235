/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtAvatar, AtButton } from 'taro-ui';
import KbPremium from './premium';
import KbNoticeBar from '@base/components/notice-bar';
import KbButton from '@base/components/button';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const {
    order_id,
    source,
    platform,
    isReadonly,
    brand,
    commission,
    pay_status,
    goods_weight,
    help_status,
    applet,
  } = data;
  const show = !isReadonly && platform === 'yjkd';
  // 保险费
  const isPremium = source === 'premium';
  // 优寄线上付
  const isYjkdOnlinePay = source === 'kbyj';

  // 立即支付
  const handlePay = () => {
    Taro.navigator({
      url: 'order/pay',
      data: {
        order_id,
      },
    });
  };

  return (
    show && (
      <View className='kb-box kb-yj-info'>
        {isPremium && <KbPremium data={data} />}
        {isYjkdOnlinePay && (
          <Fragment>
            {((brand === 'jt' && pay_status === 'waitpay') || commission) && (
              <View>
                <KbNoticeBar>
                  <View className='at-row at-row__align--center'>
                    <AtAvatar
                      circle
                      size='small'
                      image='https://cdn-img.kuaidihelp.com/brand_logo/icon_yjkd.png?t=20230316'
                    />
                    <View className='kb-spacing-sm-l'>
                      {commission ? (
                        <Text>恭喜本单获得{commission.rate * 100}%运费佣金</Text>
                      ) : (
                        <Text>为了不影响您的寄件体验，请尽快完成支付</Text>
                      )}
                    </View>
                  </View>
                </KbNoticeBar>
              </View>
            )}
            {goods_weight && (
              <View className='kb-yj-info__pay'>
                <View className='at-col'>
                  {applet.need_pay > 0 && (
                    <View className='kb-spacing-sm-b'>
                      <Text>运费合计：</Text>
                      {applet.express_fee > 0 && help_status.fee > 0 && (
                        <Text>
                          运费￥{applet.express_fee} - 优惠￥{help_status.fee} =
                        </Text>
                      )}
                      <Text className='kb-color__red'>￥{applet.need_pay}</Text>
                    </View>
                  )}
                  <View>
                    <Text>实际重量：</Text>
                    <Text>{goods_weight}KG</Text>
                  </View>
                  {commission && applet.need_pay && (
                    <View className='kb-spacing-sm-t'>
                      <Text>佣金返现：</Text>
                      <Text>
                        {applet.need_pay} * {commission.rate * 100}% =
                      </Text>
                      <Text className='kb-color__red'>￥{commission.amount}</Text>
                    </View>
                  )}
                </View>
                {brand === 'jt' && pay_status === 'waitpay' ? (
                  <View>
                    <AtButton type='primary' size='small' circle onClick={handlePay}>
                      立即支付
                    </AtButton>
                  </View>
                ) : commission ? (
                  <View>
                    <KbButton
                      size='small'
                      circle
                      type='primary'
                      openType='share'
                      page='welfare.commission'
                      info={{ action: 'join' }}
                    >
                      邀好友赚佣金
                    </KbButton>
                  </View>
                ) : null}
              </View>
            )}
          </Fragment>
        )}
      </View>
    )
  );
};

Index.defaultProps = {
  data: {},
};
Index.options = { addGlobalClass: true };

export default Index;
