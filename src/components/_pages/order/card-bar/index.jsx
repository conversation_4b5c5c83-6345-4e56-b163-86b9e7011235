/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef, useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useDidShowCom } from '@base/hooks/page';
import { createListener, noop } from '@base/utils/utils';
import { cardPayTips, getAvailableCardList } from './sdk';
import './index.scss';

const Index = (props) => {
  const { data, action } = props;
  const [canUse, updateCanUse] = useState(false);
  const [current, updateCurrent] = useState('');
  const self_ref = useRef({
    canUse,
  });
  const triggerUpdataCanUse = (param) => {
    self_ref.current.canUse = param;
    updateCanUse(param);
  };

  const triggerCheck = () => {
    const { canUse } = self_ref.current;
    if (canUse) return;
    checkCanUseCard();
  };
  // 选择模式，触发默认选择或手动选择回调
  const triggerChange = (data) => {
    if (action !== 'select') return;
    const { card_id, record_id } = data || {};
    let cardId = record_id || card_id;
    updateCurrent(cardId);
    props.onChange({
      card_id: cardId,
      desc: cardId ? cardPayTips : '',
    });
  };

  // 验证是否可选择权益次卡
  const checkCanUseCard = () => {
    // 如果购买页支付成功后，将锁死可用状态
    if (action === 'select') {
      const cardPaid = !!Taro.kbGetGlobalDataOnce('cardPaid');
      triggerUpdataCanUse(cardPaid);
      if (cardPaid) {
        triggerChange();
      }
      return cardPaid;
    }
    return false;
  };

  useEffect(() => {
    if (data) {
      const { dakId: dak_id, courierId: courier_id } = data;
      if ((!dak_id && !courier_id) || checkCanUseCard()) return;
      // 权益次卡
      getAvailableCardList({
        dak_id,
        courier_id,
        type: 'discount_card',
        action,
      })
        .then((list) => {
          const [item] = list;
          const canUse = !!item;
          triggerUpdataCanUse(canUse);
          // 可用才触发
          triggerChange(canUse ? item : { card_id: null });
        })
        .catch((err) => console.log(err));
    }
  }, [data, action]);

  useDidShowCom(() => {
    triggerCheck();
  });

  // 点击选择
  const handleClick = () => {
    const { dakId: dak_id, courierId: courier_id } = data;
    if (action === 'select') {
      // 选用模式
      createListener('cardSelect', triggerChange);
    }
    Taro.navigator({
      url: 'order/card',
      options: {
        action,
        dak_id,
        courier_id,
        current,
        type: 'discount_card',
      },
    });
  };

  return (
    // 选择
    !canUse ? null : action === 'select' ? (
      <View
        className='kb-card-bar'
        hoverStopPropagation
        hoverClass='kb-hover-opacity'
        onClick={handleClick}
      >
        权益次卡
      </View>
    ) : (
      // 购买
      <View className='kb-card-bar__buy' onClick={handleClick} hoverClass='kb-hover-opacity'>
        购买权益次卡
      </View>
    )
  );
};

Index.defaultProps = {
  onChange: noop,
  action: 'select',
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
