/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useCallback, useEffect, useRef, useState, Fragment } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useDidShowCom } from '@base/hooks/page';
import { createListener, noop } from '@base/utils/utils';
import { cardPayTips, checkEquityCardList, getDefaultEquityCard } from './sdk';
import './index.wkd.scss';

const Index = (props) => {
  const { data, action, address } = props;
  const { receive_province, receive_city, receive_district } = address || {};
  const [canUse, updateCanUse] = useState(false);
  const [canBuy, updateCanBuy] = useState(false);
  const [current, updateCurrent] = useState('');

  const self_ref = useRef({
    canUse,
    action,
  });

  useEffect(() => {
    if (data) {
      const { account_phone: phone } = data;
      if (action == 'buy' && phone) {
        checkEquityCardList({
          phone,
        })
          .then((isHasEquity) => {
            updateCanBuy(isHasEquity);
          })
          .catch(() => {
            updateCanBuy(false);
          });
      } else {
        updateCanBuy(false);
      }
    }
  }, [data]);

  useEffect(() => {
    if (action == 'select') {
      checkCanUseCard();
    }
  }, [data, receive_province, receive_city, receive_district]);

  useDidShowCom(() => {
    const cardPaid = !!Taro.kbGetGlobalDataOnce('cardPaid');
    if (cardPaid) {
      checkCanUseCard();
    }
  });

  const triggerUpdateCanUse = (param) => {
    self_ref.current.canUse = param;
    updateCanUse(param);
  };

  // 选择模式，触发默认选择或手动选择回调
  const triggerChange = (data) => {
    const { action } = self_ref.current;
    if (action !== 'select') return;
    const { card_id } = data || {};
    let cardId = card_id;
    updateCurrent(cardId);
    props.onChange({
      canUse,
      card_id: cardId,
      desc: cardId ? cardPayTips : '',
    });
  };

  /**
   * @description 验证是否存在可用权益次卡
   */
  const checkCanUseCard = () => {
    const { action } = self_ref.current;
    if (action === 'select') {
      const { account_phone: phone } = data;
      const {
        receive_province: shipping_province,
        receive_city: shipping_city,
        receive_district: shipping_district,
      } = address || {};
      if (phone && shipping_province && shipping_city && shipping_district) {
        getDefaultEquityCard({
          phone,
          shipping_province,
          shipping_city,
          shipping_district,
        })
          .then((data) => {
            if (data && data.id) {
              triggerUpdateCanUse(true);
              triggerChange({ card_id: data.id });
              return true;
            } else {
              triggerUpdateCanUse(false);
              triggerChange({ card_id: null });
            }
          })
          .catch(() => {
            triggerUpdateCanUse(false);
            triggerChange({ card_id: null });
          });
      } else {
        triggerUpdateCanUse(false);
        triggerChange({ card_id: null });
      }
    }
    return false;
  };

  // 点击选择
  const handleClick = useCallback(() => {
    const { account_phone: phone } = data;
    if (action === 'select') {
      // 选用模式
      createListener('cardSelect', triggerChange);
    }
    Taro.navigator({
      url: 'order/card',
      options: {
        action,
        phone,
        current,
        type: 'discount_card',
      },
    });
  }, [current, data, action]);

  return (
    <Fragment>
      {
        // 选择
        action === 'select' ? (
          !canUse ? null : (
            <View
              className='kb-card-bar'
              hoverStopPropagation
              hoverClass='kb-hover-opacity'
              onClick={handleClick}
            >
              权益次卡
            </View>
          )
        ) : (
          action === 'buy' &&
          // 购买
          (canBuy ? (
            <View className='kb-card-bar__buy' onClick={handleClick} hoverClass='kb-hover-opacity'>
              购买权益次卡
            </View>
          ) : null)
        )
      }
    </Fragment>
  );
};

Index.defaultProps = {
  onChange: noop,
  action: 'select',
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
