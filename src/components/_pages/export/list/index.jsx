/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View, Text, ScrollView } from '@tarojs/components';
import KbLongList from '@base/components/long-list';
import isArray from 'lodash/isArray';
import { dateCalendar, noop } from '@base/utils/utils';
import classNames from 'classnames';
import './index.scss';

const Index = ({ onDownLoad, onReady }) => {
  const [list, setList] = useState([]);
  const [currentListId, setCurrentListId] = useState('');

  const listData = {
    api: {
      url: '/api/Bill/exportList',
      formatResponse: (res) => {
        const { data: list } = res || {};
        if (isArray(list) && list.length > 0) {
          return {
            data: { list },
          };
        } else {
          return {
            data: void 0,
          };
        }
      },
      onThen: (list) => {
        setList(list);
      },
    },
  };

  const handleReady = (ins) => {
    onReady(ins);
  };

  const handleDownload = (status, filePath) => {
    if (status == 2 && filePath) {
      onDownLoad(filePath);
    }
  };

  const onItemClick = (id) => {
    if (id == currentListId) {
      setCurrentListId('');
    } else {
      setCurrentListId(id);
    }
  };

  return (
    <View className='kb-export-list'>
      <View className='kb-margin-md-t kb-spacing-xl kb-size__17 kb-border-b kb-background__white'>
        对账单记录
      </View>
      <ScrollView scrollY className='kb-scrollview'>
        <KbLongList
          className='kb-background__white'
          onReady={handleReady}
          data={listData}
          enableMore={false}
          enableRefresh
        >
          <View className='kb-list'>
            {list.map((item) => {
              return (
                <View key={item.id} className='kb-list__item--wrapper' hoverClass='kb-hover'>
                  <View className='kb-list__item kb-spacing-xl'>
                    <View className='at-row at-row__align--center'>
                      <View className='at-col at-col-3'>
                        <View>{dateCalendar(item.create_time)}</View>
                      </View>
                      <View
                        className={classNames('at-col kb-margin-md-l', {
                          'kb-list__item--fileTitle': currentListId != item.id,
                        })}
                        onClick={onItemClick.bind(null, item.id)}
                      >
                        {item.file_name}
                      </View>
                      <View
                        className='at-col at-col-2 kb-text__right'
                        hoverClass='kb-hover-opacity'
                        onClick={handleDownload.bind(null, item.status, item.bill_file)}
                      >
                        {item.status == 1 ? (
                          <Text className='kb-color__grey-3'>生成中...</Text>
                        ) : item.status == 2 ? (
                          <Text className='kb-color__brand'>下载</Text>
                        ) : (
                          <Text>生成失败</Text>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        </KbLongList>
      </ScrollView>
    </View>
  );
};

Index.defaultProps = {
  onDownLoad: noop,
  onReady: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
