/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbErrorModal from '@/components/errorModal';
import KbButton from '@base/components/button';
import { setClipboardData } from '@/utils/qy';
import { downloadFile } from '../_utils';
import './index.scss';

const Index = ({ filePath, visible, onClose }) => {
  const handleConfirm = () => {
    downloadFile(filePath)
      .then(() => {
        onClose();
      })
      .catch((err) => {
        const { errorMessage, message = errorMessage, errMsg } = err;
        Taro.kbToast({ text: message || `文件下载失败：${errMsg}` });
        onClose();
      });
  };

  return (
    <KbErrorModal
      onClose={onClose}
      title={false}
      isOpened={visible}
      buttons={[]}
      renderFooter={
        <View className='kb-modal__footer at-row at-row__align--center at-row__justify--between kb-size__xl kb-border-t'>
          <View
            className='at-col at-col-6 kb-color__grey-3 kb-text__center kb-border-r kb-spacing-md'
            hoverClass='kb-hover'
            onClick={onClose}
          >
            取消
          </View>
          <View
            className='at-col at-col-6 kb-color__brand kb-text__center kb-spacing-md'
            hoverClass='kb-hover'
            onClick={handleConfirm}
          >
            直接下载
          </View>
        </View>
      }
    >
      <View className='kb-text__center kb-spacing-md'>
        <View className='kb-size__17 kb-spacing-md-t'>下载对账单</View>
        <View className='kb-size__lg kb-spacing-md-tb'>
          对账单已生成，格式为“Excel表格”，文件您可以点击【复制对账单下载链接】按钮将“下载链接”分享给好友，或在浏览器中打开下载对账单
        </View>
        <View>
          <KbButton
            circle
            onClick={() => {
              setClipboardData(filePath, '下载链接已复制');
            }}
          >
            复制对账单下载链接
          </KbButton>
        </View>
      </View>
    </KbErrorModal>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
