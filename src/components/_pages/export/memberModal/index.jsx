/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useRef } from '@tarojs/taro';
import KbFloatLayout from '@base/components/floatLayout';
import KbMemberList from '@/components/_pages/team/memberList';
import KbCheckboxAll from '@base/components/checkbox/all';
import { View } from '@tarojs/components';
import './index.scss';

const Index = ({ isOpened, onClose, onConfirm }) => {
  const [selected, setSelected] = useState([]);
  const [total, setTotal] = useState(0);
  const listRef = useRef([]);

  const onConfirmOrCancel = (type) => {
    onClose();
    if (type == 'cancel') return;
    onConfirm({
      list: selected,
      selectedAll: selected.length > 0 && selected.length == listRef.current.length,
    });
  };

  // 获取列表
  const onGetted = (list = []) => {
    listRef.current = list;
    setTotal(list.length);
  };

  const onItemClick = (item) => {
    const copySelected = [...selected];
    const index = copySelected.findIndex(({ id }) => id == item.id);
    if (index >= 0) {
      copySelected.splice(index, 1);
    } else {
      copySelected.push({ id: item.id, avatar_url: item.avatar_url });
    }
    setSelected(copySelected);
  };

  const onCheckboxChange = (checked) => {
    const allSelect = listRef.current
      .map(({ id, avatar_url } = {}) => ({ id, avatar_url }))
      .filter((v) => v);

    setSelected(checked ? allSelect : []);
  };

  const count = selected.length;

  return (
    <KbFloatLayout
      className='kb-member-list-layout'
      renderFooter={
        <KbCheckboxAll
          confirmText='确认选择'
          cancelText='取消选择'
          cancelHasDiabled={count == 0}
          onCancel={onConfirmOrCancel.bind(null, 'cancel')}
          onConfirm={onConfirmOrCancel.bind(null, 'confirm', selected)}
          total={total}
          count={count}
          onChange={onCheckboxChange}
        />
      }
      isOpened={isOpened}
      onClose={onClose}
      title={false}
    >
      <View className='kb-member-list kb-spacing-md-t'>
        <KbMemberList
          active
          selectMode='multiple'
          selected={selected}
          onGetted={onGetted}
          onItemClick={onItemClick}
        />
      </View>
    </KbFloatLayout>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
