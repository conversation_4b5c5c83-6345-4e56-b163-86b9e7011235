/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import Taro from '@tarojs/taro';

/**
 * 创建导出对账单任务
 *  */
export const createExportTask = (data) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Bill/createExport',
      data,
      toastError: true,
      toastSuccess: '创建成功',
    })
      .then((res) => {
        if (res.code == 0) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * 直接下载文件到本地
 *  */
export const downloadFile = (filePath) => {
  return new Promise((resolve, reject) => {
    const toastIns = Taro.kbToast({ status: 'loading' });
    Taro.downloadFile({
      url: filePath,
    })
      .then(({ tempFilePath }) => {
        toastIns.close();
        Taro.openDocument({
          filePath: tempFilePath,
          fileType: 'xlsx',
          success: () => {
            resolve();
          },
          fail: (err) => {
            console.log('openDocument_err', err);
          },
        });
      })
      .catch((err) => {
        toastIns.close();
        reject(err);
      });
  });
};
