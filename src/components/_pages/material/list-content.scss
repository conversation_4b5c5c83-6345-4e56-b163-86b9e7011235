/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-material {
  &__item {
    margin-bottom: $spacing-h-md;
    padding: 36px 28px;
    background-color: $color-white;
    border-radius: 24px;
    &-avatar {
      width: 72px;
      height: 72px;
    }
    .kb-shortLink {
      box-sizing: border-box;
      width: 100%;
      height: 64px;
      margin-bottom: $spacing-h-md;
      padding: 0 $spacing-h-md;
      color: $color-grey-1;
      font-size: $font-size-base;
      line-height: 64px;
      background-color: #f7f8fa;
      border-radius: 32px;
    }
    &__poster {
      flex-wrap: wrap;
      &-item {
        position: relative;
        width: 184px;
        height: 184px;
        margin-right: $spacing-h-sm;
        margin-bottom: $spacing-h-sm;
        overflow: hidden;
        border-radius: $border-radius-md;
        &__big {
          width: 375px !important;
          height: 568px !important;
        }
        &__wrapper {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100%;
          transform: translate(-50%, -50%);
        }
      }
      &-img {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 1;
        transform: translate(-50%, -50%);
        // width: 100%;
        // height: 100%;
      }
      &-qrCode {
        position: absolute;
        z-index: 2;
      }
    }
    &__content {
      white-space: pre-wrap;
    }
  }
  &__mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0);
    opacity: 0;
    transition: all 0.17s;
    &__show {
      z-index: 1000;
      opacity: 1;
    }
    &-wrapper {
      height: 100%;
    }
    &-content {
      position: relative;
      margin: 0 auto;
      // position: absolute;
      // top: 50%;
      // left: 50%;
      // transform: translate(-50%, -50%);
    }
    &-body {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100%;
    }
    &-img {
      width: 100%;
      height: 100%;
      // &__widthFix {
      //   width: auto !important;
      //   height: 30vh !important;
      // }
      // &__heightFix {
      //   width: 90vw !important;
      //   height: auto !important;
      //   margin: 0 auto;
      // }
    }
    &-qrCode {
      position: absolute;
      z-index: 2;
    }
  }
}

.kb-text__nowrap {
  white-space: nowrap;
}
