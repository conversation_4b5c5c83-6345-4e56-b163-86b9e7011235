/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '~base/components/long-list';
import { useRef, useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import ListContent from './list-content';

const List = (props) => {
  const { tabs, type, active, shortLink, qrCode, savePoster } = props;
  const [list, setList] = useState([]);
  const listIns = useRef({});
  const listData = {
    pageKey: 'page',
    api: {
      url: '/api/activity/PromotionActivity/getPromotionMaterialsLists',
      formatRequest: (req) => {
        return {
          ...req,
          type: type == 'all' ? '' : type,
        };
      },
      formatResponse: (res) => {
        const { data } = res;
        const { count: total, lists } = data || {};
        const hasList = Array.isArray(lists) && lists.length > 0;
        if (hasList) {
          return {
            data: {
              list: lists,
            },
            total,
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (data) => {
        setList(data);
      },
    },
  };

  const onReady = (ins) => {
    listIns.current[type] = ins;
  };

  return (
    <KbLongList active={active} data={listData} onReady={onReady} enableMore>
      <View className='kb-spacing-md'>
        {list.map((item) => (
          <ListContent
            key={item.id}
            tabs={tabs}
            data={item}
            shortLink={shortLink}
            qrCode={qrCode}
            savePoster={savePoster}
          />
        ))}
      </View>
    </KbLongList>
  );
};

List.options = {
  addGlobalClass: true,
};

export default List;
