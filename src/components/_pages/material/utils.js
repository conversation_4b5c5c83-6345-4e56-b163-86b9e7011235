/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import dayjs from 'dayjs';
import request from '~base/utils/request';
import { getStorageSync, setStorage } from '~base/utils/utils';

const SHORT_LINK_KEY = 'shortLink';
export const getShortLink = ({ regiment_id, key, url }) => {
  return new Promise((resolve) => {
    const storageKey = `${SHORT_LINK_KEY}${key ? `-${key}` : ''}`;
    const { data = {}, ts } = getStorageSync(storageKey);
    const diff = dayjs().diff(dayjs(ts), 'day');
    if (data[regiment_id] && diff <= 28) {
      resolve(data[regiment_id]);
      return;
    }
    request({
      url: '/api/activity/PromotionActivity/promotionMaterialsShortLink',
      toastLoading: false,
      data: {
        page_url: url || `pages-0/pages/order/edit/index?id=${regiment_id}`,
      },
      onThen: (res) => {
        const { code, data } = res;
        if (code == 0) {
          setStorage({
            key: storageKey,
            data: {
              [regiment_id]: data,
            },
          });
          resolve(data);
        }
      },
    });
  });
};

export const getConfig = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/activity/PromotionActivity/promotionMaterialsCondition',
      toastLoading: false,
      onThen: (res) => {
        const { code, data } = res || {};
        if (code == 0) {
          const type = data.type || {};
          const tabs = Object.keys(type).map((key) => ({
            title: type[key].replace(/素材/g, ''),
            type: key,
          }));
          resolve([{ title: '全部', type: 'all' }, ...tabs]);
        }
      },
    });
  });
};

export const formatDate = (date) => {
  const now = dayjs();
  const targetDate = dayjs(date);
  const diffMinutes = now.diff(targetDate, 'minute');

  if (diffMinutes < 1) {
    return '刚刚';
  }

  if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  }

  const diffHours = now.diff(targetDate, 'hour');
  if (diffHours < 24) {
    return `${diffHours}小时前`;
  }
  return date;
};

export const removeTagsStr = (str) => {
  return str;
  let result = str.replace(/<\/p><p>/g, '\n');
  result = result.replace(/^<p>|<\/p>$/g, '');

  result = result.replace(/<[^>]*>/g, '');
  return result;
};

export const convertHttpToHttps = (url) => {
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }
  return url;
};
