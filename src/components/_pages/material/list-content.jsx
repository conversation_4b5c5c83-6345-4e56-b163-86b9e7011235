/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, ScrollView, View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import { setClipboardData } from '~/utils/qy';
import { useState } from '@tarojs/taro';
import classNames from 'classnames';
import './list-content.scss';
import { convertHttpToHttps, formatDate, removeTagsStr } from './utils';

// 计算图片样式
const calculateImageStyle = (width, height, ratio = 1) => ({
  width: width / ratio + 'px',
  height: height / ratio + 'px',
});

// 计算二维码样式
const calculateQRCodeStyle = (item, ratio = 1) => ({
  width: item.size / ratio + 'px',
  height: item.size / ratio + 'px',
  top: (item.coordinate ? item.coordinate[0][1] : 0) / ratio + 'px',
  left: (item.coordinate ? item.coordinate[0][0] : 0) / ratio + 'px',
});

const getRatio = (width, posterLength, isMask) => {
  if (width >= 750) {
    return isMask ? 3 : posterLength === 1 ? 4 : 6;
  } else if (width >= 375) {
    return isMask ? 2 : posterLength === 1 ? 3 : 4;
  }
  return 1;
};

const ListContent = (props) => {
  const {
    data,
    tabs,
    shortLink: shortLinkProps = {},
    qrCode: qrCodeProps = {},
    savePoster,
  } = props;
  const { type, updated_time, content } = data;
  const title = (tabs.find((item) => item.type == type) || {}).title;
  const date = formatDate(updated_time);
  const shortLink = type == 2 ? shortLinkProps.cps : shortLinkProps.base;
  const qrCode = type == 2 ? qrCodeProps.cps : qrCodeProps.user;
  const [preview, setPreview] = useState(null);
  let poster;
  try {
    poster = JSON.parse(data.poster || '');
  } catch (error) {
    poster = [];
  }

  const handleCopy = () => {
    const v = removeTagsStr(content) + '\n' + shortLink;
    setClipboardData(v);
  };

  const handleSave = () => {
    savePoster({
      ...data,
    });
  };

  return (
    <View className='kb-material__item'>
      <View className='kb-material__item--header at-row at-row__align--center at-row__justify--between'>
        <View className='at-row at-row__align--center'>
          <Image
            className='kb-material__item-avatar kb-spacing-md-r'
            src={`https://cdn-img.kuaidihelp.com/qj/miniapp/material/icon_${type}.png`}
          />
          <View>
            <View className='kb-size__lg'>{title}</View>
            <View className='at-row at-row__align--center'>
              <AtIcon
                value='clock2'
                prefixClass='kb-icon'
                className='kb-size__base kb-color__grey'
              />
              <View className='kb-size__sm kb-color__grey kb-text__nowrap'>{date}</View>
            </View>
          </View>
        </View>
        <View className='at-row at-row__align--center at-row__justify--end'>
          <AtButton
            className='kb-material__btn kb-color__grey'
            circle
            size='small'
            onClick={handleCopy}
          >
            复制文字
          </AtButton>
          <AtButton
            className='kb-margin-md-l kb-material__btn'
            circle
            type='secondary'
            size='small'
            onClick={() => handleSave()}
          >
            保存图文
          </AtButton>
        </View>
      </View>
      <View className='kb-spacing-md-tb'>
        <View className='kb-material__item__content'>{content}</View>
      </View>
      <View className='kb-shortLink'>{shortLink}</View>
      {poster && poster.length > 0 && (
        <View className='kb-material__item__poster at-row'>
          {poster.map((item) => {
            const ratio = getRatio(item.width, poster.length);
            const style = calculateImageStyle(item.width, item.height, ratio);
            const qrCodeStyle = calculateQRCodeStyle(item, ratio);
            return (
              <View
                key={item.url}
                className={classNames('kb-material__item__poster-item', {
                  'kb-material__item__poster-item__big': poster.length == 1,
                })}
                hoverClass='kb-hover-opacity'
                onClick={() => setPreview(item)}
              >
                <View className='kb-material__item__poster-item__wrapper' style={style}>
                  <Image
                    className='kb-material__item__poster-img'
                    src={convertHttpToHttps(item.url)}
                    lazyLoad
                    style={style}
                  />
                  {item.size > 0 && (
                    <Image
                      className='kb-material__item__poster-qrCode'
                      src={qrCode}
                      lazyLoad
                      style={qrCodeStyle}
                    />
                  )}
                </View>
              </View>
            );
          })}
        </View>
      )}
      <root-portal>
        <View
          className={classNames('kb-material__mask kb-hide__footer', {
            'kb-material__mask__show': !!preview,
          })}
          onClick={() => setPreview(null)}
        >
          {preview && (
            <ScrollView className='kb-material__mask-wrapper' scrollY>
              <View className='kb-material__mask-body'>
                <View
                  className='kb-material__mask-content'
                  style={calculateImageStyle(
                    preview.width,
                    preview.height,
                    getRatio(preview.width, 1, true),
                  )}
                >
                  <Image className='kb-material__mask-img' src={convertHttpToHttps(preview.url)} />
                  {preview && preview.size > 0 && (
                    <Image
                      className='kb-material__mask-qrCode'
                      src={qrCode}
                      style={calculateQRCodeStyle(preview, getRatio(preview.width, 1, true))}
                    />
                  )}
                </View>
              </View>
            </ScrollView>
          )}
        </View>
      </root-portal>
    </View>
  );
};

ListContent.options = {
  addGlobalClass: true,
};
export default ListContent;
