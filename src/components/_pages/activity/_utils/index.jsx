/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { getCurrentUser } from '@base/utils/utils';

export const reward_level = [
  {
    key: '1',
    tag: 'S级',
    title: 'S级任务奖励',
    desc: '需要已揽收订单数≥0单',
    label: 'S级奖励',
    tiaojian: '已揽收订单数为0-20单',
    jiamengReward: '每单（首重0.2元+续重0.1元/kg）',
    zhiyingReward: '京东订单运费1%/德邦订单运费1%',
  },
  {
    key: '2',
    tag: 'SS级',
    title: 'SS级任务奖励',
    desc: '需要已揽收订单数>20单',
    label: 'SS级奖励',
    tiaojian: '已揽收订单数为21-88单',
    jiamengReward: '每单（首重0.3元+续重0.2元/kg）',
    zhiyingReward: '京东订单运费2%/德邦订单运费1%',
  },
  {
    key: '3',
    tag: 'SSS级',
    title: 'SSS级任务奖励',
    desc: '需要已揽收订单数>88单',
    label: 'SSS级奖励',
    tiaojian: '已揽收订单数超过88单',
    jiamengReward: '每单（首重0.4元+续重0.2元/kg）',
    zhiyingReward: '京东订单运费3%/德邦订单运费1%',
  },
];

/**
 * 用户参与活动详情
 * @returns
 */
export const getUserActivityDetail = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/activity/RebateActivity/userActivityDetail',
      toastLoading: false,
      onThen: (res) => {
        resolve(res.code == 0 ? res.data : null);
      },
    });
  });
};

/**
 * 用户加入活动
 * @returns
 */
export const joinActivity = () => {
  return new Promise((resolve) => {
    request({
      url: '/api/activity/RebateActivity/joinActivity',
      toastError: true,
      onThen: (res) => {
        resolve(res.code == 0 && res.data && res.data.join_result ? true : false);
      },
    });
  });
};

/**
 * 返回需要展示的banner数据
 */
export const getActivityBanner = () => {
  return new Promise((resolve) => {
    if (getCurrentUser('qj-regiment')) {
      getUserActivityDetail().then((res) => {
        const { activity_stage } = res || {};
        if (activity_stage) {
          resolve({
            id: '83000',
            type: 'miniapp',
            platform: 'qj',
            position: '1',
            positionInfo: '首页轮播图',
            title: '活动轮播图',
            imgUrl: 'https://cdn-img.kuaidihelp.com/qj/miniapp/activity/banner.png',
            adUrl: 'pages-0/pages/activity/index',
            remark: '',
            number: '',
            status: '1',
            system: 'all',
            create_time: '2023-06-08 13:45:32',
            popupType: '1',
            template_ad_type: '1',
            weight: '0',
            custom_info: '2',
            jumpType: 'h5',
          });
        } else {
          resolve();
        }
      });
    } else {
      resolve();
    }
  });
};

/**
 * 判断是否参与活动
 * @param {*} opt
 * @param {string} opt.activity 活动类型
 * @returns
 */
export const checkIsParticipateActivity = (opt) => {
  const { activity } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/api/activity/Activity/checkIsParticipateActivity',
      data: {
        activity,
      },
      toastLoading: false,
      onThen: (res) => {
        resolve(res.code == 0 ? res.data : false);
      },
    });
  });
};

/**
 * 领取活动券
 * @param {*} opt
 * @returns
 */
export const addReceiveCouponService = (opt) => {
  const { activity, coupon_id } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/api/Coupon/addReceiveCoupon',
      data: {
        activity,
        coupon_id,
      },
      toastError: true,
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};

/**
 * 获取活动券配置信息
 * @param {*} opt
 * @returns
 */
export const getAllCouponConfigService = (opt) => {
  const { activity, coupon_id } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/api/Coupon/getAllCouponConfig',
      data: {
        activity,
        coupon_id,
      },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};

/**
 * 检查是否领取活动券
 * @param {*} opt
 * @returns
 */
export const checkReceiveCouponService = (opt) => {
  const { activity } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/api/activity/PromotionActivity/checkReceiveCoupon',
      data: {
        activity,
      },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};
