/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { Image, Fragment } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { useUpdate } from '@base/hooks/page';
import { getUserActivityDetail } from '@/components/_pages/activity/_utils';
import { setStorage, getStorage, getCurrentUser } from '@base/utils/utils';
import dayjs from 'dayjs';
import './index.scss';

const ActivityFloatDoor = () => {
  const [isOpened, setIsOpened] = useState(false);
  const [isOpenedCurtain, setIsOpenedCurtain] = useState(false);

  const recordCurtain = () => {
    setStorage({
      key: 'activity-curtain',
      data: dayjs().add(1, 'day').hour(7).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss'),
    });
  };

  const isLimitCurtain = () => {
    return new Promise((resolve) => {
      getStorage({
        key: 'activity-curtain',
        complete: (res) => {
          const { data } = res.data || {};
          const oDate = dayjs();
          const diff = data ? oDate.diff(dayjs(data), 'second', true) : 1;
          resolve(diff < 0);
        },
      });
    });
  };

  useUpdate((loginData) => {
    const { logined } = loginData;
    if (!logined) return;
    if (getCurrentUser('qj-regiment')) {
      getUserActivityDetail().then((res) => {
        const { activity_stage, reward_stage, is_join } = res || {};
        if (activity_stage || (reward_stage && is_join)) {
          setIsOpened(true);
        }
        if (activity_stage) {
          isLimitCurtain().then((isLimit) => {
            if (!isLimit) {
              setIsOpenedCurtain(true);
            }
          });
        }
      });
    }
  });

  const handleCurtainClose = () => {
    recordCurtain();
    setIsOpenedCurtain(false);
  };

  const handleCurtainClick = () => {
    recordCurtain();
    setIsOpenedCurtain(false);
    Taro.navigator({
      url: 'activity',
    });
  };

  const handleClick = (ev) => {
    ev.stopPropagation();
    Taro.navigator({
      url: 'activity',
    });
  };

  return (
    <Fragment>
      {isOpened ? (
        <Image
          className='activityFloatDoor'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/qj/miniapp/activity/float.png'
          onClick={handleClick}
          hoverClass='kb-hover'
        />
      ) : null}
      <AtCurtain isOpened={isOpenedCurtain} onClose={handleCurtainClose}>
        <Image
          onClick={handleCurtainClick}
          className='activityFloatDoor-curtain'
          src='https://cdn-img.kuaidihelp.com/qj/miniapp/activity/curtain.png?v=1'
        />
      </AtCurtain>
    </Fragment>
  );
};

ActivityFloatDoor.options = {
  addGlobalClass: true,
};

export default ActivityFloatDoor;
