/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useRef, useState, Fragment, useMemo } from '@tarojs/taro';
import KbLongList from '@base/components/long-list';
import { View } from '@tarojs/components';
import { extendMemo } from '@/components/_pages/_utils';
import { noop } from '~base/utils/utils';
import KbOrderListItem from './list-item';
import { formatResponseOrderList, getCpsOrderCount } from './_uitls';
import './list-content.scss';

const Index = (props) => {
  const { active: activeProps, searchData, tabKey, isAdmin, filterKey } = props;
  const actionRef = useRef({});
  const [list, updateList] = useState(null);

  const listData = {
    api: {
      url: '/api/lifeservice/Order/userOrderList',
      formatRequest: (req) => {
        const { page, start_time, end_time, brand, search } = req || {};
        return {
          page: page,
          start_date: start_time,
          end_date: end_time,
          search,
          order_type: brand,
          order_status: !tabKey || tabKey === 'all' ? '' : tabKey,
        };
      },
      formatResponse: formatResponseOrderList(),
      onThen: (data, _res, req) => {
        // console.log('req', req);
        updateList(data);
        getCpsOrderCount(req).then((count) => {
          props.onGetted(data, tabKey, { total: count });
        });
      },
    },
  };

  // 跳转订单详情
  const handleClickItem = () => {};

  // 列表准备就绪
  const handleReady = (ins) => {
    actionRef.current.listIns = ins;
    props.onReady(ins);
  };

  const active = useMemo(() => {
    return activeProps && searchData;
  }, [activeProps, searchData]);

  return (
    <Fragment>
      <KbLongList active={active} data={listData} enableMore onReady={handleReady}>
        {list && (
          <View className='kb-list'>
            {list.map((item) => {
              const { order_id } = item;
              return (
                <View className='kb-list-item--wrapper' key={order_id}>
                  <KbOrderListItem
                    tabKey={tabKey}
                    data={item}
                    onClick={handleClickItem}
                    isAdmin={isAdmin}
                    filterKey={filterKey}
                  />
                </View>
              );
            })}
          </View>
        )}
      </KbLongList>
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  searchData: null,
  active: false,
  onGetted: noop,
  onClickItem: noop,
  onReady: noop,
  onItemFooterClick: noop,
  selected: null,
  showToolBar: true,
};

export default extendMemo(Index);
