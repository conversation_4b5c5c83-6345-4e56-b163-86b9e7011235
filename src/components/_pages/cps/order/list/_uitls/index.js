/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import request from '~base/utils/request';
import { getCurrentUser } from '~base/utils/utils';

export const formatResponseOrderList = () => {
  return async (res) => {
    const { data: list } = res;

    const hasList = isArray(list) && list.length > 0;
    if (hasList) {
      return {
        data: {
          list,
        },
      };
    }
    return {
      data: void 0,
    };
  };
};

/**
 * 订单tab栏数据
 * @param {boolean} isAdmin 是否是管理员，只有管理员有待确认页
 *  */
export const createOrderTabs = () => {
  const YJQ = {
    title: '已结清',
    key: 'finished',
    max: 999,
  };
  const QB = {
    title: '全部',
    key: 'all',
    max: 999,
  };
  const DFY = {
    title: '待返佣',
    key: 'wait_rebate',
    max: 999,
  };
  const YQX = {
    title: '已取消',
    key: 'canceled',
    max: 999,
  };
  const YZF = {
    title: '已支付',
    key: 'paid',
    max: 999,
  };
  const tabs = getCurrentUser('regiment') ? [YJQ, QB, DFY, YQX] : [YZF, QB, YQX];
  return tabs;
};

// 获取订单数量
export function getCpsOrderCount(data) {
  return new Promise((resolve) => {
    request({
      url: '/api/lifeservice/Order/userOrderStatistic',
      data: data,
      toastLoading: false,
      onThen: (res) => {
        resolve(res.data.count || '0');
      },
    });
  });
}

export const updateTabList = (tabList = [], tabKey, res) => {
  const tabListArr = [...tabList];
  const total = res.total || 0;
  tabListArr.forEach((val) => {
    const title = val.title.split('(')[0];
    if (val.key == tabKey) {
      val.title = /[0-9]+/g.test(title) ? title.replace(/[0-9]+/g, total) : `${title}(${total})`;
    } else {
      val.title = title;
    }
  });

  return tabListArr;
};

export const order_type_map = {
  1: {
    name: '美团外卖',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_mt.png',
  },
  2: {
    name: '饿了么外卖',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_ele.png',
  },
  3: {
    name: '美团酒店',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_mt.png',
  },
  4: {
    name: '同程酒店',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_tc.png',
  },
  5: {
    name: '飞猪酒店',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_fz.png',
  },
  6: {
    name: '滴滴打车',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_dd.png',
  },
  7: {
    name: '花小猪打车',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_hxz.png',
  },
  8: {
    name: 'T3打车',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_t3.png',
  },
  9: {
    name: '同程打车',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_tc.png',
  },
  10: {
    name: '电影票',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_dyp.png',
  },
  99: {
    name: '其他',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/order_img_qt.png',
  },
};
