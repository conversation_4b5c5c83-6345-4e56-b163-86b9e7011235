/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-list {
  &-item {
    margin-bottom: $spacing-v-md;
    padding: $spacing-v-md $spacing-h-md;
    background: $color-white;
    border-radius: 24px;
    &__info {
      display: flex;
      justify-content: space-between;
      &__order {
        flex: 1;
        .logo {
          width: 102px;
          height: 24px;
        }
        .order-number {
          position: relative;
          margin-left: $spacing-h-sm;
          padding-left: $spacing-h-sm;
          font-size: 24px;
          &::before {
            position: absolute;
            top: 50%;
            left: 0;
            height: 26px;
            border-left: $width-base solid #e6e6e6;
            transform: translateY(-50%);
            content: '';
          }
        }
        .order {
          &-info {
            font-size: 24px;
          }
          &-name {
            width: 390px;
            margin-bottom: $spacing-h-md;
            overflow: hidden;
            font-size: 32px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          &-type {
            color: #b69169;
          }
        }
        .sp-img {
          width: 120px;
          height: 120px;
          margin-right: $spacing-h-md;
        }
      }
      .commission {
        .money {
          color: #646566;
        }
        .tag {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 82px;
          height: 36px;
          margin-top: $spacing-v-sm;
          color: #646566;
          font-size: 22px;
          background: #f7f7f7;
          border: $width-base solid #dcdee0;
          border-radius: 4px;
        }
        &-red {
          .money {
            color: #e34d59;
          }
          .tag {
            color: #e34d59;
            background: #fdf6f6;
            border-color: #e34d59;
          }
        }
        &-green {
          .money {
            color: #07c160;
          }
          .tag {
            color: #07c160;
            background: #f2fcf7;
            border-color: #07c160;
          }
        }
      }
    }
    &__user {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-top: $spacing-h-sm;
      color: #646566;
      font-size: 24rpx;
      .item {
        position: relative;
        box-sizing: border-box;
        padding-right: 10px;
        padding-left: 10px;
        &::after {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          border-right: $width-base solid #e6e6e6;
          content: '';
        }
        &:last-child::after {
          display: none;
        }
      }
      .source {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        &-tag {
          height: 26px;
          margin-left: 10px;
          padding: 0 5px;
          font-size: 20px;
          line-height: 26px;
          text-align: center;
          border: $width-base solid #969799;
          border-radius: 4px;
        }
      }
      .nickname {
        width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &__commission {
      height: 48px;
      margin-top: $spacing-h-sm;
      padding: 0 $spacing-h-md;
      color: #867b73;
      font-size: 24px;
      line-height: 48px;
      background: #fff5ec;
      border-radius: 8px;
      &__label {
        color: #47433f;
      }
    }
    .flex {
      display: flex;
      align-items: center;
    }
  }
}
