/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import { View, Text, ScrollView, Picker } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import KbFloatLayout from '@base/components/floatLayout';
import KbListBox from '@/components/_pages/order/listbox';
import KbButton from '@base/components/button';
import dayjs from 'dayjs';
import { useUpdate } from '~base/hooks/page';
import request from '~base/utils/request';
import './list-filter.scss';

const timeType = [
  {
    label: '近一个月',
    key: '1',
  },
  {
    label: '近三个月',
    key: '3',
  },
];

const endTime = dayjs().format('YYYY-MM-DD');

const format = (time) => time.format('YYYY-MM-DD');

const Index = (props) => {
  const { onSelect, initFilterData = {} } = props;
  const { start, end } = initFilterData;
  const [show, setShow] = useState(false);
  const [current, setCurrent] = useState({
    brand: {},
    timeType: {},
  });
  const [time, setTime] = useState([start, end]);
  const [, setHasSelected] = useState(false);
  const [brands, setBrands] = useState([]);

  const onCancel = () => {
    setShow(false);
  };

  const onClick = () => {
    setShow(true);
  };

  const formatTime = (type = 1, mode) => {
    if (mode == 'clean') {
      setTime([]);
      return;
    }
    const startTime = dayjs()
      .subtract(30 * Number(type), 'day')
      .format('YYYY-MM-DD');
    setTime([startTime, endTime]);
  };

  const calculateTime = (start, end) => {
    const startTimeInOneMonth = format(dayjs().subtract(30 * Number(1), 'day'));
    const startTimeInThreeMonth = format(dayjs().subtract(30 * Number(3), 'day'));
    if (end == endTime) {
      if (start == startTimeInOneMonth) {
        return timeType[0];
      }
      if (start == startTimeInThreeMonth) {
        return timeType[1];
      }
    }
  };

  const onTagChange = (data, type) => {
    setCurrent((prevData) => {
      if (prevData[type].label == data.label) {
        if (type == 'timeType') {
          formatTime(data.key, 'clean');
        }
        return {
          ...prevData,
          [type]: {},
        };
      }
      if (type == 'timeType') {
        formatTime(data.key);
      }
      return {
        ...prevData,
        [type]: data,
      };
    });
  };

  const onReset = () => {
    setCurrent({
      brand: {},
      timeType: {},
    });
    setTime([]);
    setShow(false);
    setHasSelected(false);
    onSelect({
      brand: null,
      start_time: null,
      end_time: null,
    });
  };

  const onConfirm = () => {
    const selected = Object.values(current).some((v = {}) => v.key);
    const { brand } = current;
    const [start, end] = time;
    const filterValue = {
      brand: brand.key,
      start_time: start && dayjs(start).format('YYYY-MM-DD 00:00:00'),
      end_time: end && dayjs(end).format('YYYY-MM-DD 23:59:59'),
    };
    const selectTime = time.filter((v) => v).length;
    if (selectTime > 0) {
      if (!start) {
        Taro.kbToast({
          text: '请选择开始时间',
        });
        return;
      }
      if (!end) {
        Taro.kbToast({
          text: '请选择结束时间',
        });
        return;
      }
    }
    setHasSelected(selected || selectTime == 2);
    setShow(false);
    onSelect(filterValue);
  };

  const onSelectTime = (type, value) => {
    const { detail = {} } = value;
    const [start, end] = time;
    const selectTime = detail.value;
    let timeType = {};
    if (type === 'start') {
      if (end && dayjs(selectTime).diff(end) > 0) {
        Taro.kbToast({
          text: '起始时间不能大于结束时间',
        });
        return;
      }
      timeType = calculateTime(selectTime, end) || {};
      setTime([selectTime, end]);
    }
    if (type === 'end') {
      if (start && dayjs(selectTime).diff(start) < 0) {
        Taro.kbToast({
          text: '结束时间不能大于起始时间',
        });
        return;
      }
      timeType = calculateTime(start, selectTime) || {};
      setTime([start, selectTime]);
    }
    setCurrent((prevData) => ({
      ...prevData,
      timeType,
    }));
  };

  const getBrandList = () => {
    request({
      url: '/api/lifeservice/Order/lifeServiceOrderTypeList',
      toastLoading: false,
      loadingStatusKey: 'loading',
      onThen: ({ code, data }) => {
        if (code == 0 && data) {
          setBrands(
            Object.keys(data).map((key) => {
              return {
                label: data[key],
                key: key,
              };
            }),
          );
        }
      },
    });
  };

  useEffect(() => {
    if (start && end) {
      setTime([start, end]);
      setHasSelected(true);
    }
  }, [start, end]);

  useUpdate(({ logined }) => {
    if (logined) {
      getBrandList();
    }
  }, []);

  return (
    <View className='kb-filter__button'>
      <View className='kb-filter__content'>
        <View onClick={onClick} hoverClass='kb-hover-opacity' className='btn'>
          <Text>筛选</Text>
          <AtIcon
            prefixClass='kb-icon'
            value='filter'
            className='kb-spacing-xs-l kb-spacing-xs-b kb-spacing-md-r'
            size={12}
          />
        </View>
        <KbFloatLayout
          onClose={onCancel}
          title='筛选'
          isOpened={show}
          renderFooter={
            <View className='kb-spacing-md kb-filter__footer'>
              <View className='at-row at-row__align--center'>
                <View hoverClass='kb-hover-opacity' onClick={onReset}>
                  <View className='kb-spacing-xl-lr'>
                    <AtIcon prefixClass='kb-icon' value='reset' className='kb-icon-size__lg' />
                    <View className='kb-size__xs kb-filter__footer--text'>重置</View>
                  </View>
                </View>
                <View className='at-col'>
                  <KbButton circle type='primary' onClick={onConfirm}>
                    确认
                  </KbButton>
                </View>
              </View>
            </View>
          }
        >
          <View className='kb-filter__modal'>
            <ScrollView className='kb-scrollview' scrollY>
              <View className='kb-spacing-xl'>
                <View className='kb-size__lg'>下单时间</View>
                <KbListBox
                  className='kb-order-filter__list'
                  itemClass='kb-order-filter__list--box'
                  list={timeType}
                  circle
                  itemSize='mini'
                  onChange={(data) => onTagChange(data, 'timeType')}
                  selectted={current.timeType.label}
                />
                <View className='at-row at-row__align--center at-row__justify--between kb-size__base'>
                  <View className='at-col kb-color__grey-3 kb-filter__modal--time'>
                    <Picker
                      value={time[0]}
                      mode='date'
                      fields='day'
                      onChange={onSelectTime.bind(null, 'start')}
                      end={endTime}
                    >
                      <Text className='kb-spacing-md-l'>{time[0] || '起始时间'}</Text>
                    </Picker>
                  </View>
                  <View className='at-col at-col-1 at-col--auto kb-spacing-xl-lr'>至</View>
                  <View className='at-col kb-color__grey-3 kb-filter__modal--time'>
                    <Picker
                      value={time[1]}
                      mode='date'
                      fields='day'
                      onChange={onSelectTime.bind(null, 'end')}
                      end={endTime}
                    >
                      <Text className='kb-spacing-md-l'>{time[1] || '终止时间'}</Text>
                    </Picker>
                  </View>
                </View>
                <View className='kb-size__lg'>下单品牌</View>
                <KbListBox
                  className='kb-order-filter__list'
                  itemClass='kb-order-filter__list--box'
                  circle
                  list={brands}
                  itemSize='mini'
                  onChange={(data) => onTagChange(data, 'brand')}
                  selectted={current.brand.label}
                />
              </View>
            </ScrollView>
          </View>
        </KbFloatLayout>
      </View>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  selectedBrand: '',
  onSelect: () => {},
};

export default Index;
