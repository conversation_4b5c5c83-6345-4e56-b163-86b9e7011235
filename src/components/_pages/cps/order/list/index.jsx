/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, Fragment, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtTabs, AtTabsPane } from 'taro-ui';
import { getTabCurrentByType, noop } from '~base/utils/utils';
import KbListFilter from './list-filter';
import KbListContent from './list-content';
import { createOrderTabs, updateTabList } from './_uitls';
import './index.scss';

const Index = (props) => {
  const {
    onSwitchTab,
    onReady,
    isAdmin,
    onGetted,
    tabKey,
    onFilter,
    active,
    filterData,
    ...restProps
  } = props;

  const tabList = createOrderTabs();
  const defaultCurrent = getTabCurrentByType(null, tabKey, tabList);
  const [tabLists, setTabLists] = useState(tabList);
  const [current, updateCurrent] = useState(defaultCurrent);

  useEffect(() => {
    updateCurrent(defaultCurrent);
  }, [defaultCurrent]);

  // 切换tab
  const handleSwitchTab = (current) => {
    if (!tabList) return;
    const tabItem = tabList[current];
    updateCurrent(current);
    onSwitchTab(tabItem);
  };

  // list组件准备就绪
  const handleReady = (key, ins) => {
    onReady(ins, key);
  };

  const onGetList = (list, _tabKey, extraData) => {
    if (extraData) {
      const _tablists = updateTabList(tabList, _tabKey, extraData);
      setTabLists(_tablists);
    }
    onGetted(list);
  };

  const onSelect = (selectData) => {
    onFilter(selectData, 'filter');
  };

  return (
    <Fragment>
      <View className='kb-cpsOrder-list'>
        {tabLists && <KbListFilter onSelect={onSelect} initFilterData={filterData} />}
        <AtTabs
          tabList={tabLists ? tabLists : []}
          onClick={handleSwitchTab}
          current={current}
          animated={false}
          swipeable={false}
          className='kb-cpsOrder-tabs'
        >
          {tabList.map((item, index) => {
            return (
              <AtTabsPane key={item.key} current={current} index={index}>
                <KbListContent
                  {...restProps}
                  active={current === index && active}
                  onReady={handleReady.bind(null, item.key)}
                  tabKey={item.key}
                  current={current}
                  isAdmin={isAdmin}
                  onGetted={onGetList}
                />
              </AtTabsPane>
            );
          })}
        </AtTabs>
      </View>
    </Fragment>
  );
};

export default Index;

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  onSwitchTab: noop,
  onReady: noop,
};
