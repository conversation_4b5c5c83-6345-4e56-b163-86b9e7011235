/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useMemo } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import KbNumber from '~base/components/number';
import { setClipboardData } from '~/utils/qy';
import { extendMemo } from '@/components/_pages/_utils';
import request from '~base/utils/request';
import { noop } from '~base/utils/utils';
import { order_type_map } from './_uitls';
import './list-item.scss';

const Index = (props) => {
  const { data: item, onClick } = props;
  const {
    order_id,
    is_admin,
    order_type,
    order_desc,
    create_time,
    order_type_desc,
    pay_price,
    order_rebate_fee,
    order_status,
    user_id,
    user_name,
    source_user_type,
    rebate_status,
  } = item || {};

  const handleClickItem = (e) => onClick(e, item);

  const handleCopy = (v, ev) => {
    ev.stopPropagation();
    setClipboardData(v);
  };

  const handleBillDetail = (item, order_rebate_info) => {
    if (order_rebate_info.text === '已结清') {
      if (item.order_rebate_fee > 0) {
        request({
          url: '/api/Bill/orderTransaction',
          data: {
            order_id: item.order_id,
          },
          onThen: (res) => {
            if (res.data.list && res.data.list.length > 0) {
              const oItem = res.data.list[0];
              Taro.navigator({
                url: 'user/account/detail',
                options: {
                  bill_id: oItem.id,
                  source: oItem.source,
                },
              });
            }
          },
        });
      } else {
        Taro.kbToast({
          text: '因返佣金额小于 0.01元，该订单暂不支持返佣！',
        });
      }
    }
  };

  const isRegiment = !!is_admin;
  const order_type_item = order_type_map[order_type] || {};
  const order_rebate_info = useMemo(() => {
    if (`${order_status}` === '0' || `${order_status}` === '-9') {
      return {
        color: '',
        text: '已取消',
      };
    }
    if (!isRegiment && order_status == '2') {
      return {
        color: 'green',
        text: '已支付',
      };
    }
    if (isRegiment && order_status == '2' && rebate_status == '1') {
      return {
        color: 'green',
        text: '已结清',
      };
    }
    if (isRegiment && order_status == '2' && rebate_status == '0') {
      return {
        color: 'red',
        text: '待返佣',
      };
    }
    return {
      color: '',
      text: '',
    };
  }, [pay_price, order_rebate_fee, order_status, isRegiment]);

  return (
    <Fragment>
      <View
        className='kb-list-item'
        hoverClass='kb-hover'
        onClick={handleClickItem}
        onLongPress={handleClickItem}
      >
        <View className='kb-list-item__info'>
          <View className='kb-list-item__info__order'>
            <View className='flex kb-margin-md-b'>
              <View>
                <Image
                  className='logo'
                  src='https://cdn-img.kuaidihelp.com/qj/miniapp/cps/logo.png'
                  mode='widthFix'
                />
              </View>
              <View
                className='order-number'
                onClick={handleCopy.bind(this, order_id)}
                hoverClass='kb-hover-opcity'
                hoverStopPropagation
              >
                订单号：{order_id} <Text className='kb-color__grey'>复制</Text>
              </View>
            </View>
            <View className='flex kb-margin-md-b'>
              <View>
                <Image className='sp-img' src={order_type_item.img} mode='widthFix' />
              </View>
              <View className='order-info'>
                <View className='order-name'>{order_desc}</View>
                <View>下单时间：{create_time}</View>
                <View>
                  服务类型：<Text className='order-type'>{order_type_desc}</Text>
                </View>
                {pay_price > 0 && <View>付款金额：{pay_price}元</View>}
              </View>
            </View>
          </View>
          {order_rebate_info && order_rebate_info.text && (
            <View className={`commission commission-${order_rebate_info.color}`}>
              {isRegiment && (
                <View
                  className='money'
                  onClick={() => handleBillDetail(item, order_rebate_info)}
                  hoverStopPropagation
                >
                  <Text className='kb-size__sm'>¥</Text>
                  <KbNumber number={order_rebate_fee} label='￥' />
                </View>
              )}
              <View className='tag'>{order_rebate_info.text}</View>
            </View>
          )}
        </View>
        {isRegiment && (
          <Fragment>
            <View className='kb-list-item__user'>
              <View className='item source'>
                订单来源:{' '}
                {source_user_type === 'self' ? (
                  '自购下单'
                ) : source_user_type === 'regiment' ? (
                  '团长下单'
                ) : (
                  <Fragment>
                    团员下单
                    <View className='source-tag'>
                      {source_user_type === 'user' ? '直推' : '间推'}
                    </View>
                  </Fragment>
                )}
              </View>
              <View className='item'>ID: {user_id}</View>
              <View className='item nickname'>用户昵称: {user_name}</View>
            </View>
            {order_rebate_info && order_rebate_info.text === '待返佣' && (
              <View className='kb-list-item__commission'>
                <Text className='kb-list-item__commission__label'>待结算佣金：</Text>
                次月25日发放至微快递特惠寄 “收益钱包”
              </View>
            )}
          </Fragment>
        )}
      </View>
    </Fragment>
  );
};

Index.defaultProps = {
  data: {},
  onClick: noop,
  hasSelected: false,
  checked: false,
};

Index.options = {
  addGlobalClass: true,
};

export default extendMemo(Index, ['data', 'hasSelected', 'checked', 'filterKey']);
