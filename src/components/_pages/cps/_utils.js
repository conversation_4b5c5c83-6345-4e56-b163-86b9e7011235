/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';

export const cpsPubId = 260864;
export const cpsList = [
  {
    key: 1,
    title: '19.9元起电影购票优惠',
    desc: '支持全国1w+影院',
    profit: '平均5~15%',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/type_1.png',
    icons: [
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_1.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_2.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_3.png',
    ],
    type: 'movie',
  },
  {
    key: 2,
    title: '惊喜红包天天领',
    desc: '外卖/券包/超时/酒店',
    profit: '平均3~6%',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/type_2.png',
    icons: [
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_4.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_5.png',
    ],
    type: 'meituan',
  },
  {
    key: 3,
    title: '每周五领 16元红包',
    desc: '每月18日领 18元红包',
    profit: '平均3~6%',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/type_3.png',
    icons: [
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_6.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_7.png',
    ],
    type: 'ele',
  },
  {
    key: 4,
    title: '低至5折起 打车出行服...',
    desc: '滴滴/花小猪/T3/同程',
    profit: '平均3~6%',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/type_4.png',
    icons: [
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_8.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_9.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_10.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_11.png',
    ],
    type: 'didi',
  },
  {
    key: 5,
    title: '特惠酒店',
    desc: '好房随时住',
    profit: '平均3~5%',
    img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/type_5.png',
    icons: [
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_4.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_11.png',
      'https://cdn-img.kuaidihelp.com/qj/miniapp/cps/icon_12.png',
    ],
    type: 'hotel',
  },
];

export const cpsNavigate = ({ type, userid } = {}) => {
  if (!type) return;
  if (type == 'profit') {
    Taro.navigator({
      url: 'profit?type=2',
    });
    return;
  }
  if (Taro.kbGetGlobalData(loadedCpsPluginsKey)) {
    handleCpsRedirect({ type, sid: userid, blank: 'target' });
    return;
  }
  let url = `cps/redirect?type=${type}&sid=${userid}`;
  Taro.navigator({
    url,
  });
};

const loadedCpsPluginsKey = 'loadedCpsPlugins';
export const handleCpsRedirect = ({ type, sid, blank = 'self' }) => {
  const baseUrl = `plugin://${type === 'movie' ? 'jtkMovie/home' : 'meishi/shop'}`;
  const url = `${baseUrl}?pub_id=${cpsPubId}&sid=${sid}${type !== 'movie' ? `&type=${type}` : ''}`;
  Taro.kbSetGlobalData(loadedCpsPluginsKey, true);
  if (blank == 'target') {
    Taro.navigateTo({ url });
  } else {
    Taro.redirectTo({ url });
  }
};
