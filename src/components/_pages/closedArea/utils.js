/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';

/**
 * 获取停发区查询的品牌
 *  */
const getStopAreaBrands = () => {
  return new Promise((resolve) => {
    const checkStopAreaBrands = Taro.kbGetGlobalData('CheckStopAreaBrands');
    if (checkStopAreaBrands) {
      resolve(checkStopAreaBrands);
      return;
    }
    request({
      url: '/api/StopArea/supportBrandList',
      toastLoading: false,
      toastSuccess: false,
    })
      .then((res) => {
        if (res.code == 0) {
          Taro.kbSetGlobalData('CheckStopAreaBrands', res.data);
          resolve(res.data);
        } else {
          resolve({});
        }
      })
      .catch(() => {
        resolve({});
      });
  });
};

export default getStopAreaBrands;
