

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */



.kb-area {
  line-height: 1.5;
  padding: $spacing-v-lg $spacing-h-lg;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $color-white;
  &__value,
  &__placeholder {
    flex-grow: 1;
    padding-right: $spacing-h-lg;
  }
  &__placeholder {
    color: $color-grey-3;
  }
  &::before,
  &::after {
    font-family: "kb-icon";
    color: $color-grey-2;
    font-size: $font-size-md;
  }
  &::before {
    content: "\e61d";
    margin-right: $spacing-h-lg;
  }
  &::after {
    content: "\e661";
    margin-right: -$spacing-h-sm;
  }
}
