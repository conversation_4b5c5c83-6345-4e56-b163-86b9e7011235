

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import qs from "qs";
import "./index.scss";

const Index = props => {
  const { placeholder, value } = props;
  const onClick = () => {
    const { province = "", city = "", district = "" } = value || {};
    Taro.navigator({
      url: `city?${qs.stringify({
        current: `${province}-${city}-${district}`
      })}`
    });
  };

  return (
    <View hoverClass="kb-hover" className="kb-area" onClick={onClick}>
      {value ? (
        <View className="kb-area__value">
          {value.province} {value.city} {value.district}
        </View>
      ) : (
        <View className="kb-area__placeholder">{placeholder}</View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true
};

Index.defaultProps = {
  value: null,
  placeholder: "请选择区域"
};

export default Index;
