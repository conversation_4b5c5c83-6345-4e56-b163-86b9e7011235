/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import isFunction from 'lodash/isFunction';
import Taro from '@tarojs/taro';
import {
  checkHasAddress,
  checkHasContact,
  checkNearX,
  checkNearY,
  checkSubsEnd,
  checkSubsOverrun,
  subtextNoSpace,
} from './ocr.check';
// import rotateListMap from './test-address-2';
// import { rotateList1 as rotateList } from './test-address';

/**
 *
 * @description 角度转为弧度
 * @param {*} degrees
 */
export function transDegToAngle(degrees) {
  return (degrees * Math.PI) / 180;
}

/**
 *
 * @description  角度调整为360度内
 * @param {*} angle
 */
export function adjustedAngleInCircle(angle) {
  return angle % (2 * Math.PI);
}

export class Ocr {
  constructor() {
    this.data = {
      faceImgWidth: 0,
      faceImgHeight: 0,
      imageInfo: {
        scale: 1,
        scaleWidth: 0,
        scaleHeight: 0,
        x: 0,
        y: 0,
      },
    };
    this.ocrParams = {
      textHeightScales: [0.7, 1.7, 2.7],
    };
    this.init();
  }
  triggerCallback(res) {
    const { callback } = this;
    this.clearTimeout();
    if (isFunction(callback)) {
      const { list = [] } = res || {};
      callback({
        list,
      });
    }
  }
  init() {
    // @ts-ignore
    if (typeof wx === 'undefined' || !wx.createVKSession) return;
    const session = wx.createVKSession({
      track: {
        plane: {
          mode: 3,
        },
        OCR: {
          mode: 2,
        },
      },
      version: 'v1',
    });
    session.start((err) => {
      this.sessionErr = null;
      if (err) {
        this.sessionErr = err; // 记录错误
        this.triggerCallback();
        return console.error('VK error: ', err);
      }
      session.on('updateAnchors', (anchors) => {
        this.dataTextContentList = [];
        this.dataTextContentList = this.dataTextContentList.concat(
          anchors.map((anchor) => {
            let result = {};
            // @ts-ignore
            result = {
              text: anchor.text,
              subtext: anchor.subtext,
              box: anchor.box,
              centerX: anchor.centerX,
              centerY: anchor.centerY,
            };
            if (anchor.box) {
              let lt = anchor.box[0];
              let lr = anchor.box[1];
              let rb = anchor.box[2];
              let lb = anchor.box[3];
              let width = lr.x - lt.x;
              let height = lb.y - lt.y;
              let avgX = (lt.x + lr.x + rb.x + lb.x) / 4;
              let avgY = (lt.y + lr.y + rb.y + lb.y) / 4;
              result.centerX = avgX;
              result.centerY = avgY;
              result.origin = {
                x: lt.x,
                y: lt.y,
              };
              result.size = {
                width,
                height,
              };
            }
            return result;
          }),
        );
        // 调试
        // Taro.setClipboardData({
        //   data: `${JSON.stringify(this.dataTextContentList)}`,
        // });
        this.polymerizationAddress(this.dataTextContentList).then((res) =>
          this.triggerCallback(res),
        );
      });
      // @ts-ignore
      session.on('removeAnchors', () => {
        console.log('anchor remove');
      });
    });
    this.session = session;
  }

  /**
   *
   * @description 获取尺寸
   * @param {*} box
   */
  getSize(box) {
    const [point0, point1, point2] = box;
    // 计算两个相邻边的向量
    const vector1 = {
      x: point1.x - point0.x,
      y: point1.y - point0.y,
    };
    const vector2 = {
      x: point2.x - point1.x,
      y: point2.y - point1.y,
    };

    // 计算两个向量的长度
    const width = Math.sqrt(vector1.x ** 2 + vector1.y ** 2);
    const height = Math.sqrt(vector2.x ** 2 + vector2.y ** 2);

    return {
      width,
      height,
    };
  }

  // 获取逆时针旋转前的坐标点
  inverseRotateBox(box, angle) {
    let { centerX = 0, centerY = 0 } = this.getImageCenter();
    // 矩形框：老中心点
    return box.map((item) => {
      const { x: ox, y: oy } = item;
      const dx = ox - centerX;
      const dy = oy - centerY;
      const xBack = dx * Math.cos(-angle) - dy * Math.sin(-angle);
      const yBack = dx * Math.sin(-angle) + dy * Math.cos(-angle);
      const x = xBack + centerX;
      const y = yBack + centerY;
      return { x, y };
    });
  }

  /**
   * @description 修正坐标点，按照回正数据，字体回正后左上角为0号坐标；
   */
  patchBox(item, list = []) {
    const { box: boxOriginal, subtext } = item;
    const {
      box: [firstBox0],
    } = list[0] || {};
    const {
      box: [lastBox0],
    } = list.slice(-1)[0] || {};
    let box = boxOriginal;
    let angle = 0;
    const [b0, b1, b2, b3] = box;
    let size = this.getSize(boxOriginal);
    const subtextLen = subtext.length;
    if (b0.x < b1.x && Math.abs(b0.y - b1.y) < 2) {
      // y大小不超过2，认为是水平的
      // 0,90,180,270
      if (size.width > size.height) {
        // 0或180
        const isOverturn = firstBox0.y > lastBox0.y;
        if (isOverturn) {
          box = [b2, b3, b0, b1];
          angle = transDegToAngle(180);
        } else {
          box = boxOriginal;
          angle = transDegToAngle(0);
        }
      } else {
        // 90或270
        const isOverturn = firstBox0.x < lastBox0.x;
        if (isOverturn) {
          box = [b3, b0, b1, b2];
          angle = transDegToAngle(270);
        } else {
          box = [b1, b2, b3, b0];
          angle = transDegToAngle(90);
        }
      }
    } else {
      if (size.width < size.height) {
        // 45度
        box = [b1, b2, b3, b0];
      } else {
        const isOverturn = firstBox0.y > lastBox0.y;
        if (isOverturn) {
          box = [b2, b3, b0, b1];
        }
      }
      angle = this.getAngle(box);
    }

    if (subtextLen > 3) {
      this.subtextAngle = angle;
    } else {
      angle = this.subtextAngle || 0;
      // 长度过小时，将angle更新
      switch (angle) {
        case 0:
          box = boxOriginal;
          break;
        case transDegToAngle(90):
          box = [b1, b2, b3, b0];
          break;
        case transDegToAngle(180):
          box = [b2, b3, b0, b1];
          break;
        case transDegToAngle(270):
          box = [b3, b0, b1, b2];
          break;

        default:
          break;
      }
    }

    // 更新坐标后，重新计算size
    size = this.getSize(box);
    return {
      box,
      boxOriginal: box,
      angle,
      size,
    };
  }

  /**
   *
   * @description box修补为正确尺寸
   * @param {*} item 坐标数据
   */
  formatRealSize(item, list) {
    const { box, angle, size, boxOriginal } = this.patchBox(item, list);

    const rotateBox = this.inverseRotateBox(box, angle);
    return {
      ...item,
      angle,
      origin: rotateBox[0],
      size,
      box: rotateBox,
      boxOriginal,
    };
  }

  /**
   *
   * @description list格式化，补充angle，并比例转换为实际尺寸
   * @param {*} list
   */
  formatter(list) {
    const { faceImgWidth, faceImgHeight } = this.data;
    const patchedList = list
      .filter(({ subtext, box: [{ x, y }] }) => !!subtextNoSpace(subtext) && x > 0 && y > 0)
      .map((item) => ({
        ...item,
        box: item.box.map(({ x, y }) => ({
          x: x * faceImgWidth,
          y: y * faceImgHeight,
        })),
      }));

    // 补充角度与尺寸（angle、size）
    let patchedListWithAngle = patchedList.map((item) => this.formatRealSize(item, patchedList));

    // 过滤数据并补充索引
    patchedListWithAngle = patchedListWithAngle
      .filter((item) => this.filter(item, patchedListWithAngle))
      .map((item, index) => ({
        ...item,
        index,
      }));

    return patchedListWithAngle;
  }

  /**
   *
   * @description 获取中心坐标点
   */
  getCenter(box) {
    const centerX = (box[0].x + box[2].x) / 2;
    const centerY = (box[0].y + box[2].y) / 2;
    return { x: centerX, y: centerY };
  }

  /**
   *
   * @description 获取图片中心坐标
   * 主要用于矩形框回正处理，
   * 注意：这里要和canvas中图片旋转时使用的中心点坐标一致
   */
  getImageCenter() {
    const {
      imageInfo: { x, y, scaleWidth, scaleHeight },
    } = this.data;
    return {
      centerX: x + scaleWidth / 2,
      centerY: y + scaleHeight / 2,
    };
  }
  /**
   *
   * @description 获取旋转角度
   * @param {*} box
   */
  getAngle(box) {
    const point0 = box[0];
    const point1 = box[1];
    const { x: x0, y: y0 } = point0;
    const { x: x1, y: y1 } = point1;
    // 计算矩形的对角线向量
    const diagonalVectorX = x1 - x0;
    const diagonalVectorY = y1 - y0;

    // 计算对角线向量的初始角度
    let diagonalAngle = Math.atan2(diagonalVectorY, diagonalVectorX);
    if (diagonalAngle < 0) {
      diagonalAngle += 2 * Math.PI;
    }

    // 计算旋转的角度
    let rotationAngle = diagonalAngle;

    // 临界角度，小于1度的，当0度处理
    const max = transDegToAngle(1);
    // // 将角度转换为度数
    // const rotationAngleDegrees = rotationAngle * (180 / Math.PI);
    return rotationAngle > 0 && rotationAngle < max ? 0 : adjustedAngleInCircle(rotationAngle);
  }

  /**
   *
   * 计算两行是否相邻
   * @param cur: 当前行
   * @param next: 可能相邻行
   * @param subs 已经集合的行
   * @param dir 比较方向
   * @param index: 高度放大倍数索引
   * @returns boolean；
   */
  checkIsNear(cur, next, subs, dir, patchScale, index = 0) {
    const { textHeightScales } = this.ocrParams;
    const textHeightScale = textHeightScales[index];
    if (!textHeightScale || checkSubsEnd(cur, next, subs, dir)) {
      return false;
    }
    const { size: size1, box: box1 } = cur;
    const { size: size2, box: box2 } = next;

    const angle1 = 0; // 已将box反向回转
    const angle2 = 0; // 已将box反向回转
    const height1 = size1.height * patchScale * textHeightScale;
    const height2 = size2.height * patchScale;
    const width1 = size1.width;
    const width2 = size2.width;

    // 可以根据需要调整相邻的条件，这里使用两个矩形中心点的距离作为条件
    const { x: centerX1, y: centerY1 } = this.getCenter(box1);
    const { x: centerX2, y: centerY2 } = this.getCenter(box2);

    // 将两行的中心点坐标投影到原始坐标系中
    const projectedCenterX1 = centerX1 * Math.cos(angle1) - centerY1 * Math.sin(angle1);
    const projectedCenterY1 = centerX1 * Math.sin(angle1) + centerY1 * Math.cos(angle1);

    const projectedCenterX2 = centerX2 * Math.cos(angle2) - centerY2 * Math.sin(angle2);
    const projectedCenterY2 = centerX2 * Math.sin(angle2) + centerY2 * Math.cos(angle2);
    // 可以根据需要调整相邻的条件，这里使用两行中心点投影在垂直方向上的距离作为条件
    const verticalDistance = Math.abs(projectedCenterY2 - projectedCenterY1);
    const horizontalDistance = Math.abs(projectedCenterX2 - projectedCenterX1);

    // 可以根据需要调整相邻的距离阈值：两行高度一般
    const verticalThreshold = (height1 + height2) / 2;
    const horizontalThreshold = (width1 + width2) / 2;

    // 中心点移动，因选择框是按照自身中心点旋转的；
    const { x: fixX1 } = this.calculateBoxFixDiff([cur]);
    const { x: fixX2 } = this.calculateBoxFixDiff([next]);

    const isNearX = checkNearX(cur, next, fixX1, fixX2);
    const isNearY = checkNearY(cur, next);

    const isNear =
      isNearY ||
      (verticalDistance < verticalThreshold && horizontalDistance < horizontalThreshold && isNearX);

    if (isNear) {
      next.textHeightScale = textHeightScale;
      return isNear;
    }

    if (checkSubsOverrun(subs)) {
      return false;
    }

    // 行间距不大于rowSpace，左侧偏移不超过colSpace；
    return this.checkIsNear(cur, next, subs, dir, patchScale, 1 + index);
  }

  /**
   *
   * @description 计算子行中心
   * @param {[{x:number;y:number}][]} boxes
   */
  getBoxCenter(boxes) {
    const xList = [];
    const yList = [];
    boxes.forEach((item) => {
      xList.push(...item.map((iitem) => iitem.x));
      yList.push(...item.map((iitem) => iitem.y));
    });
    return {
      x: (Math.min(...xList) + Math.max(...xList)) / 2,
      y: (Math.min(...yList) + Math.max(...yList)) / 2,
    };
  }

  /**
   *
   * @description 计算旋转与转正中心点修复差值
   * @param {{box:{x:number;y:number}[],boxOriginal:{x:number;y:number}[]}} subs
   */
  calculateBoxFixDiff(subs) {
    // 中心点移动，因选择框是按照自身中心点旋转的；
    const { x: scX, y: scY } = this.getBoxCenter(subs.map((item) => item.box));
    const { x: oscX, y: oscY } = this.getBoxCenter(subs.map((item) => item.boxOriginal));
    return {
      x: scX - oscX,
      y: scY - oscY,
    };
  }

  /**
   *
   * @description 预估图片旋转角度
   * 根据识别出的文案信息确认：
   * 1、有符合提交的地址信息，按照地址信息中，最长数据的旋转角度为准；
   * 2、不满足1，则按照所有信息中最长的为准；
   */
  estimateImageAngle(addressList, originalList) {
    let angle = 0;
    let len = 0;
    const list = addressList.length > 0 ? addressList : originalList;
    list.forEach((item) => {
      const { subtext, angle: itemAngle } = item;
      const subtextLen = subtext.length;
      if (subtextLen > len) {
        len = subtextLen;
        angle = itemAngle;
      }
    });
    return angle;
  }

  /**
   *
   * @description 过滤不合理的数据：
   * 1、角度大于平均值的；（仅针对字长度等于1的）
   * @param {{angle:number;subtext:string}} item
   */
  filter(item, list) {
    const { angle, subtext } = item;
    if (subtext.length > 1) return true;
    const angleTotal = list.reduce((pre, cur) => {
      return pre + cur.angle;
    }, 0);
    // 平均角度
    const averageAngle = angleTotal / list.length;
    return angle - averageAngle < transDegToAngle(90);
  }

  /**
   *
   * 聚合地址
   * 1、符合一下关键的认定为地址
   */
  async polymerizationAddress(listOriginal) {
    // 补充旋转角度
    const list = this.formatter(listOriginal);
    const hasAddressList = await checkHasAddress(list);

    // 查找相邻行
    const subsIndexMap = []; // 收集索引
    const findSubs = (item) => {
      return new Promise((resolve) => {
        const triggerRun = (patchScale = 1) => {
          // 从该条开始，上下查找
          const { index: start } = item;
          if (subsIndexMap.includes(start)) {
            // 已被收集
            return resolve(item);
          }
          const subsIndex = [start]; // 已被集合到subs中的索引
          const subs = [item]; // 相邻行
          let preIndex = start; // 上一行索引
          let preNextIndex = start;
          let nextIndex = start; // 下一行索引
          let nextPreIndex = start;
          const maxIndex = list.length - 1; // 最大索引
          while (preIndex >= 0 || nextIndex <= maxIndex) {
            preIndex--;
            nextIndex++;
            const pre = list[preIndex];
            const next = list[nextIndex];
            if (
              pre &&
              !subsIndexMap.includes(preIndex) &&
              this.checkIsNear(list[preNextIndex], pre, subs, 'pre', patchScale)
            ) {
              preNextIndex = preIndex;
              subsIndex.push(preIndex);
              subs.unshift(pre);
            }
            if (
              next &&
              !subsIndexMap.includes(nextIndex) &&
              this.checkIsNear(list[nextPreIndex], next, subs, 'next', patchScale)
            ) {
              nextPreIndex = nextIndex;
              subsIndex.push(nextIndex);
              subs.push(next);
            }
          }
          // if (subs.length === 1 && patchScale === 1) {
          //   triggerRun(3.4);
          //   return;
          // }
          subsIndexMap.push(...subsIndex); // 已被收集当索引集合
          resolve({
            ...item,
            subs,
          });
        };
        triggerRun();
      });
    };

    // 子列排序
    // 有些情况会出现顺序异常，需要排序
    const subsSorter = (subs) => subs.sort((a, b) => (a.origin.y - b.origin.y > 0 ? 1 : -1));

    // 过滤出含有subs的
    // 将subs框出
    const formatter = (res) =>
      res
        .filter((item) => item.subs)
        .map((item) => {
          const itemSubs = subsSorter(item.subs);
          const itemSubsFirst = itemSubs[0];
          const itemSubsLast = itemSubs[itemSubs.length - 1];
          const maxWith = Math.max(...itemSubs.map((iitem) => iitem.origin.x + iitem.size.width));
          const minX = Math.min(...itemSubs.map((iitem) => iitem.origin.x));
          // 1 : 含有地址和联系方式；0：含有地址；
          // 有联系方式
          const hasContact = checkHasContact(itemSubs);
          const accuracy = hasContact ? 1 : 0;

          // 中心点移动，因选择框是按照自身中心点旋转的；
          const { x: fixX, y: fixY } = this.calculateBoxFixDiff(itemSubs);

          return {
            ...item,
            accuracy,
            origin: {
              x: Math.min(...itemSubs.map((iitem) => iitem.origin.x)) - fixX, // 取最小x
              y: Math.min(...itemSubs.map((iitem) => iitem.origin.y)) - fixY, // 取最小y
            },
            size: {
              width: maxWith - minX, // 取最大宽度
              height: itemSubsLast.origin.y + itemSubsLast.size.height - itemSubsFirst.origin.y,
            },
          };
        });

    // 排序
    const sorter = (res) => res.sort((a, b) => (a.accuracy - b.accuracy > 0 ? -1 : 1));

    // 提取最大值
    const filterAccuracy = (res, a = 1) => res.filter((item) => item.accuracy === a).length;
    // 只取带有联系方式的，或者全部取出；
    const sliceMax = (res) => ({
      list: res.slice(0, filterAccuracy(res, 1) || res.length),
    });

    return await Promise.all(hasAddressList.map(findSubs))
      .then(formatter)
      .then(sorter)
      .then(sliceMax);
  }
  /**
   *
   * @description ocr识别
   * @param {*} imageData
   * @param {*} imageInfo
   */
  async runOCR(imageData, imageInfo) {
    // 调试
    // if (!imageData) return;
    // const { width, height } = imageData;
    // this.data.faceImgWidth = width;
    // this.data.faceImgHeight = height;
    // this.data.imageInfo = imageInfo;
    // return await new Promise((resolve) => {
    //   const orgList = rotateList || rotateListMap[`rotateList${start}`];
    //   console.log('orgList', orgList);
    //   this.polymerizationAddress(orgList).then((list) => {
    //     console.log('list', list);
    //     resolve(list);
    //   });
    // });

    this.callback = null;
    const isValid = !(this.sessionErr || !imageData || !this.session || !this.session.runOCR);
    if (isValid) {
      const { width, height } = imageData;
      this.data.faceImgWidth = width;
      this.data.faceImgHeight = height;
      this.data.imageInfo = imageInfo;
      this.session.runOCR({
        frameBuffer: imageData.data.buffer,
        width,
        height,
      });
    }
    this.clearTimeout();
    const res = await new Promise((resolve) => {
      this.callback = resolve;
      this.callbackTimeDelay = setTimeout(() => {
        // 半秒无结果
        this.triggerCallback();
      }, 500);
    });
    return res;
  }
  clearTimeout() {
    if (this.callbackTimeDelay) {
      clearTimeout(this.callbackTimeDelay);
    }
  }
  destroy() {
    this.clearTimeout();
    if (this.session) {
      this.session.destroy();
    }
  }
}
