/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
import request from '@base/utils/request';

// 清理base64，去除类型标记前缀
function clearBase64Data(base64) {
  return `${base64}`.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
}

// 图片上传并识别
// eslint-disable-next-line import/prefer-default-export
export async function triggerOcrImage(files) {
  const filePaths = isArray(files) ? files.filter((item) => item && item.filePath) : null;
  if (!filePaths || filePaths.length === 0) {
    Taro.kbToast({ text: '请先选择图片！' });
    return [];
  }
  const ins = Taro.kbToast({ status: 'loading', text: '解析中' });
  const errMsg = [];
  const collectErrMsg = (res) => {
    const { code, msg } = res;
    if (code > 0 && msg) {
      errMsg.push(msg);
    }
    return res;
  };
  const res = await Promise.all(
    filePaths.map(
      ({ filePath, isBase64, ...restItem }) =>
        new Promise((resolve) => {
          return request({
            url: '/api/AddressBook/getAddrInfoByImg',
            ...(!isBase64
              ? {
                  data: {
                    filePath,
                    name: 'img',
                  },
                  requestDataType: 'file',
                }
              : {
                  // 微信本地文件保存失败，改为base64上传解析
                  data: {
                    b_file: clearBase64Data(filePath),
                  },
                }),
            toastLoading: false,
            onThen: (res) => {
              const { data: resData } = res;
              const orderKey = ['name', 'tel', 'province', 'city', 'county', 'address'];
              const text = resData ? orderKey.map((key) => resData[key]).join('') : '';

              if (text && isString(text)) {
                // 直接将图片内容返回
                resolve({
                  ...restItem,
                  text,
                  img: filePath,
                });
              } else {
                collectErrMsg(res);
                resolve(null);
              }
            },
          });
        }),
    ),
  );
  const list = res.filter((item) => !!item);
  if (list.length === 0) {
    ins.update({ text: errMsg.join('、') || '未识别出地址信息' });
  } else {
    ins.close();
  }
  return list;
}
