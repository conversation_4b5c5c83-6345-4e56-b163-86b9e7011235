/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import apis from '@/utils/apis';
import Create from '@base/components/long-list/create';

/**
 *
 * @description 获取城市列表
 * @param {*} onThen
 */
export function createCityLongList(onThen) {
  return {
    storageKey: 'citys',
    packetSize: 5,
    packetStorage: (data) => {
      // 格式化分组缓存
      const { province, city, district } = data;
      const district_1 = {},
        district_2 = {},
        district_3 = {},
        district_4 = {};
      Object.keys(district).map((key) => {
        if (0 < key && key <= 3732) {
          district_1[key] = district[key];
        } else if (3732 < key && key <= 6000) {
          district_2[key] = district[key];
        } else if (6000 < key && key <= 9000) {
          district_3[key] = district[key];
        } else {
          district_4[key] = district[key];
        }
      });
      return [
        { province, city },
        { district: district_1 },
        { district: district_2 },
        { district: district_3 },
        { district: district_4 },
      ];
    },
    api: {
      mastLogin: false,
      url: apis.city,
      onThen,
    },
  };
}

/**
 *
 * @description 获取城市信息
 */
export function getCities() {
  return new Promise((resolve) => {
    new Create(createCityLongList((_, res) => resolve(res.data)));
  });
}
