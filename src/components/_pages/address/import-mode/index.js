

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import Taro, { useEffect, useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';

function Index(props) {
  const { className, data, onChange, canToPay, canCollection } = props;
  const [modeList, setModeList] = useState([{ value: 'normal', label: '常规' }]);
  const [mode, setMode] = useState('normal');
  useEffect(() => {
    let list = [...modeList];
    if (canToPay) {
      list.push({ value: 'to_pay', label: '到付' });
    }
    if (canCollection) {
      list.push({ value: 'collection', label: '代收货款' });
    }
    setModeList(list);
  }, []);
  useEffect(() => {
    if (data) {
      setMode(data);
    }
  }, [data]);
  const onSwitchMode = (value) => {
    setMode(value);
    onChange({ value });
  };
  const showTips = () => {
    Taro.kbModal({
      className: 'kb-size__base',
      content: [
        {
          text: '收件人姓名、电话、省市区、详细地址、物品类型、备注为基础导入，全部导入模式都支持；',
        },
        {
          text: '常规模式：',
        },
        {
          text: '支持全部信息导入（收件人姓名、电话、地址在一个单元格内）',
        },
        {
          text: '代收模式：',
        },
        {
          text: '支持代收金额导入（代收金额为单独一列数据，具体对应每一行收件人信息）',
        },
        {
          text: '到付模式：',
        },
        {
          text: '支持到付金额导入（到付金额为单独一列数据，具体对应每一行收件人信息）',
        },
      ],
    });
  };
  return (
    <View
      className={`kb-box at-row at-row__justify--between at-row__align--center kb-spacing-md-lr kb-spacing-sm-tb ${className}`}
    >
      <View className='at-col' onClick={showTips}>
        导入模式
        <AtIcon className='kb-color__orange' prefixClass='kb-icon' value='help' size='16' />
      </View>
      <View className='at-col'>
        <View className='at-row at-row__justify--end'>
          {modeList.map((item) => (
            <AtButton
              className='kb-margin-md-r'
              type={item.value == mode ? 'primary' : 'secondary'}
              size='small'
              circle
              onClick={() => onSwitchMode(item.value)}
            >
              {item.label}
            </AtButton>
          ))}
        </View>
      </View>
    </View>
  );
}

Index.defaultProps = {
  className: '',
  canToPay: false,
  canCollection: false,
  data: 'normal',
  onChange: () => {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
