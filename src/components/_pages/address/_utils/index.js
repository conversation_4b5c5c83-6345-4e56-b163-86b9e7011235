/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  extractData,
  mergeBySpace,
  getStorage,
  removeStorage,
  getStorageSync,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import { check } from '@base/utils/rules';
import apis from '@/utils/apis';
import isArray from 'lodash/isArray';
import isEqual from 'lodash/isEqual';
import isString from 'lodash/isString';
import isPlainObject from 'lodash/isPlainObject';

export const apiURLs = {
  company: {
    getList: '/g_wkd/v2/work/CustomerAddress/getListByCustomerId',
    setDefault: '/g_wkd/v2/work/CustomerAddress/topOrDefaultAddress',
    setTop: '/g_wkd/v2/work/CustomerAddress/topOrDefaultAddress',
    update: '/g_wkd/v2/work/CustomerAddress/upCustomerAddress',
    save: '/g_wkd/v2/work/CustomerAddress/addCustomerAddress',
    remove: '/g_wkd/v2/work/CustomerAddress/delCustomerAddress',
  },
  person: {
    getList: '/api/weixin/mini/address/record/history',
    setDefault: '/api/weixin/mini/address/record/setDefault',
    setTop: '/api/AddressBook/top',
    update: '/api/AddressBook/add',
    save: '/api/AddressBook/add',
    remove: '/api/weixin/mini/address/record/delete',
  },
};

// type转化为receive转化为collect
export const fixType = (type) => {
  return type === 'send' ? type : 'collect';
};

export const addressKeys = ['name', 'mobile', 'province', 'city', 'district', 'address', 'save'];

//
export const excludeAddressKeys = ['save'];

// 地址key对应的内容
export const addressKeysMap = {
  all: '地址信息',
  whatever: '地址信息有误',
  name: '姓名',
  mobile: '手机号',
  province: '省份',
  city: '城市',
  district: '地区',
  address: '详细地址',
  door: '门牌号',
  longitude: '经纬度',
  latitude: '经纬度',
  service: '增值服务',
};

// 检查数据完整性
export function checkDataComplete(data, keys = addressKeys, excludeKeys = excludeAddressKeys) {
  let n_keys = keys.filter((key) => !excludeKeys.includes(key));
  const errors = n_keys.filter((key) => !data || !data[key]);
  const error = errors[0] || '';
  if (!error) {
    return {
      error,
      complete: true,
    };
  }
  if (errors.length === n_keys.length) {
    // 所有信息都不存在
    return {
      error: 'all',
      complete: false,
    };
  }
  return {
    error,
    complete: false,
  };
}

// 获取地址编辑提交按钮
export function getSubmitBars(action, fixFor = 'edit') {
  if (action === 'fix') {
    const bars = [
      {
        key: 'receive',
        label: '用于收件地址',
      },
    ];
    if (fixFor === 'edit') {
      bars.unshift({
        key: 'send',
        label: '用于寄件地址',
        type: 'secondary',
      });
    }
    return bars;
  }
  return [
    {
      key: 'save',
      label: '保存',
    },
  ];
}

// 检查列表中是否存在错误的地址信息
export function getErrorAddressIndex(list, toast = true) {
  const index = list.findIndex((item) => item.error);
  let errMsg = '';
  if (index >= 0) {
    const { error } = list[index];
    const others = ['whatever']; // 其他错误，不用补充地址缺少
    const fix = others.includes(error) ? '' : `地址缺少`;
    errMsg = `第${1 + index}条${fix + addressKeysMap[error]}`;
    toast &&
      Taro.kbToast({
        text: errMsg,
      });
  }
  return { index, errMsg };
}

// 本地收件人列表缓存key
export const receiveStorageKey = 'receiveList';
export const sendStorageKey = 'send';
export const defaultSendStorageKey = 'address';
// 提取公共地址部分
export function extractAddressInfo(data, merge = []) {
  if (process.env.MODE_ENV == 'wkd') {
    return extractData(data, [
      ['name', 'real_name'],
      'name',
      ['mobile', (item) => mergeBySpace(item.mobile, item.telephone_code, item.phone)],
      'province',
      ['province', 'address_province'],
      ['province', 'province_name'],
      'city',
      ['city', 'address_city'],
      ['city', 'city_name'],
      ['district', 'address_county'],
      ['district', 'county_name'],
      ['district', 'county'],
      ['address', 'address_detail'],
      ['detail', 'address'],
      ...merge,
    ]);
  }
  return extractData(data, [
    'name',
    ['mobile', (item) => mergeBySpace(item.mobile, item.tel)],
    'province',
    'city',
    ['district', 'county'],
    // ['address', 'detail'],
    // ['detail', 'address'],
    ['address', 'address'],
    ...merge,
  ]);
}

// 批量解析地址
export function batchAnalysisAddress(text, opts) {
  return new Promise((resolve, reject) => {
    const { filter, ...api } = opts || {};
    request({
      ...api,
      url: apis['address.parse'],
      data: {
        addrstr: text,
      },
      onThen: ({ data, code, msg }) => {
        const list = isArray(data)
          ? data
              .filter(filter ? filter : (item) => item.name && item.address && item.province)
              .map((item) =>
                extractAddressInfo(item, [
                  'province_shortname',
                  'original',
                  'province_confidence',
                  'city_confidence',
                  ['district_confidence', 'county_confidence'],
                ]),
              )
          : [];
        if (list.length > 0) {
          resolve(list);
        } else {
          reject(new Error(code > 0 ? msg : '未识别出合法地址'));
        }
      },
    });
  });
}

// 比较地址列表，判断两组数据的地址信息是否相同
export function repeatSortAddressList(list, isRemoveRepeat) {
  let repeat = 0;
  list.map((item, index) => {
    const repeatIndex = list.findIndex(
      (iitem, iindex) =>
        iindex < index && isEqual(extractData(item, addressKeys), extractData(iitem, addressKeys)),
    );
    if (repeatIndex >= 0) {
      if (!isRemoveRepeat) {
        list.splice(repeatIndex, 0, item);
        list.splice(index + 1, 1);
        repeat++;
      } else {
        list.splice(index, 1);
        // 重复执行可不用考虑是否为有序的重复数据；
        // 如果确定是有序的可考虑使用快慢指针优化；
        repeatSortAddressList(list, isRemoveRepeat);
      }
    }
  });
  return repeat;
}

//小邮桶中提交订单时，打印类型的转换
export function formatXytPrintType(type) {
  let print = void 0;
  switch (type) {
    case 'pprint':
      print = '1';
      break;
    case 'print':
      print = '3';
      break;
  }
  return print;
}

/**
 *
 * @description 创建tab
 * @param {*} org send | receive
 * @param {*} action select
 * @returns
 */
export function createTabList({ org, action }) {
  const SEND = org === 'send';
  const RECEIVE = org === 'receive';
  const SELECT = action === 'select';

  const send = [{ title: '发件人地址', key: 'send' }];
  const receive = [{ title: '收件人地址', key: 'receive' }];

  if (SELECT) {
    if (SEND) {
      return send;
    }
    if (RECEIVE) {
      return [...receive];
    }
  } else {
    return [...send, ...receive];
  }
}

/**
 * 处理文件上传接口差异
 */
export const getUploadFileApis = ({ filePath, name }) => {
  return {
    url: '/api/AddressBook/excelAddressUpload',
    data: {
      filePath,
      name: 'address_excel',
      fileType: 'file',
      excel: name,
    },
    formatResponse: ({ code, data, msg }) => {
      return {
        code,
        data: {
          path: data.file,
          data: data.list,
        },
        msg,
      };
    },
  };
};
// 抽取有数量单位的数字
export const getNumberByText = (text) => {
  if (isString(text)) {
    console.log('text.match(/^(d+)/)', text.match(/^(\d+)/));
    const res = text.match(/^(\d+)/);
    return res ? Number(res[0]) : null;
  }
  return null;
};
/**
 * @description 处理文件解析数据接口差异
 */
export const execFileDataApis = ({ allType, list, file }) => {
  let typeMap = {
    商品名称: '物品类型',
    '全部信息（姓名、电话、地址）': '全部信息',
    不导入: '忽略此项',
  };
  const info = list.map((item) => {
    const { type } = item;
    return type.includes(allType) ? '所有' : typeMap[type] || type;
  });
  return {
    url: '/api/AddressBook/addressExcelContent',
    data: {
      info: JSON.stringify(info),
      file,
    },
    formatResponse: ({ code, data, msg }) => {
      let n_data = data;
      if (data.length > 0) {
        n_data = data.map((item) => {
          return extractData(item, [
            ['name', 'receiveName'],
            ['mobile', 'receiveMobile'],
            ['province', 'receiveProvince'],
            ['city', 'receiveCity'],
            ['county', 'receiveArea'],
            ['detail', 'receiveAddress'],
            'product',
            'weight',
            ['note', 'mark'],
            ['collection_amount', 'collection_delivery'],
            ['to_pay_amount', 'to_pay'],
          ]);
        });
      }
      return {
        code,
        data: n_data,
        msg,
      };
    },
  };
};

/**
 *
 * @description 获取地址前缀
 * @param {*} req
 * @returns
 */
export function getAddressPrefix(req) {
  let prefix = '';
  if (process.env.MODE_ENV === 'wkd') {
    const { extraData } = req;
    const extraDataItem = extraData && extraData[0];
    if (extraDataItem && extraDataItem !== 'save') {
      prefix = extraDataItem;
    }
  }
  return prefix;
}

/**
 *
 * @description 格式化编辑地址请求
 * @param {*} req
 * @param {*} param1
 * @returns
 */
export function formatRequestEdit(req, { params }) {
  const { org, id, source } = params;
  let address_info =
    source != 'list'
      ? {
          ...req,
          ...extractData(req, [
            ['address', `detail`],
            ['county', 'district'],
          ]),
        }
      : {
          ...extractData(req, [
            [
              'name',
              (data) => {
                return [data[`${org}_name`], data[`${org}_company`]]
                  .filter((item) => !!item)
                  .join('##');
              },
            ],
            ['mobile', `${org}_mobile`],
            ['province', `${org}_province`],
            ['city', `${org}_city`],
            ['county', `${org}_district`],
            ['address', `${org}_address`],
          ]),
        };

  return {
    ...address_info,
    address_type: fixType(org),
    id,
  };
}

/**
 *
 * @description 格式化表单数据
 * @param {*} data
 * @param {*} req
 * @returns
 */
export function formatFormDataByPrefix(data, req) {
  if (process.env.MODE_ENV === 'wkd') {
    const prefix = getAddressPrefix(req);
    if (prefix) {
      const newData = {};
      Object.keys(data).forEach((key) => {
        newData[key.replace(/(.*)(_.*)/, `${prefix}$2`)] = data[key];
      });
      return newData;
    }
    return data;
  } else {
    return data;
  }
}

/**
 *
 * @description 兼容支付宝与微信
 * @returns
 */
export const chooseAddress = () => {
  return new Promise((resolve, reject) => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      Taro.chooseAddress()
        .then((res) => {
          const {
            userName: name,
            provinceName: province,
            cityName: city,
            countyName: district,
            detailInfo: address,
            telNumber: mobile,
          } = res;

          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        })
        .catch(reject);
    } else {
      if (!my.getAddress) {
        reject(new Error('支付宝版本过低无法使用该功能'));
        return;
      }
      my.getAddress({
        success: (res) => {
          const {
            fullname: name,
            mobilePhone: mobile,
            prov: province,
            city,
            area: district,
            address,
          } = res.result || {};
          resolve({
            name,
            province,
            city,
            district,
            address,
            mobile,
          });
        },
        fail: reject,
      });
    }
  });
};

/**
 * @description 格式化地址请求参数
 * @param type person | company
 * @param addressType send | receive
 * @param customer_id 大客户id
 * @param {Function} query 获取搜索参数
 *  */
export const formatReqParams = ({ type, addressType, customer_id }) => {
  let reqParams = {};
  if (!type || !addressType) {
    return {};
  }
  // 大客户请求参数
  if (type === 'company') {
    reqParams = {
      extra_info: { type: fixType(addressType) },
      customer_id,
    };
  } else {
    reqParams = {
      address_type: fixType(addressType),
      page_per: 30,
    };
  }
  return reqParams;
};

/**
 * @description 兼容微快递原有搜索参数和大客户搜索参数
 *  */
export const formatSearchParams = (searchValue = {}, type) => {
  const params = { ...searchValue };
  const { search, extra_info = {} } = params;
  const isPhone = check('phone', search).code == 0;
  if (type === 'company') {
    if (search) {
      extra_info.mobile = isPhone ? search : undefined;
      extra_info.name = !isPhone ? search : undefined;
    } else {
      extra_info.mobile = undefined;
      extra_info.name = undefined;
    }
  } else {
    //兼容微快递参数
    search && (params.words = search);
  }
  // 删除多余参数
  delete params.search;
  delete params.province;

  return params;
};
// 防抖处理后的
export const getReceiveStorageList = (status = 'reload', data) => {
  return new Promise((resolve, reject) => {
    const list = data || Taro.kbGetGlobalDataOnce(receiveStorageKey);
    if (list) {
      // 如果全局有数据，从全局获取
      resolve(list);
    } else if (status === 'init') {
      // 初次加载才会使用本地缓存数据
      getStorage({
        key: receiveStorageKey,
      })
        .then((res) => {
          const { data: resData } = res.data || {};
          resolve(resData);
        })
        .catch(() => {
          reject();
        });
    }
  });
};
//获取本地缓存的寄件人地址---主要是类似粘贴板那种临时性质的地址
export const getSendStorageData = () => {
  return new Promise((resolve, reject) => {
    getStorage({
      key: sendStorageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data) {
          //使用一次后清空
          removeStorage({ key: sendStorageKey });
          resolve(data);
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};
export const checkLastStorageData = (storageKey) => {
  return new Promise((resolve, reject) => {
    if (!storageKey) reject();
    getStorage({
      key: storageKey,
    })
      .then((res) => {
        const { data } = res.data;
        if (data && data.send_name) {
          resolve();
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};
// 获取默认发件人
export const getDefaultSendAddress = (storageKey) => {
  return new Promise((resolve) => {
    // 默认发件人
    const type = 'send';
    // 如果粘贴板上存在地址，将其设置为默认发件人
    getSendStorageData()
      .then((data) => {
        resolve(data);
      })
      .catch(() => {
        checkLastStorageData(storageKey)
          .then()
          .catch(() => {
            request({
              url: '/api/AddressBook/list',
              data: {
                address_type: fixType(type),
                page_per: 5,
                page: 1,
              },
              toastLoading: false,
              onThen: ({ code, data }) => {
                if (code == 0 && isPlainObject(data[0])) {
                  const { name, tel, mobile, province, city, district, county, address } =
                    data[0] || {};
                  if (!name) return; // 无默认发件人地址时依然响应了空对象
                  resolve({
                    name,
                    mobile: mergeBySpace(mobile, tel),
                    province,
                    city,
                    district: district || county,
                    address,
                  });
                }
              },
            });
          });
      });
  });
};

// 标红
export const labelRedFn = (confidence) => {
  if (!confidence && confidence !== 0) return '';
  return `${confidence < 3 ? 'kb-color__orange' : ''}`;
};

export const getReceiveInfo = () => {
  // 获取收件人信息
  return new Promise((resolve, reject) => {
    getStorage({
      key: receiveStorageKey,
    })
      .then((res) => {
        const { data } = res.data || {};
        resolve(data);
      })
      .catch(reject);
  });
};

/**
 * 检查是否有默认的收件人信息
 *  */
export const checkDefaultReceiver = () => {
  const { data } = getStorageSync(defaultSendStorageKey) || {};
  const { data: receiveList = [] } = getStorageSync('receiveList') || {};
  const {
    receive_address,
    receive_city,
    receive_district,
    receive_mobile,
    receive_name,
    receive_province,
  } = data || {};

  const hasAddress = Boolean(
    receive_address ||
      receive_city ||
      receive_district ||
      receive_mobile ||
      receive_name ||
      receive_province,
  );

  if (receiveList.length > 1) {
    return false;
  }
  return hasAddress;
};
