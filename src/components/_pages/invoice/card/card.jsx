/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo, Fragment } from '@tarojs/taro';
import KbCheckbox from '@base/components/checkbox';
import { View, Text } from '@tarojs/components';
import { dateCalendar } from '@base/utils/utils';
import KbNumber from '@base/components/number';
import classNames from 'classnames';
import './index.scss';

const Index = (props) => {
  const { selected, mode, data, type } = props;
  const isChecked = useMemo(() => {
    if (selected && data) {
      const { orderId, times } = data;
      const selectKey = orderId || times;
      return selected.includes(selectKey);
    }
    return false;
  }, [selected, data]);

  const handleChange = () => {
    if (mode !== 'select') return;
    props.onSelect(data);
  };

  const getSpliceTime = (time) => {
    return time.split('-');
  };

  return (
    <View
      className={classNames('kb-invoice-card kb-list__item--wrapper kb-spacing-lg', {
        'kb-invoice-card__month': type == 'month',
      })}
      onClick={handleChange}
      hoverClass={mode === 'select' ? 'kb-hover-opacity' : 'none'}
    >
      {type == 'month' ? (
        <View className='at-row at-row__align--center'>
          <View className='at-row at-row__align--center at-row__justify--between'>
            <View>
              <View className='kb-size__md'>{getSpliceTime(data.times)[1]}月可开票金额</View>
              <View className='kb-size__sm kb-color__grey'>{getSpliceTime(data.times)[0]}年</View>
            </View>
            <View className='kb-size__lg kb-size__bold'>￥{data.pay_price}</View>
          </View>
          <View className='item-checkbox kb-margin-md-l'>
            <KbCheckbox checked={isChecked} onChange={handleChange} />
          </View>
        </View>
      ) : (
        <Fragment>
          <View className='kb-invoice-card__title kb-spacing-lg-b'>
            <View className='at-row at-row__align--center at-row__justify--between'>
              <Text className='kb-size__md'>
                {dateCalendar(data.dateTime || data.orderTime, { timer: true })}
              </Text>
              <View>
                <Text className='kb-size__sm'>¥</Text>
                <KbNumber number={data.price} fontsize={[40, 24]} />
              </View>
            </View>
          </View>
          <View className='kb-spacing-lg-t at-row at-row__align--center at-row__justify--between'>
            <View className='kb-invoice-card__info'>
              <View className='kb-flex kb-margin-md-b'>
                <View className='kb-invoice-card__label'>运单号</View>
                <View className='kb-invoice-card__value'>{data.waybillNo}</View>
              </View>
              <View className='kb-flex kb-margin-md-b'>
                <View className='kb-invoice-card__label'>发件地址</View>
                <View className='kb-invoice-card__value kb-color__grey'>{data.sendAddress}</View>
              </View>
              <View className='kb-flex kb-margin-md-b'>
                <View className='kb-invoice-card__label'>收件地址</View>
                <View className='kb-invoice-card__value kb-color__grey'>
                  {data.receiverAddress}
                </View>
              </View>
            </View>
            {mode === 'select' && (
              <View className='item-checkbox'>
                <KbCheckbox checked={isChecked} onChange={handleChange} />
              </View>
            )}
          </View>
        </Fragment>
      )}
    </View>
  );
};
Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  mode: 'select',
  onSelect: () => {},
  data: {},
  selected: [],
};
export default Index;
