/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbList from '@base/components/long-list';
import { View, Text } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import { dateCalendar, noop } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import { findKey } from '../_utils';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    active: false,
    mode: '', //yhj商家优惠寄
    onReady: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.state = {
      list: [],
    };
    const { mode } = this.props;
    this.listData = {
      api: {
        url: '/api/Invoice/historyInvoice',
        data: { limit: 50, is_yhj: mode == 'yhj' ? 1 : 0 },
        formatResponse: (res) => {
          const { data: list } = res;
          if (isArray(list) && list.length > 0) {
            return {
              data: { list },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  // 跳转详情页
  handleNavToDetail = (item) => {
    const { invoiceId: id } = item;
    Taro.navigator({
      url: 'invoice/detail',
      options: {
        id,
      },
    });
  };

  handleReady = (ins) => this.props.onReady(ins);

  // 打开发票预览
  handleOpenPdf = (item) => {
    const { gdPdfUrl: url } = item;
    if (!url) return;
    const toastIns = Taro.kbToast({ status: 'loading' });
    Taro.downloadFile({
      url,
    })
      .then(({ tempFilePath }) => {
        toastIns.close();
        Taro.openDocument({
          filePath: tempFilePath,
          success: () => {
            console.log('打开文档成功');
          },
        });
      })
      .catch(({ errorMessage, message = errorMessage }) => {
        toastIns.update({ text: message });
      });
  };

  render() {
    const { active } = this.props;
    const { list } = this.state;
    const statusIcons = {
      clock: '申请中',
      close: '开票失败',
      chenggong: '开票成功',
    };
    const typeMap = {
      normal: '普通',
      special: '专用',
    };
    return (
      <KbList
        noDataText='呀，空空如也～'
        data={this.listData}
        enableMore
        onReady={this.handleReady}
        active={active}
      >
        <View className='kb-list'>
          {list.map((item) => (
            <View key={item.id} className='kb-list__item--wrapper'>
              <View className='kb-list__item' hoverClass='kb-hover'>
                <View className='item-content'>
                  <View onClick={this.handleNavToDetail.bind(this, item)}>
                    <View className='at-row at-row__align--center at-row__justify--between kb-color__grey'>
                      <Text>{dateCalendar(item.createAt, { timer: true })}</Text>
                      <View className='kb-invoice-history__tag'>
                        {typeMap[item.invoice_type]}-电子发票
                      </View>
                      <View className='at-col kb-invoice-history__money'>￥ {item.totalPrice}</View>
                    </View>
                    <View className='at-row at-row__align--center kb-invoice-history__title'>
                      <Text>{item.title}</Text>
                    </View>
                  </View>
                  <View className='at-row at-row__align--center kb-invoice-history__status'>
                    <View
                      className={`icon kb-invoice-history__status__${findKey(
                        statusIcons,
                        item.status,
                      )}`}
                    >
                      <AtIcon
                        className='kb-icon-size__base'
                        prefixClass='kb-icon'
                        value={findKey(statusIcons, item.status)}
                      />
                    </View>
                    <View className='kb-color__grey kb-size__base'>{item.status}</View>
                    {item.gdPdfUrl && (
                      <View hoverStopPropagation>
                        <AtButton
                          type='primary'
                          size='small'
                          circle
                          className='kb-button__mini'
                          onClick={this.handleOpenPdf.bind(this, item)}
                        >
                          预览
                        </AtButton>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </KbList>
    );
  }
}

export default Index;
