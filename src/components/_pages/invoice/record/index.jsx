/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbList from '@base/components/long-list';
import KbCheckboxAll from '@base/components/checkbox/all';
import KbListCard from '@/components/_pages/invoice/card/card';
import { View, Text } from '@tarojs/components';
import isArray from 'lodash/isArray';
import { noop, decimalLen } from '@base/utils/utils';
import classNames from 'classnames';
import './index.scss';

class Index extends Component {
  static defaultProps = {
    selectedAll: false,
    active: false,
    onReady: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.state = {
      money: 0,
      list: [],
      selected: [],
      monthLabel: '',
    };
    const { type } = this.props;
    this.listData = {
      api: {
        url:
          type == 'month' ? '/api/Invoice/getInvoiceOrdersMonth' : '/api/Invoice/getInvoiceOrders',
        data: { limit: 50 },
        formatResponse: (res) => {
          const { data: list } = res;
          if (isArray(list) && list.length > 0) {
            return {
              data: { list },
            };
          } else {
            return {
              data: void 0,
            };
          }
        },
        onThen: (list, _, req) => {
          this.setState({
            list,
          });
          if (req.page === 1) {
            // 刷新页面，清空选择
            this.setState({
              selected: [],
            });
          }
        },
      },
    };
  }

  handleReady = (ins) => this.props.onReady(ins);

  // 计算金额
  countMoney = (selected = []) => {
    const { list } = this.state;
    let money = selected
      .map((key) => {
        const { price, pay_price } =
          list.find((item) => {
            const { orderId, times } = item || {};
            const selectKey = orderId || times;
            return selectKey === key;
          }) || {};
        return 1 * (price || pay_price || 0);
      })
      .reduce((total, cur) => total + cur, 0);
    if (decimalLen(money) > 2) {
      money = Math.floor(money * 100) / 100;
    }
    this.setState({ money });
  };

  countMonth = (selected = []) => {
    let months = selected
      .map((times) => {
        return times && times.split('-')[1];
      })
      .join(',');

    this.setState({
      monthLabel: months,
    });
  };

  handelSelected = (item) => {
    const { type } = this.props;
    const { orderId, times } = item;
    const selectKey = orderId || times;
    const { selected } = this.state;
    const index = selected.findIndex((item) => item === selectKey);
    if (index >= 0) {
      selected.splice(index, 1);
    } else {
      selected.push(selectKey);
    }
    this.setState({ selected: [...selected] });
    this.countMoney(selected);
    if (type == 'month') {
      this.countMonth(selected);
    }
  };

  // 开票下一步
  handleNext = () => {
    const { type, is_remark } = this.props;
    const { selected, money, list } = this.state;
    if (selected.length > 0) {
      if (is_remark) {
        const count = selected.reduce((total, cur) => {
          return total + +cur.cnt;
        }, 0);
        if ((type == 'order' && selected.length > 5) || (type == 'month' && count > 10)) {
          return Taro.kbToast({ text: '超出批量开票并【备注】运单号上限条数！' });
        }
      }

      const options = {
        money,
        list: selected.map((selectedOrderId) => {
          const { orderId, brandType } = list.find(
            (item) => (item.orderId || item.times) === selectedOrderId,
          );
          return { orderId, brandType };
        }),
      };

      if (type == 'month') {
        options.is_month = type;
        options.params = selected.map((key) => {
          const { times } = list.find((item) => item.times === key);
          return times;
        });
      }

      options.is_remark = +is_remark;

      Taro.navigator({
        url: 'invoice/edit',
        key: 'invoiceInfo',
        options,
        onArrived: () => {
          console.log('通过postMessage传递大量数据');
        },
      });
    }
  };
  handleCheckboxAllChange = () => {
    const { type } = this.props;
    const { list, selected: oldSelected } = this.state;
    let selected = [];
    if (list.length !== oldSelected.length) {
      selected = list.map((item) => item.orderId || item.times);
    }
    this.setState({ selected });
    this.countMoney(selected);
    if (type == 'month') {
      this.countMonth(selected);
    }
  };

  render() {
    const { active, type } = this.props;
    const { money, list, selected, monthLabel } = this.state;
    const isMonthType = type == 'month';
    const checkBoxLabel = isMonthType ? '已勾选' : '';

    return (
      <KbList
        noDataText='呀，空空如也～'
        enableMore={type !== 'month'}
        data={this.listData}
        onReady={this.handleReady}
        active={active}
      >
        <View
          className={classNames('kb-list', {
            'kb-list__month': isMonthType,
          })}
        >
          {list.map((item) => (
            <KbListCard
              type={type}
              data={item}
              onSelect={this.handelSelected}
              key={item.orderId || item.times}
              selected={selected}
            />
          ))}
        </View>
        <View className='kb-invoice-record__checkbox'>
          <View className='kb-invoice-record__checkbox--fixed'>
            <KbCheckboxAll
              confirmText='下一步'
              total={list.length}
              count={selected.length}
              onConfirm={this.handleNext}
              onChange={this.handleCheckboxAllChange}
              label={selected.length ? checkBoxLabel : '全选'}
              renderLabel={
                <Fragment>
                  {selected.length ? (
                    <View className='kb-size__base'>
                      {isMonthType ? <Text className='kb-color__brand'>{monthLabel}月</Text> : ''}
                      <Text>订单，共</Text>
                      <Text className='kb-color__brand'>{money}</Text>
                      <Text>元</Text>
                    </View>
                  ) : null}
                </Fragment>
              }
            />
          </View>
        </View>
      </KbList>
    );
  }
}

export default Index;
