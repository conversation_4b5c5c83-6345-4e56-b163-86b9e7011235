

/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import { View } from "@tarojs/components";
import { useEffect, useState } from "@tarojs/taro";
import { loadBC } from "./es";

const Index = props => {
  const [loaded, updateLoaded] = useState(false);
  const { data } = props;

  const handleError = e => console.log("e", e);

  useEffect(() => {
    loadBC()
      .then(({ success }) => updateLoaded(success))
      .catch(err => console.info("err", err));
  }, []);

  return (
    <View>
      {loaded &&
        data.map(item => (
          <bc-module
            code={item.code}
            name={item.name}
            onError={handleError}
            ext={{
              type: item.type,
              pluginOptions: item.pluginOptions,
              loadMoreTime: item.loadMoreTime
            }}
          ></bc-module>
        ))}
    </View>
  );
};

Index.config = {
  usingComponents: {
    "bc-module": "./es/bc-module/bc-module"
  }
};

Index.defaultProps = {
  data: [
    {
      code: "suite://bc.suite.materiel/bc.template.materiel",
      name: "block-materiel",
      pluginOptions: { relationId: "2717650397" },
      loadMoreTime: 0
    }
  ]
};

export default Index;
