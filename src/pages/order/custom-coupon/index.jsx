/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { Image, ScrollView, View } from '@tarojs/components';
import KbPage from '~base/components/page';
import KbLoader from '@base/components/loader';
import KbEmpty from '@base/components/empty';
import {
  addReceiveCouponService,
  getAllCouponConfigService,
} from '~/components/_pages/activity/_utils';
import { debounce } from '~base/utils/utils';
import { apply_brand_type_map, createApplyUserMsg } from '~/components/_pages/order/card/_utils';
import { getEditPageLunchParams } from '~/components/_pages/order/_utils/order.edit';
import dayjs from 'dayjs';
import classNames from 'classnames';
import './index.scss';

class Index extends Component {
  constructor(props) {
    super(props);
    const { activity, coupon_id } = this.$router.params;
    this.state = {
      activity,
      coupon_id,
      apply_brand_type_map,
      expiration_time_map: {
        hour: '小时',
        minute: '分钟',
        days: '天',
      },
      loading: true,
    };
    this.handleAdd = debounce(this.handleAdd, 1000);
  }

  componentDidShow = async () => {
    // 解决部分机型重载问题
    if (this.coupon_params && this.coupon_params.coupon_id) {
      const { activity, coupon_id } = await getEditPageLunchParams(this, { ignore404: true });
      this.next_coupon_params = {
        activity,
        coupon_id,
      };
      this.initPage(this.next_coupon_params);
    }
  };

  onUpdate = async (data) => {
    const { logined } = data;
    if (logined) {
      this.initPage();
    }
  };

  initPage = async (next_coupon_params) => {
    if (next_coupon_params) {
      this.coupon_params = next_coupon_params;
    } else {
      const { activity, coupon_id } = await getEditPageLunchParams(this, { ignore404: true });
      this.coupon_params = {
        activity,
        coupon_id,
      };
    }
    this.setState(this.coupon_params);
    this.getCouponConfig();
  };

  getCouponConfig = () => {
    const { activity, coupon_id } = this.coupon_params || this.state;
    const { apply_brand_type_map } = this.state;
    if (!coupon_id || !activity) return;
    this.setState({
      loading: true,
    });
    getAllCouponConfigService({ activity, coupon_id }).then((res) => {
      this.setState({
        loading: false,
      });
      if (res.data && res.data.length > 0) {
        const couponDetail = res.data[0] || {};
        const { apply_brand_type, rate_type, min_freight } = couponDetail;
        const apply_brand_type_name = apply_brand_type_map[apply_brand_type] || '';
        this.setState({
          couponDetail: {
            ...couponDetail,
            rate_type:
              rate_type === 'discount' ? 'discount' : min_freight > 0 ? 'subtract' : 'zhijian',
            apply_brand_type_name,
          },
        });
      } else if (res.code != 0) {
        this.setState({
          msg: res.msg,
        });
      }
    });
  };

  handleAdd = () => {
    const { activity, coupon_id } = this.state;
    if (!coupon_id || !activity) return;
    addReceiveCouponService({ activity, coupon_id }).then((res) => {
      if (res.code == 0) {
        Taro.kbToast({
          text: '领取成功',
          onClose: () => {
            Taro.navigator({
              url: 'order/card',
            });
          },
        });
      }
    });
  };

  render() {
    const { couponDetail = {}, expiration_time_map, loading, msg, ...rest } = this.state;
    const {
      apply_brand_type_name,
      rate,
      rate_type,
      expiration_type,
      expiration_time,
      coupon_img,
      min_freight,
      apply_user,
      apply_brand_type,
    } = couponDetail || {};
    const applyUserMsg = createApplyUserMsg(apply_user);
    const apply_brand_type_msg = apply_brand_type_map[apply_brand_type] || '';
    const isManJian = !!(rate_type === 'subtract');
    const isZhiJian = !!(rate_type === 'zhijian');
    const isZheKou = !!(rate_type === 'discount');

    const rootCls = classNames('kb-customCoupon', {
      'kb-customCoupon-bgColor': !!coupon_img,
    });
    return (
      <KbPage className={rootCls} onUpdate={this.onUpdate} {...rest}>
        <ScrollView className='kb-customCoupon-scrollview' scrollY>
          {msg ? (
            <KbEmpty centered description={msg} />
          ) : loading ? (
            <KbLoader centered />
          ) : (
            <View className='kb-customCoupon-container'>
              <View className='kb-customCoupon-bg'>
                <Image className='img' mode='widthFix' src={coupon_img} />
              </View>
              <View className='kb-customCoupon-coupon'>
                <View className='kb-customCoupon-coupon__tag'>{apply_brand_type_name}</View>
                <View className='kb-customCoupon-coupon__reward'>
                  <View className='value'>{rate}</View>
                  <View className='unit'>{isZheKou ? '折' : '元'}</View>
                </View>
                <View className='kb-customCoupon-coupon__type'>
                  {isManJian ? '满减券' : isZhiJian ? '直减券' : isZheKou ? '折扣券' : ''}
                  {isManJian ? `-满${min_freight}可用` : null}
                </View>
                <View className='kb-customCoupon-coupon__time'>
                  有效期截止：
                  {expiration_type === 'time'
                    ? `${dayjs(expiration_time).format('YYYY-MM-DD')}有效`
                    : `领取后${expiration_time}${expiration_time_map[expiration_type]}内有效`}
                </View>
                <View
                  className='kb-customCoupon-coupon__btn'
                  onClick={this.handleAdd}
                  hoverClass='kb-hover-opacity'
                >
                  立即领取
                </View>
                <View className='kb-customCoupon-coupon__apply'>
                  限指定团队：&quot;{apply_brand_type_msg}&quot; {applyUserMsg}身份寄件使用
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </KbPage>
    );
  }
}

export default Index;
