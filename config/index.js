/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const path = require('path');
const config = {
  projectName: 'miniapp_qj',
  date: '2019-12-20',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  sourceRoot: 'src',
  // babel、csso、uglify 等配置从 plugins 配置中移出来
  babel: {
    sourceMap: false,
    presets: [['env', { modules: false }]],
    plugins: [
      'transform-decorators-legacy',
      'transform-class-properties',
      'transform-object-rest-spread',
      [
        'transform-runtime',
        {
          helpers: false,
          polyfill: false,
          regenerator: true,
          moduleName: 'babel-runtime',
        },
      ],
    ],
  },
  plugins: ['@tarojs/plugin-sass', '@tarojs/plugin-terser', 'taro-plugin-build', 'taro-plugin-sub'],
  // 配置混淆插件，打包自动移除console相关代码
  terser: {
    enable: true,
    config: {
      compress: {
        drop_console: process.env.DEBUG_ENV !== 'OPEN',
      },
    },
  },
  // 小程序配置从 weapp 改为 mini，可以删掉很多小配置
  mini: {
    webpackChain(chain) {
      // 包大小分析
      // chain
      //   .plugin("analyzer")
      //   .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin, []);
    },
    imageUrlLoaderOption: {
      limit: 0, // 大小限制，单位为 b
    },
    cssLoaderOption: {},
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: 10240, // 设定转换尺寸上限
        },
      },
    },
  },
  // 可以删掉很多小配置
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    webpackChain(chain, webpack) {},
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
          browsers: ['last 3 versions', 'Android >= 4.1', 'ios >= 8'],
        },
      },
    },
  },
  sass: {
    resource: [`src/styles/color.scss`, 'src/styles/variable.scss', 'src/styles/mixins.scss'],
    projectDirectory: path.resolve(__dirname, '..'),
  },
  alias: {
    '~': path.resolve('src'),
    '~base': path.resolve('src/lib/modules_base'),
    '@': path.resolve('src'),
    '@base': path.resolve('src/lib/modules_base'),
  },
};

module.exports = function (merge) {
  const {
    MODE_ENV,
    NODE_ENV,
    TARO_ENV,
    ROOT_PATH = `dist/${TARO_ENV}.${NODE_ENV === 'development' ? 'dev' : 'build'}`,
  } = process.env;
  let mergeConfig = {};
  if (NODE_ENV === 'development') {
    mergeConfig = merge({}, config, require('./dev'));
  } else {
    mergeConfig = merge({}, config, require('./prod'));
  }

  mergeConfig.outputRoot = ROOT_PATH; //-alipay：支付宝 | -weapp：微信
  mergeConfig.env.MODE_ENV = MODE_ENV || 'qj';

  // 微快递微信版
  mergeConfig.copy = {
    patterns: [
      {
        from: `src/lib/config/config.request/crypto.weapp.qj.wasm`,
        to: `${mergeConfig.outputRoot}/crypto.wasm`,
      }, // 指定需要 copy 的文件
    ],
  };

  if (TARO_ENV === 'weapp') {
    mergeConfig.copy.patterns.push({
      from: `src/lib/config/config.request/crypto.websocket.wasm`,
      to: `${mergeConfig.outputRoot}/crypto.websocket.wasm`,
    });
  }

  return mergeConfig;
};
